import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: true,
  env: {
    NEXT_PUBLIC_APP_ENV: process.env.NEXT_PUBLIC_APP_ENV || "development",
  },
  async redirects() {
    return [
      {
        source: "/ingredients",
        destination: "/ingredients/industry-insights", // Redirect to the first item in the menu
        permanent: true,
      },
      {
        source: "/menu-items",
        destination: "/menu-items/industry-insights", // Redirect to the first item in the menu
        permanent: true,
      },
      {
        source: "/retail",
        destination: "/retail/industry-insights", // Redirect to the first item in the menu
        permanent: true,
      },
    ];
  },
  images: {
    unoptimized: false,
    formats: ["image/avif", "image/webp"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**", //todo: remove
      },
    ],
  },
};

export default nextConfig;
