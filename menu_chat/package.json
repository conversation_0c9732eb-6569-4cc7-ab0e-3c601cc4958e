{"name": "menudata", "engines": {"node": "^22.x"}, "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "dev-debug": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@nivo/geo": "^0.94.0", "@popperjs/core": "^2.11.8", "@tanstack/react-query": "^5.72.2", "@tanstack/react-table": "^8.21.2", "@types/voca": "1.4.6", "@visx/geo": "^3.12.0", "axios": "^1.8.4", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "d3-geo": "^2.0.2", "geo-albers-usa-territories": "^0.1.0", "motion": "^12.6.2", "next": "15.2.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-tagcloud": "^2.3.3", "react-tooltip": "^5.28.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.2", "topojson-client": "^3.1.0", "voca": "1.4.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@rails/actioncable": "^8.0.200", "@tailwindcss/postcss": "^4", "@types/actioncable": "^5.2.11", "@types/next-auth": "^3.15.0", "@types/node": "^20", "@types/rails__actioncable": "^6.1.11", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-tagcloud": "^2.3.2", "@types/topojson-client": "^3.1.5", "eslint": "^9", "eslint-config-next": "15.2.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}, "overrides": {"@visx/geo": {"react": "^19.0.0", "react-dom": "^19.0.0"}}}