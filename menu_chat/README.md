## MenuData

Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Getting Started
Node Version. Please use updated Node Version; Recommended v23.9

Install NPM modules:
```bash
npm install
```

Set ENV variables mentioned in `.env.template`

Run development server:
```bash
# to start server
npm run dev


# if you are debugging with backend Rails app; run on a different port 
# runs development on port 3001 - allows simultaneous development of Rails api on port 3000
npm run dev2 
```

Open [http://localhost:3001](http://localhost:3000) with your browser to see the result.

## Pull Requests

When issuing PRs, please send PR to `staging_ci` branch. We will be using this branch first to test before deploying to production. 

Please verify your app builds before issuing PRs. We need to build the app without errors to deploy.
```bash
npm run build

> menudata@0.1.0 build
> next build

(node:144668) Warning: Setting the NODE_TLS_REJECT_UNAUTHORIZED environment variable to '0' makes TLS connections and HTTPS requests insecure by disabling certificate verification.
(Use `node --trace-warnings ...` to show where the warning was created)
   ▲ Next.js 15.2.3
   - Environments: .env

 ✓ Linting and checking validity of types    
   Creating an optimized production build ...
 ✓ Compiled successfully
 ✓ Collecting page data    

```




## Best Practices

This app follows [Pages Router](https://nextjs.org/docs/pages)

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

## Libraries Used in this App
- [NextAuth.js](https://next-auth.js.org/getting-started/introduction) - used for Authentication
- [React Context API](https://legacy.reactjs.org/docs/context.html) - used for State Management
- [TanStack Query](https://tanstack.com/query/latest/docs/framework/react/overview) - Used for API Handling