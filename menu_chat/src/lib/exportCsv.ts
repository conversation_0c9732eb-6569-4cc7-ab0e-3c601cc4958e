import { Table as TanStackTable } from "@tanstack/react-table";

export const exportTableToCSV = <T>(
  table: TanStackTable<T> | undefined,
  tableName = "export"
) => {
  if (!table) return;

  const visibleRows = table.getRowModel().rows;
  const columns = table.getVisibleFlatColumns();
  const data = visibleRows.map((row) => row.original);

  const csv = [
    columns.map((col) => col.columnDef?.header || col.id).join(","),
    ...data.map((row) =>
      columns.map((col) => `"${(row as Record<string, unknown>)[col.id] ?? ""}"`).join(",")
    ),
  ].join("\n");

  const blob = new Blob(["\uFEFF" + csv], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", `${tableName}.csv`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};