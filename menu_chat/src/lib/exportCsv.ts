import { Table as TanStackTable } from "@tanstack/react-table";

const downloadCSV = (csv: string, filename: string) => {
  const blob = new Blob(["\uFEFF" + csv], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const exportTableToCSV = <T>(
  table: TanStackTable<T> | undefined,
  tableName = "export"
) => {
  if (!table) return;

  const visibleRows = table.getRowModel().rows;
  const columns = table.getVisibleFlatColumns();
  const data = visibleRows.map((row) => row.original);

  const csv = [
    columns.map((col) => col.columnDef?.header || col.id).join(","),
    ...data.map((row) =>
      columns.map((col) => `"${(row as Record<string, unknown>)[col.id] ?? ""}"`).join(",")
    ),
  ].join("\n");

  downloadCSV(csv, `${tableName}.csv`)
};

export const exportToCSV = (filename: string, data: object[]) => {
  if (!data.length) return;

  const csv = [
    Object.keys(data[0]).join(","),
    ...data.map((row) =>
      Object.values(row)
        .map((v) => `"${String(v).replace(/"/g, '""')}"`)
        .join(",")
    ),
  ].join("\n");

  downloadCSV(csv, `${filename}`)
};