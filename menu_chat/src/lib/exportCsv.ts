import { Table as TanStackTable, Column } from "@tanstack/react-table";

const downloadCSV = (csv: string, filename: string) => {
  const blob = new Blob(["\uFEFF" + csv], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const exportTableToCSV = <T>(
  table: TanStackTable<T> | undefined,
  tableName = "export",
) => {
  if (!table) return;

  const visibleRows = table.getPaginationRowModel().rows;
  const columns = table.getVisibleFlatColumns() as Column<T, unknown>[];

  const exportable = columns
    .map((col) => {
      const header = col.columnDef?.header;
      const isFavorite = String(col.id) === "favorite";
      const headerText = isFavorite
        ? "favorite"
        : typeof header === "string"
          ? header
          : String(col.id ?? "");
      return { col, headerText, isFavorite };
    })
    .filter(({ headerText }) => headerText.trim().length > 0);

  const escape = (value: unknown) => String(value ?? "").replace(/"/g, '""');

  const csv = [
    exportable.map(({ headerText }) => headerText).join(","),
    ...visibleRows.map((row) =>
      exportable
        .map(({ col, isFavorite }) => {
          const raw = isFavorite ? "" : row.getValue(col.id as string);
          return `"${escape(raw)}"`;
        })
        .join(","),
    ),
  ].join("\n");

  downloadCSV(csv, `${tableName}.csv`);
};

export const exportToCSV = (filename: string, data: object[]) => {
  if (!data.length) return;

  const csv = [
    Object.keys(data[0]).join(","),
    ...data.map((row) =>
      Object.values(row)
        .map((v) => `"${String(v).replace(/"/g, '""')}"`)
        .join(",")
    ),
  ].join("\n");

  downloadCSV(csv, `${filename}`)
};
