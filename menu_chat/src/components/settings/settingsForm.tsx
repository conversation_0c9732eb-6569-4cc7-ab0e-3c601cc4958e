import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Input from "@/components/base/input";
import Textarea from "@/components/base/textarea";
import Button from "@/components/base/button";
import { IconEdit2 } from "@/components/icons";

const SettingsForm = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [customer, setCustomer] = useState("McDonald's");
  const [accountHolder, setAccountHolder] = useState("Michael Prewet");
  const [email, setEmail] = useState("<EMAIL>");
  const [companyProfile, setCompanyProfile] = useState(
    "McDonald’s leverages data analytics to enhance customer experience and operational efficiency in the food and beverage industry. From optimizing food delivery performance to using predictive analytics for targeted marketing campaigns, McDonald’s employs advanced tools to improve sales, streamline processes, and boost customer satisfaction",
  );

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    switch (name) {
      case "customer":
        setCustomer(value);
        break;
      case "accountHolder":
        setAccountHolder(value);
        break;
      case "email":
        setEmail(value);
        break;
      case "companyProfile":
        setCompanyProfile(value);
        break;
      default:
        break;
    }
  };

  return (
    <div className="mt-5">
      <div className="border-muted-100 mx-auto max-w-[735px] rounded-md border bg-white p-4">
        <Heading level={4} className="mb-4">
          Account Information
        </Heading>
        <div className="flex flex-col items-start gap-4">
          <div className="relative w-full">
            <label
              className={twMerge(
                "pointer-events-none absolute top-2 left-4 z-10 text-xs font-semibold text-neutral-400 transition-colors",
                customer && "text-neutral-700",
              )}
            >
              Customer
            </label>
            <Input
              value={customer}
              onChange={handleChange}
              name="customer"
              inputSize="lg"
              type="text"
              placeholder="Type in customer name"
              disabled={!isEditing}
              inputClassName={twMerge(
                "bg-muted-500/15 pt-5.5 pb-2 pr-8 h-auto disabled:bg-muted-100 disabled:border-muted-100 disabled:opacity-100 disabled:text-[#93918A]",
                customer && "bg-white",
              )}
            />
          </div>
          <div className="relative w-full">
            <label
              className={twMerge(
                "pointer-events-none absolute top-2 left-4 z-10 text-xs font-semibold text-neutral-400 transition-colors",
                accountHolder && "text-neutral-700",
              )}
            >
              Account Holder
            </label>
            <Input
              value={accountHolder}
              onChange={handleChange}
              name="accountHolder"
              inputSize="lg"
              type="text"
              placeholder="Type in account holder"
              disabled={!isEditing}
              inputClassName={twMerge(
                "bg-muted-500/15 pt-5.5 pb-2 pr-8 h-auto disabled:bg-muted-100 disabled:border-muted-100 disabled:opacity-100 disabled:text-[#93918A]",
                accountHolder && "bg-white",
              )}
            />
          </div>
          <div className="relative w-full">
            <label
              className={twMerge(
                "pointer-events-none absolute top-2 left-4 z-10 text-xs font-semibold text-neutral-400 transition-colors",
                email && "text-neutral-700",
              )}
            >
              Email
            </label>
            <Input
              value={email}
              onChange={handleChange}
              name="email"
              inputSize="lg"
              type="email"
              placeholder="Type in email"
              disabled={!isEditing}
              inputClassName={twMerge(
                "bg-muted-500/15 pt-5.5 pb-2 pr-8 h-auto disabled:bg-muted-100 disabled:border-muted-100 disabled:opacity-100 disabled:text-[#93918A]",
                email && "bg-white",
              )}
            />
          </div>
          <div className="relative w-full">
            <label
              className={twMerge(
                "pointer-events-none absolute top-2 left-4 z-10 text-xs font-semibold text-neutral-400 transition-colors",
                companyProfile && "text-neutral-700",
              )}
            >
              Company Profile
            </label>
            <Textarea
              value={companyProfile}
              onChange={handleChange}
              name="companyProfile"
              placeholder="Type in company profile"
              disabled={!isEditing}
              className={twMerge(
                "bg-muted-500/15 disabled:bg-muted-100 disabled:border-muted-100 min-h-52 pt-5.5 pr-8 pb-2 disabled:text-[#93918A] disabled:opacity-100",
                companyProfile && "bg-white",
              )}
            />
          </div>
          <div className="flex w-full items-center justify-between">
            {isEditing ? (
              <Button
                variant="primary"
                size="sm"
                onClick={() => setIsEditing(false)}
              >
                Save
              </Button>
            ) : (
              <Button
                variant="tertiary"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <IconEdit2 size={20} />
                Edit
              </Button>
            )}
            {isEditing && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsEditing(false)}
              >
                Cancel
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsForm;
