import { twMerge } from "tailwind-merge";

interface TableProps extends React.TableHTMLAttributes<HTMLTableElement> {
  children: React.ReactNode;
  className?: string;
}

interface TableBodyProps {
  children: React.ReactNode;
  withBorder?: boolean;
}

interface TableSectionProps {
  children: React.ReactNode;
  className?: string;
}

interface TableCellProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
  onClick?: () => void;
}

// Main Table Wrapper
const Table = ({ children, className, ...props }: TableProps) => {
  return (
    <div className={twMerge("w-full overflow-x-auto", className)}>
      <table
        className="w-full border-separate border-spacing-0 text-left"
        {...props}
      >
        {children}
      </table>
    </div>
  );
};

// Table Head
const Thead = ({ children }: TableSectionProps) => {
  return <thead>{children}</thead>;
};

// Table Body
const Tbody = ({ children, withBorder }: TableBodyProps) => {
  return <tbody className={withBorder ? "with-border" : ""}>{children}</tbody>;
};

// Table Row
const Tr = ({ children, className }: TableSectionProps) => {
  return (
    <tr className={twMerge("group/tr hover:cursor-pointer", className)}>
      {children}
    </tr>
  );
};

// Table Header Cell
const Th = ({ children, title, onClick }: TableCellProps) => {
  return (
    <th className="group/th pb-1" title={title} onClick={onClick}>
      <span className="bg-muted-500/10 font-archivo flex h-6 items-center px-2 py-1 text-[11px] leading-[1.6] font-semibold tracking-[0.2px] text-neutral-600 uppercase group-first/th:rounded-l-lg group-last/th:justify-end group-last/th:rounded-r-lg">
        {children}
      </span>
    </th>
  );
};

// Table Data Cell
const Td = ({ children, className }: TableCellProps) => {
  return (
    <td
      className={twMerge(
        "group-hover/tr:bg-muted-500/10 border-muted-100 border-b px-2 py-3 text-sm font-medium text-neutral-900 transition-colors last:text-right",
        className,
      )}
    >
      {children}
    </td>
  );
};

export default Table;
export { Thead, Tbody, Tr, Th, Td };
