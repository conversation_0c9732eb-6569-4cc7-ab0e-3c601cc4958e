import { twMerge } from "tailwind-merge";
import React, { ReactNode } from "react";
import {
  flexRender,
  // ColumnDef,
  RowData,
  Table as TanStackTable,
} from "@tanstack/react-table";

interface TableProps extends React.TableHTMLAttributes<HTMLTableElement> {
  children: React.ReactNode;
  className?: string;
}

interface TableBodyProps {
  children: React.ReactNode;
  withBorder?: boolean;
}

interface TableSectionProps {
  children: React.ReactNode;
}

interface TableCellProps {
  children: React.ReactNode;
  colSpan?: number;
}

// Enhanced TanStack Table support
interface EnhancedTableProps<T extends RowData> {
  table: TanStackTable<T>;
  className?: string;
  withBorder?: boolean;
  enableResizing?: boolean;
  isLoading?: boolean;
  emptyState?: ReactNode;
}

// Main Table Wrapper
const Table = ({ children, className, ...props }: TableProps) => {
  return (
    <div className={twMerge("w-full overflow-x-auto", className)}>
      <table
        className="w-full border-separate border-spacing-0 text-left"
        {...props}
      >
        {children}
      </table>
    </div>
  );
};

// Table Head
const Thead = ({ children }: TableSectionProps) => {
  return <thead>{children}</thead>;
};

// Table Body
const Tbody = ({ children, withBorder }: TableBodyProps) => {
  return <tbody className={withBorder ? "with-border" : ""}>{children}</tbody>;
};

// Table Row
const Tr = ({ children }: TableSectionProps) => {
  return (
    <tr className="hover:bg-muted-500/10 transition-colors hover:cursor-pointer">
      {children}
    </tr>
  );
};

// Table Header Cell
const Th = ({ children }: TableCellProps) => {
  return (
    <th className="group pb-1">
      <span className="bg-muted-500/10 leading=[1.6] flex h-9 items-center px-2 py-1 text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase group-first:rounded-l-lg group-last:rounded-r-lg">
        {children}
      </span>
    </th>
  );
};

// Table Data Cell
const Td = ({ children, colSpan }: TableCellProps) => {
  return (
    <td
      className="border-muted-500/30 border-b px-2 py-3 text-sm font-medium text-neutral-900"
      colSpan={colSpan}
    >
      {children}
    </td>
  );
};

// Add this new component for skeleton rows
const SkeletonRow = ({ columns }: { columns: number }) => {
  return (
    <Tr>
      {Array.from({ length: columns }).map((_, index) => (
        <Td key={index}>
          <div className="flex items-center space-x-2">
            <div className="bg-muted-200 h-4 w-full animate-pulse rounded" />
          </div>
        </Td>
      ))}
    </Tr>
  );
};

// Enhanced TanStack Table Component
function EnhancedTable<T extends RowData>({
  table,
  className,
  withBorder = true,
  enableResizing = false,
  isLoading = false,
  emptyState,
}: EnhancedTableProps<T>) {
  const isEmpty = (table?.getRowModel?.()?.rows?.length ?? 0) === 0;
  const columnCount = table.getAllColumns().length;

  // Column resizing styles
  const resizeStyles = {
    resizer: {
      position: "absolute" as const,
      right: 0,
      top: 0,
      height: "100%",
      width: "5px",
      background: "rgba(0, 0, 255, 0.5)",
      cursor: "col-resize",
      userSelect: "none" as const,
      touchAction: "none" as const,
      opacity: 0,
    },
    resizerActive: {
      opacity: 1,
    },
  };

  return (
    <div className={twMerge("w-full overflow-x-auto", className)}>
      <table className="w-full border-separate border-spacing-0 text-left">
        <Thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const handleSort = () => {
                  const column = header.column;
                  if (column.getCanSort()) {
                    column.toggleSorting();
                  }
                };

                return (
                  <Th key={header.id}>
                    <div
                      onClick={handleSort}
                      style={{
                        position: "relative",
                        width: enableResizing ? header.getSize() : "auto",
                        cursor: header.column.getCanSort()
                          ? "pointer"
                          : "default",
                        userSelect: "none",
                      }}
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                      <span>
                        {{
                          asc: " 🔼",
                          desc: " 🔽",
                        }[header.column.getIsSorted() as string] ?? ""}
                      </span>

                      {enableResizing && header.column.getCanResize() && (
                        <div
                          onMouseDown={header.getResizeHandler()}
                          onTouchStart={header.getResizeHandler()}
                          style={{
                            ...resizeStyles.resizer,
                            ...(header.column.getIsResizing()
                              ? resizeStyles.resizerActive
                              : {}),
                          }}
                        />
                      )}
                    </div>
                  </Th>
                );
              })}
            </Tr>
          ))}
        </Thead>
        <Tbody withBorder={withBorder}>
          {isLoading ? (
            <>
              {Array.from({ length: 5 }).map((_, index) => (
                <SkeletonRow key={index} columns={columnCount} />
              ))}
            </>
          ) : isEmpty && emptyState ? (
            <Tr>
              <Td colSpan={columnCount}>
                <div className="flex justify-center py-4">{emptyState}</div>
              </Td>
            </Tr>
          ) : (
            table.getRowModel().rows.map((row) => (
              <Tr key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Td key={cell.id}>
                    <div
                      style={{
                        width: enableResizing ? cell.column.getSize() : "auto",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </div>
                  </Td>
                ))}
              </Tr>
            ))
          )}
        </Tbody>
      </table>
    </div>
  );
}

export default Table;
export { Thead, Tbody, Tr, Th, Td, EnhancedTable };
