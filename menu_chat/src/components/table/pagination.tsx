import { twMerge } from "tailwind-merge";
import Paragraph from "@/components/base/paragraph";
import {
  IconArrowUp,
  IconArrowDown,
  IconArrowLeft,
  IconArrowRight,
} from "@/components/icons";
import Button from "@/components/base/button";

interface PaginationInputProps {
  currentPage: number;
  perPage: number;
  totalResults: number;
  onPageChange: (page: number) => void;
  onPerPageChange: (perPage: number) => void;
  className?: string;
}

const Pagination: React.FC<PaginationInputProps> = ({
  currentPage,
  perPage,
  totalResults,
  onPageChange,
  onPerPageChange,
  className,
}) => {
  const handleIncrement = () => {
    const newValue = perPage + 10;
    const cappedValue = Math.min(newValue, totalResults);
    onPerPageChange(cappedValue);

    const maxPage = Math.ceil(totalResults / cappedValue);
    if (currentPage > maxPage) onPageChange(maxPage);
  };

  const handleDecrement = () => {
    const newValue = perPage - 10;
    if (newValue >= 10) {
      onPerPageChange(newValue);

      const maxPage = Math.ceil(totalResults / newValue);
      if (currentPage > maxPage) onPageChange(maxPage);
    }
  };

  const pageStartIndex = (currentPage - 1) * perPage + 1;
  const pageEndIndex = Math.min(currentPage * perPage, totalResults);
  const resultsShown =
    totalResults === 0 ? "No results" : `${pageStartIndex} — ${pageEndIndex}`;

  const disableIncrement = perPage >= totalResults;
  const disableDecrement = perPage <= 10;

  const totalPages = Math.ceil(totalResults / perPage);
  const maxVisible = 4;

  const pageEnd = Math.min(totalPages, Math.max(currentPage, maxVisible));
  const pageStart = Math.max(1, pageEnd - maxVisible + 1);

  const pageNumbers = [];
  for (let i = pageStart; i <= pageEnd; i++) {
    pageNumbers.push(i);
  }

  const goToPreviousPage = () => {
    if (currentPage > 1) onPageChange(currentPage - 1);
  };

  const goToNextPage = () => {
    const maxCurrentPage = Math.ceil(totalResults / perPage);
    if (currentPage < maxCurrentPage) onPageChange(currentPage + 1);
  };

  return (
    <div
      className={twMerge(
        "mt-2 flex items-center justify-between gap-2 p-2",
        className,
      )}
    >
      <div className="flex items-center gap-3">
        <div className="border-muted-100 flex items-center gap-1 rounded-lg border p-1">
          <Button
            size="xs"
            variant="ghost"
            isSquared
            onClick={handleIncrement}
            disabled={disableIncrement}
          >
            <IconArrowUp size={16} className="text-neutral-600" />
          </Button>
          <span className="block size-6 text-center font-medium tracking-[-0.3px]">
            {perPage}
          </span>
          <Button
            size="xs"
            variant="ghost"
            isSquared
            onClick={handleDecrement}
            disabled={disableDecrement}
          >
            <IconArrowDown size={16} className="text-neutral-600" />
          </Button>
        </div>
        <div className="text-sm font-medium tracking-[-0.3px]">
          <Paragraph size="xs" className="text-neutral-700">
            results per page
          </Paragraph>
          <Paragraph
            size="xs"
            className="flex items-center gap-1 text-neutral-600"
          >
            <span>{resultsShown}</span>
            <span className="text-neutral-400">|</span>
            <span>{totalResults}</span>
          </Paragraph>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Button
          size="xs"
          variant="ghost"
          isSquared
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
        >
          <IconArrowLeft size={16} className="text-neutral-600" />
        </Button>
        <div className="flex items-center gap-1">
          {pageNumbers.map((page) => (
            <Button
              key={page}
              size="xs"
              variant="ghost"
              isSquared
              onClick={() => onPageChange(page)}
              className={twMerge(
                "text-yellow-1000",
                currentPage === page && "bg-neutral-100",
              )}
            >
              {page}
            </Button>
          ))}
        </div>
        <Button
          size="xs"
          variant="ghost"
          isSquared
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
        >
          <IconArrowRight size={16} className="text-neutral-600" />
        </Button>
      </div>
    </div>
  );
};

export default Pagination;
