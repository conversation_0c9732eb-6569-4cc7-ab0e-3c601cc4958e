import Button from "@/components/base/button";
import Input from "@/components/base/input";
import { IconDownload, IconClose, IconSearch, IconCircleUp, IconCircleDown } from "@/components/icons";
import FilterDropdown from "@/components/base/filterDropdown";
import SortDropdown from "@/components/base/sortDropdown";
import { Table as TanStackTable } from "@tanstack/react-table";
import { exportTableToCSV } from "@/lib/exportCsv";
import { INGREDIENTS_FILTER } from "@/data/ingredients";

//TODO NEED TO CHANGE ON ENDPOINTS
const RETAIL_PRODUCTS_FILTER = INGREDIENTS_FILTER;

export const RETAIL_PRODUCTS_SORT = [
  { label: "Retailer Name", value: "heading" },
  { label: (<><IconCircleUp size={20} className="text-neutral-500" /> A-Z</>), value: "retailer_name-highest" },
  { label: (<><IconCircleDown size={20} className="text-neutral-500" /> Z-A</>), value: "retailer_name-lowest" },
  { label: "", value: "divider" },
  { label: "Category", value: "heading" },
  { label: (<><IconCircleUp size={20} className="text-neutral-500" /> A-Z</>), value: "category-highest" },
  { label: (<><IconCircleDown size={20} className="text-neutral-500" /> Z-A</>), value: "category-lowest" },
  { label: "", value: "divider" },
  { label: "Price", value: "heading" },
  { label: (<><IconCircleUp size={20} className="text-neutral-500" /> High to Low</>), value: "price-lowest" },
  { label: (<><IconCircleDown size={20} className="text-neutral-500" /> Low to High</>), value: "price-highest" },
  { label: "", value: "divider" },
  { label: "Date Created", value: "heading" },
  { label: (<><IconCircleUp size={20} className="text-neutral-500" /> Newest</>), value: "created_at-lowest" },
  { label: (<><IconCircleDown size={20} className="text-neutral-500" /> Oldest</>), value: "created_at-highest" },
];

interface RetailProductsFiltersProps<T> {
  search: string;
  onSearch: (value: string) => void;
  filters: string[];
  onFilter: (value: string[]) => void;
  sort: string;
  onSort: (value: string) => void;
  table?: TanStackTable<T>;
  tableName?: string;
}

const RetailProductsFilters = <T,>({
                                     search,
                                     onSearch,
                                     filters,
                                     onFilter,
                                     sort,
                                     onSort,
                                     table,
                                     tableName,
                                   }: RetailProductsFiltersProps<T>) => {
  return (
    <>
      <div className="flex items-center justify-between gap-3 pt-3 pb-6">
        <Button variant="primary" size="sm" onClick={() => exportTableToCSV(table, tableName)}>
          <IconDownload size={20} />
          Export
        </Button>
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search by product name"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => onSearch(e.target.value)}
          />
          <FilterDropdown
            selectedValue={filters}
            options={RETAIL_PRODUCTS_FILTER}
            onSelect={onFilter}
          />
          <SortDropdown
            selectedValue={sort}
            options={RETAIL_PRODUCTS_SORT}
            onSelect={(value) => onSort(value)}
          />
        </div>
      </div>
      {filters.length > 0 && (
        <div className="mb-3 flex items-center justify-between gap-2">
          <div className="flex items-center gap-1.5">
            {filters.map((filter) => (
              <span
                key={filter}
                className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
              >
                {filter}
                <button
                  type="button"
                  className="flex size-4 items-center justify-center"
                  onClick={() => onFilter(filters.filter((f) => f !== filter))}
                >
                  <IconClose
                    size={16}
                    className="text-muted-900 cursor-pointer"
                  />
                </button>
              </span>
            ))}
          </div>
          <Button variant="ghost" size="sm" onClick={() => onFilter([])}>
            Reset
          </Button>
        </div>
      )}
    </>
  );
};

export default RetailProductsFilters;