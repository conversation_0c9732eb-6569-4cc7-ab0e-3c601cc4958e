import { twMerge } from "tailwind-merge";
import Input from "@/components/base/input";

interface PaginationInputProps {
  value: number;
  totalResults: number;
  className?: string;
  onChange: (value: number) => void;
}

const PaginationInput: React.FC<PaginationInputProps> = ({
  value,
  totalResults,
  className,
  onChange,
}) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value, 10);
    if (!isNaN(newValue)) {
      onChange(newValue);
    }
  };

  return (
    <div
      className={twMerge(
        "mt-2 flex items-center justify-end gap-2 p-2",
        className,
      )}
    >
      <span className="text-sm font-medium text-neutral-700">showing</span>
      <Input
        value={value}
        onChange={handleInputChange}
        placeholder="page"
        className="w-11"
        inputSize="sm"
        inputClassName="text-center"
        type="number"
        min={1}
        max={totalResults}
        step={1}
      />
      <span className="text-sm font-medium text-neutral-700">
        of <strong>{totalResults}</strong> results
      </span>
    </div>
  );
};

export default PaginationInput;
