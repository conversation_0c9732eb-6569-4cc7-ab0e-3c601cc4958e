import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconEye2 = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M1.99.99a1 1 0 0 0-.697 1.717l3.318 3.318C1.706 8.5.107 11.551.107 11.551a1 1 0 0 0-.002.896S4.238 21 12 21c2.24 0 4.131-.81 5.75-1.836l3.543 3.543a1 1 0 1 0 1.414-1.414l-3.98-3.98a1 1 0 0 0-.168-.168l-2.9-2.9a1.002 1.002 0 0 0-.247-.247l-5.44-5.44a1 1 0 0 0-.187-.185L6.873 5.461a1 1 0 0 0-.184-.186L2.707 1.293A1 1 0 0 0 1.99.99ZM12 3c-.828 0-1.6.117-2.32.287a1 1 0 1 0 .46 1.947A8.022 8.022 0 0 1 12 5c5.877 0 9.373 6.25 9.791 7.016-.181.34-.59 1.176-1.72 2.535a1 1 0 1 0 1.536 1.28c1.486-1.785 2.288-3.382 2.288-3.382a1 1 0 0 0 0-.896S19.762 3 12 3ZM6.064 7.479l1.768 1.767A4.982 4.982 0 0 0 7 12c0 2.75 2.25 5 5 5a4.982 4.982 0 0 0 2.754-.832l1.535 1.535C15.015 18.44 13.624 19 12 19c-5.89 0-9.4-6.29-9.803-7.031.332-.603 1.638-2.617 3.867-4.49ZM9.3 10.713l3.988 3.988A2.985 2.985 0 0 1 9.3 10.713Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconEye2;
