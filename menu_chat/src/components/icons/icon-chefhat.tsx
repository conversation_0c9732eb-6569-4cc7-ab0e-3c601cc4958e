import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconChefHat = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          fill="currentColor"
          d="M17.5 3a4.39 4.39 0 0 0-1.967.463A4.992 4.992 0 0 0 12 2a4.992 4.992 0 0 0-3.533 1.463A4.39 4.39 0 0 0 6.5 3 4.505 4.505 0 0 0 2 7.5c0 1.915 1.241 3.602 3 4.228V21a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-9.272c1.759-.626 3-2.312 3-4.228C22 5.019 19.981 3 17.5 3Zm.357 6.96a1 1 0 0 0-.857.99V20h-2v-2a1 1 0 0 0-2 0v2h-2v-2a1 1 0 0 0-2 0v2H7v-9.05c0-.497-.365-.918-.857-.99A2.504 2.504 0 0 1 4 7.5C4 6.122 5.122 5 6.5 5c.704 0 1.232.304 1.551.559a1 1 0 0 0 1.455-.225 3 3 0 0 1 4.988 0 1.002 1.002 0 0 0 1.455.225A2.479 2.479 0 0 1 17.5 5C18.878 5 20 6.122 20 7.5a2.504 2.504 0 0 1-2.143 2.46Z"
        />
      </svg>
    </span>
  );
};

export default IconChefHat;
