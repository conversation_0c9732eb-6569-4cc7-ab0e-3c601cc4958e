import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconVegetarian = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(`inline-flex items-center justify-center`, className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M20.73.04a1 1 0 0 0-.92 1.39c.16.393.207 1.018.085 1.474v.004l-.002.006a2.105 2.105 0 0 1-.44.822 4.423 4.423 0 0 0-.869-.46 4.23 4.23 0 0 0-2.451-.174l-.006.002-.006.001c-1.425.33-2.428 1.051-3.265 1.48l-.014.007-.012.008c-.424.232-.839.425-1.275.601-.442.175-.91.33-1.4.487-.957.304-2.106.594-3.268 1.205a9.72 9.72 0 0 0-1.604 1.06l-.002.002c-.263.208-.488.417-.69.604l-.015.013-.013.014c-.221.22-.445.457-.657.701l-.01.01-.007.012a8.242 8.242 0 0 0-1.713 3.644v.006l-.002.004a8.542 8.542 0 0 0 .22 4.021 8.584 8.584 0 0 0 2.067 3.475l.002.002.002.002a8.638 8.638 0 0 0 3.453 2.13 8.503 8.503 0 0 0 4.067.28h.006a8.28 8.28 0 0 0 3.726-1.707l.01-.008.01-.01c.25-.213.492-.436.718-.66l.012-.011.012-.012c.2-.21.413-.44.625-.71l.002-.001a9.842 9.842 0 0 0 1.078-1.631v-.002c.62-1.185.909-2.351 1.203-3.305v-.002c.15-.49.299-.955.469-1.396l.002-.004c.169-.432.355-.841.582-1.264l.008-.013.005-.014c.415-.833 1.136-1.852 1.455-3.303v-.006a4.192 4.192 0 0 0-.218-2.502c-.207-.5-.484-.884-.768-1.226.418-.48.741-1.04.904-1.668a4.334 4.334 0 0 0-.164-2.742 1 1 0 0 0-.931-.637Zm-3.505 4.968c.224.013.444.056.629.129l.004.002.005.002c.396.151.747.435 1.147.796.373.393.668.738.832 1.14l.002.005.002.004c.158.377.196.898.107 1.309-.22.995-.77 1.8-1.275 2.808-.268.5-.49.99-.684 1.487l-.002.001v.004a22.877 22.877 0 0 0-.517 1.534c-.317 1.027-.579 2.04-1.063 2.964v.002a7.901 7.901 0 0 1-.863 1.303l-.006.006-.004.006a8.31 8.31 0 0 1-.504.57l-.01.01c-.179.177-.373.354-.566.52a6.317 6.317 0 0 1-2.818 1.292 6.532 6.532 0 0 1-3.104-.21A6.67 6.67 0 0 1 5.89 19.06l-.002-.002a6.611 6.611 0 0 1-1.58-2.663 6.568 6.568 0 0 1-.164-3.072 6.271 6.271 0 0 1 1.29-2.742c.17-.194.35-.387.532-.568l.008-.01c.198-.182.381-.349.555-.484l.005-.004.006-.006c.4-.323.83-.613 1.276-.846h.002c.903-.475 1.913-.742 2.945-1.07.508-.162 1.02-.331 1.535-.535l.002-.002h.004c.494-.2.977-.426 1.465-.692l.023-.011c1.004-.518 1.803-1.071 2.772-1.297.208-.045.438-.062.662-.05Zm-5.572 5.043c-.396 0-.788.07-1.157.203l-.072.025-.006.002-.01.004c-1.176.396-2.048.73-3.003 1.686-1.54 1.54-1.53 4.069.013 5.611a3.985 3.985 0 0 0 2.819 1.168 3.956 3.956 0 0 0 2.793-1.152h.002c.957-.958 1.29-1.833 1.685-3.01l.004-.008.023-.07a3.339 3.339 0 0 0-.76-3.492 3.303 3.303 0 0 0-2.331-.967Zm0 2c.354 0 .667.13.917.38.368.369.483.875.293 1.4l-.001.005-.036.104-.002.003c-.396 1.181-.465 1.499-1.205 2.239h-.002v.002c-.37.369-.85.566-1.38.566a1.947 1.947 0 0 1-1.405-.582 1.96 1.96 0 0 1-.013-2.783c.741-.742 1.057-.811 2.242-1.21l.008-.001.103-.037h.002c.159-.058.318-.086.479-.086Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconVegetarian;
