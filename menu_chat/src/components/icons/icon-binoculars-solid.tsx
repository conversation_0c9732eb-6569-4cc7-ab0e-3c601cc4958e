import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconBinocularsSolid = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M6.99 3.99c-1.44 0-2.71.92-3.17 2.33L1.3 14.79c-.004.016-.003.033-.007.05A4.84 4.84 0 0 0 1 16.5c0 1.097.36 2.208 1.127 3.07C2.894 20.433 4.083 21 5.5 21c1.417 0 2.606-.567 3.373-1.43.723-.812 1.072-1.846 1.111-2.88.658.2 1.336.31 2.016.31.682 0 1.361-.111 2.02-.313C14.12 19.067 16.098 21 18.5 21c2.466 0 4.5-2.034 4.5-4.5a4.4 4.4 0 0 0-.26-1.441 1 1 0 0 0-.04-.27l-2.53-8.51a3.32 3.32 0 0 0-3.16-2.289c-.97 0-1.89.42-2.52 1.16-.54.62-.81 1.4-.82 2.17a4.51 4.51 0 0 0-3.35 0c0-.79-.28-1.56-.81-2.17a3.302 3.302 0 0 0-2.52-1.16ZM5.5 14c.917 0 1.477.308 1.877.758.4.45.623 1.09.623 1.742 0 .653-.223 1.292-.623 1.742-.4.45-.96.758-1.877.758s-1.477-.308-1.877-.758c-.4-.45-.623-1.09-.623-1.742 0-.653.223-1.292.623-1.742.4-.45.96-.758 1.877-.758Zm13 0c1.36 0 2.5 1.14 2.5 2.5S19.86 19 18.5 19 16 17.86 16 16.5s1.14-2.5 2.5-2.5Z"
        />
      </svg>
    </span>
  );
};

export default IconBinocularsSolid;
