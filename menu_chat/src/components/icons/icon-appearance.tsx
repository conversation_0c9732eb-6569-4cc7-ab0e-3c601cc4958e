import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconAppearance = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M4 4c-1.093 0-2 .907-2 2v12c0 1.093.907 2 2 2h16c1.093 0 2-.907 2-2V6c0-1.093-.907-2-2-2H4Zm0 2h16v12H4V6Zm8 1c-4.278 0-6.355 4.565-6.355 4.565a1 1 0 0 0 0 .87S7.722 17 12 17c4.278 0 6.355-4.565 6.355-4.565a1 1 0 0 0 0-.87S16.279 7 12 7Zm0 2c2.413 0 3.855 2.336 4.24 3-.385.664-1.827 3-4.24 3-2.413 0-3.855-2.336-4.24-3 .385-.664 1.827-3 4.24-3Zm0 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"
        />
      </svg>
    </span>
  );
};

export default IconAppearance;
