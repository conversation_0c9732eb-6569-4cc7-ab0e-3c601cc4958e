import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconHalal = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <g clipPath="url(#a)">
          <g clipPath="url(#b)">
            <path
              fill="currentColor"
              d="M12 .756a1.02 1.02 0 0 0-.716.294l-2.7 2.7h-3.82c-.56 0-1.014.454-1.014 1.014v3.82l-2.698 2.7a1.015 1.015 0 0 0 0 1.434l2.698 2.7v3.818c0 .56.454 1.014 1.014 1.014h3.82l2.701 2.701a1.02 1.02 0 0 0 1.433-.001l2.698-2.7h3.822c.559 0 1.012-.453 1.012-1.012v-3.822l2.698-2.7a1.014 1.014 0 0 0 0-1.432L20.25 8.583v-3.82c0-.559-.455-1.013-1.015-1.013l-3.817.001-2.702-2.7A1.009 1.009 0 0 0 12 .756Zm0 1.7 2.496 2.495c.191.194.448.3.72.3l3.534-.001v3.535c0 .269.105.524.297.717L21.545 12l-2.496 2.496a1.007 1.007 0 0 0-.299.718v3.536h-3.535c-.269 0-.524.105-.717.297L12 21.545l-2.5-2.5a1.01 1.01 0 0 0-.715-.295H5.25v-3.535c0-.269-.105-.524-.297-.717L2.455 12l2.5-2.5a1.01 1.01 0 0 0 .295-.715V5.25h3.535c.269 0 .524-.105.717-.297L12 2.455ZM8.25 8.25A.75.75 0 0 0 7.5 9v2.25c0 .293-.037 1.25-.5 1.251-.182 0-.329-.252-.329-.252a.75.75 0 1 0-1.342.67C5.53 13.32 6.107 14 7 14c.335 0 2.001-.133 2.001-2.751V9a.75.75 0 0 0-.75-.75Zm2.25 0a.75.75 0 0 0-.75.75c0 .964.349 1.684.752 2.562a.749.749 0 0 0 1.363-.624c-.333-.727-.615-1.281-.615-1.938a.75.75 0 0 0-.75-.75Zm3 0a.75.75 0 0 0-.75.75c0 3.827-.967 4.5-3 4.5a.75.75 0 1 0 0 1.5c1.5 0 2.576-.388 3.303-1.233.732.342 1.396.483 2.001.483 1.177 0 2.152-.489 3.243-.979a.75.75 0 0 0 .082-1.336c-1.019-.595-.843-2.185-2.629-2.185a.75.75 0 0 0 0 1.5c.425 0 .391.387.911 1.106-.94.397-1.758.598-2.877.103.315-.891.466-2.03.466-3.459a.75.75 0 0 0-.75-.75Z"
            />
          </g>
        </g>
        <defs>
          <clipPath id="a">
            <rect width="24" height="24" fill="#fff" />
          </clipPath>
          <clipPath id="b">
            <rect width="24" height="24" fill="#fff" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconHalal;
