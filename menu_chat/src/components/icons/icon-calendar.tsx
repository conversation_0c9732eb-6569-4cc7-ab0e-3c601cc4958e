import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconCalendar = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M4 3a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H4Zm1 2h14v14H5V5Zm2 2v2h2V7H7Zm4 0v2h2V7h-2Zm4 0v2h2V7h-2Zm-8 3.986v2h2v-2H7Zm4 0v2h2v-2h-2Zm4 0v2h2v-2h-2ZM7 15v2h2v-2H7Zm4 0v2h2v-2h-2Z"
        />
      </svg>
    </span>
  );
};

export default IconCalendar;
