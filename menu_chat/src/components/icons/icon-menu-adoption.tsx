import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconMenuAdoption = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M11.99.998a1.002 1.002 0 0 0-.998 1.047C7.404 2.422 4.47 4.802 3.344 8H2.99a1 1 0 1 0 0 2h.828c.107.017.216.017.323 0h.097a1 1 0 0 0-.931 1.365l.107.272v.004c.347.952.58 1.867.58 2.84 0 1.09.406 2.07.945 3.007l-.685 1.371a1 1 0 0 0 .38 1.305l4.14 2.48a1 1 0 0 0 1.41-.412l.755-1.517.002-.002c.414-.823.977-1.277 1.72-1.777.74-.5 1.666-1.011 2.46-1.936l.002-.002 2.75-3.186.006-.005c.402-.475.884-1.042 1.299-1.596a5.71 5.71 0 0 0 .347-.492l.137-.24A1.002 1.002 0 0 0 18.947 10h.889c.108.018.218.018.326 0h.83a1 1 0 1 0 0-2h-.36c-1.126-3.196-4.057-5.577-7.644-5.955A.998.998 0 0 0 11.99.998Zm-.002 2.988c2.95 0 5.369 1.692 6.492 4.014H5.496C6.62 5.678 9.04 3.986 11.988 3.986ZM4.35 10h14.283a1.01 1.01 0 0 0-.098.02l-.049.013c-2.132.359-3.698 1.742-5.091 2.893-1.419 1.17-2.623 2.06-3.9 2.06-.312 0-.419-.054-.714-.465-.295-.41-.63-1.175-.976-2.19v-.005c-.192-.555-.465-1.179-1.057-1.627-.592-.447-1.343-.63-2.33-.697A.956.956 0 0 0 4.35 10Z"
        />
      </svg>
    </span>
  );
};

export default IconMenuAdoption;
