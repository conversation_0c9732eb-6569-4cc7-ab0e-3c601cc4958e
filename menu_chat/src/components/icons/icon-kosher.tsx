import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconKosher = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M3.77 2.585A.84.84 0 1 0 2.587 3.78l1.996 1.973a7.364 7.364 0 0 0-2.065 2.878c-.354-.044-.403-.172-.48-.333-.093-.199-.095-.428-.095-.428a.828.828 0 0 0-.238-.647.847.847 0 0 0-1.447.542s-.054.607.248 1.245c.234.493.781 1.002 1.553 1.212-.098.56-.113.94-.113.94 0 3.817 1.738 8.638 1.738 8.638a.837.837 0 0 0 .309.408c.143.1.315.154.49.154h1.692a.85.85 0 0 0 .598-.245.832.832 0 0 0 .248-.591v-2.336c1.215.504 3.493 1.262 6.399 1.398.***************.135.373.14.403.268.813.268.813.************.306.426a.853.853 0 0 0 .503.162h1.691a.85.85 0 0 0 .598-.245.831.831 0 0 0 .248-.591v-.921c.125-.013.253-.02.38-.03l2.87 2.84a.84.84 0 1 0 1.183-1.196L3.77 2.584Zm7.48 1.052c-1.307 0-2.483.169-3.546.473l1.408 1.392a12.162 12.162 0 0 1 2.137-.193c3.04 0 4.864 1.368 5.765 2.3a3.33 3.33 0 0 0-.776.435c-.416.323-.76.855-.76 1.447a.828.828 0 0 0 .243.6.847.847 0 0 0 .929.184.847.847 0 0 0 .458-.46.828.828 0 0 0 .061-.324c0-.035-.027-.026.113-.134.139-.108.445-.253.855-.366.592-.163 1.411-.246 2.27-.29-.343 1.117-1.21 1.793-1.21 1.793a.838.838 0 0 0-.336.67s-.007.865.24 1.783c.125.46.306.95.658 1.386.17.21.525.243.793.388v.997c-.328.223-.402.7-.12.98l.233.229c.26.257.663.317.964.11l.145-.104a.828.828 0 0 0 .47-.889V14.3a.831.831 0 0 0-.248-.591.85.85 0 0 0-.598-.245c-.159 0-.194-.024-.317-.177-.124-.152-.26-.446-.347-.77-.14-.516-.138-.867-.146-1.072A4.655 4.655 0 0 0 22.23 8.01a.828.828 0 0 0-.082-.594l-.004-.01a.848.848 0 0 0-.092-.131l-.003-.006a.843.843 0 0 0-.053-.056l-.007-.003a.843.843 0 0 0-.056-.053l-.007-.003a.852.852 0 0 0-.71-.166l-.007.003a17.24 17.24 0 0 0-2.358.18c-.69-.945-2.967-3.535-7.602-3.535ZM5.774 6.933l10.238 10.124a.83.83 0 0 0-.535.777v.857h-.232c-.04-.122-.041-.135-.089-.271a11.971 11.971 0 0 0-.231-.621 2.583 2.583 0 0 0-.126-.268c-.025-.048-.05-.098-.115-.18a.84.84 0 0 0-.165-.157.823.823 0 0 0-.185-.107 2.142 2.142 0 0 0-.149-.05l-.188-.019c-3.524 0-7.46-1.8-7.46-1.8a.854.854 0 0 0-1.104.351.829.829 0 0 0-.105.404v2.718h-.211c-.267-.807-1.48-4.565-1.48-7.527 0 0 .024-2.5 2.137-4.231Zm7.958 11.712c.***************.265.046l-.182-.02-.083-.026Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <rect width="24" height="24" fill="#fff" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconKosher;
