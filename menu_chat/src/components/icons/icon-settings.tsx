import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconSettings = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M11.13 1c-.944 0-1.772.677-1.96 1.602l-.201.994A8.917 8.917 0 0 0 6.232 5.17l-.957-.32a2.007 2.007 0 0 0-2.365.894l-.002.002-.869 1.508a2.01 2.01 0 0 0 .406 2.498l.758.67C3.108 10.937 3 11.449 3 12c0 .55.108 1.063.203 1.578l-.758.67a2.01 2.01 0 0 0-.408 2.496v.002l.871 1.508a2.01 2.01 0 0 0 2.367.894l.96-.32a8.912 8.912 0 0 0 2.732 1.576l.203.992A2.013 2.013 0 0 0 11.13 23h1.74c.944 0 1.772-.677 1.96-1.602l.201-.994a8.92 8.92 0 0 0 2.735-1.574l.957.32c.895.3 1.896-.077 2.369-.896l.869-1.506v-.002c.47-.817.3-1.87-.406-2.496l-.002-.002-.756-.67c.095-.515.203-1.027.203-1.578 0-.55-.108-1.063-.203-1.578l.758-.67a2.01 2.01 0 0 0 .408-2.496v-.002l-.871-1.508a2.01 2.01 0 0 0-2.367-.894l-.96.32a8.91 8.91 0 0 0-2.732-1.576l-.203-.992v-.002A2.01 2.01 0 0 0 12.87 1h-1.74Zm0 2H12.872l.305 1.502a1 1 0 0 0 .71.764A6.983 6.983 0 0 1 16.894 7a1 1 0 0 0 1.017.232l1.45-.484.869 1.504v.002L19.08 9.27a1 1 0 0 0-.306.996c.143.564.226 1.142.226 1.734 0 .592-.082 1.17-.227 1.734a1 1 0 0 0 .307.996l1.149 1.016-.87 1.508-1.45-.486a1 1 0 0 0-1.018.234 6.98 6.98 0 0 1-3.006 1.732 1 1 0 0 0-.71.764L12.87 21h-1.738l-.002-.002-.305-1.5a1 1 0 0 0-.71-.764A6.983 6.983 0 0 1 7.106 17a1 1 0 0 0-1.017-.232l-1.45.484-.868-1.504v-.002L4.92 14.73a1 1 0 0 0 .307-.996A6.991 6.991 0 0 1 5 12c0-.592.083-1.17.227-1.734a1 1 0 0 0-.307-.996L3.772 8.254l.869-1.506v-.002l1.449.486a1 1 0 0 0 1.017-.234 6.977 6.977 0 0 1 3.006-1.732 1 1 0 0 0 .711-.764L11.131 3ZM12 8c-1.25 0-2.315.505-2.998 1.273C8.319 10.043 8 11.028 8 12s.319 1.958 1.002 2.727C9.685 15.495 10.75 16 12 16c1.25 0 2.315-.505 2.998-1.273C15.681 13.957 16 12.972 16 12s-.319-1.958-1.002-2.727C14.315 8.505 13.25 8 12 8Zm0 2c.75 0 1.185.245 1.502.602.317.356.498.87.498 1.398 0 .528-.181 1.042-.498 1.398-.317.357-.752.602-1.502.602s-1.185-.245-1.502-.602C10.181 13.042 10 12.528 10 12c0-.528.181-1.042.498-1.398.317-.357.752-.602 1.502-.602Z"
        />
      </svg>
    </span>
  );
};

export default IconSettings;
