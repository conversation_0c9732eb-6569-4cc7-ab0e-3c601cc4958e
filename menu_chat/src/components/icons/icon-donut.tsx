import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconDonut = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 2C6.489 2 2 6.489 2 12s4.489 10 10 10 10-4.489 10-10S17.511 2 12 2Zm0 2c4.43 0 8 3.57 8 8s-3.57 8-8 8-8-3.57-8-8 3.57-8 8-8Zm0 4c-1.25 0-2.315.505-2.998 1.273C8.319 10.043 8 11.028 8 12s.319 1.958 1.002 2.727C9.685 15.495 10.75 16 12 16c1.25 0 2.315-.505 2.998-1.273C15.681 13.957 16 12.972 16 12s-.319-1.958-1.002-2.727C14.315 8.505 13.25 8 12 8Zm0 2c.75 0 1.185.245 1.502.602.317.356.498.87.498 1.398 0 .528-.181 1.042-.498 1.398-.317.357-.752.602-1.502.602s-1.185-.245-1.502-.602C10.181 13.042 10 12.528 10 12c0-.528.181-1.042.498-1.398.317-.357.752-.602 1.502-.602Z"
        />
      </svg>
    </span>
  );
};

export default IconDonut;
