import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconSustainable = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M16.027 1.031a1 1 0 0 0-.361.063l-2.992 1.06a1 1 0 0 0-.508 1.483l1.857 2.902a1.002 1.002 0 1 0 1.686-1.078l-.49-.766c2.283 1.011 4.019 3.044 4.584 5.528a1 1 0 1 0 1.949-.446c-.685-3.011-2.738-5.482-5.46-6.783l.042-.015a1 1 0 0 0-.307-1.948Zm-5.982 1.19a1 1 0 0 0-.268.027c-3.192.726-5.79 2.984-7.013 5.953l-.203-.283A1 1 0 1 0 .935 9.082l1.836 2.563a1 1 0 0 0 1.479.164l2.414-2.147a1 1 0 0 0-1.328-1.496l-.606.537c1.023-2.244 3.037-3.947 5.493-4.506a1 1 0 0 0-.178-1.976Zm10.318 9.719a1 1 0 0 0-.613.252l-2.414 2.146a1.001 1.001 0 0 0 1.328 1.496l.605-.537c-1.022 2.244-3.036 3.947-5.492 4.506a1.001 1.001 0 0 0-.324 1.835.998.998 0 0 0 .77.114c3.192-.726 5.79-2.983 7.013-5.953l.203.283a1 1 0 1 0 1.625-1.164l-1.835-2.562a1 1 0 0 0-.866-.416ZM3.182 12.986a1 1 0 0 0-.934 1.235c.686 3.019 2.747 5.496 5.479 6.795l-.186.095a1 1 0 1 0 .918 1.778l3.062-1.586a1 1 0 0 0 .301-1.537L9.76 17.352a1.001 1.001 0 0 0-1.324-.193 1 1 0 0 0-.196 1.49l.572.671c-2.299-1.005-4.048-3.046-4.615-5.54a1 1 0 0 0-1.015-.794Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconSustainable;
