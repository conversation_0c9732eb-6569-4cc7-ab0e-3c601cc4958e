import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconArrowLeft = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M16.995 19.981a1 1 0 0 0-.307-.697L9.395 11.99l7.293-7.293a1 1 0 1 0-1.414-1.414l-8 8a1 1 0 0 0 0 1.414l8 8a1 1 0 0 0 1.72-.717Z"
        />
      </svg>
    </span>
  );
};

export default IconArrowLeft;
