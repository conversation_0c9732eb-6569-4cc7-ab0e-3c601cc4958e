import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconLogout = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M6.5 2C5.13 2 4 3.13 4 4.5v15C4 20.87 5.13 22 6.5 22h11c1.37 0 2.5-1.13 2.5-2.5V19a1 1 0 1 0-2 0v.5c0 .287-.213.5-.5.5h-11a.487.487 0 0 1-.5-.5v-15c0-.287.213-.5.5-.5h11c.287 0 .5.213.5.5V5a1 1 0 1 0 2 0v-.5C20 3.13 18.87 2 17.5 2h-11Zm10.49 5.99a1 1 0 0 0-.697 1.717L17.586 11H10a1 1 0 1 0 0 2h7.586l-1.293 1.293a1 1 0 1 0 1.414 1.414l3-3a1 1 0 0 0 0-1.414l-3-3a1 1 0 0 0-.717-.303Z"
        />
      </svg>
    </span>
  );
};

export default IconLogout;
