import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconPerson = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 2C9.80267 2 8 3.80267 8 6V7C8 9.19733 9.80267 11 12 11C14.1973 11 16 9.19733 16 7V3C16 2.73479 15.8946 2.48045 15.7071 2.29292C15.5195 2.10539 15.2652 2.00003 15 2H12ZM12 4H14V7C14 8.11667 13.1167 9 12 9C10.8833 9 10 8.11667 10 7V6C10 4.88333 10.8833 4 12 4ZM9.96289 13C9.8937 13.0026 9.82497 13.0125 9.75781 13.0293C5.37736 14.1244 3.16797 17.4453 3.16797 17.4453C3.05846 17.6096 3.00001 17.8026 3 18V20C3.00003 20.2652 3.10539 20.5195 3.29292 20.7071C3.48045 20.8946 3.73479 21 4 21H20C20.2652 21 20.5195 20.8946 20.7071 20.7071C20.8946 20.5195 21 20.2652 21 20V18C21 17.8026 20.9415 17.6096 20.832 17.4453C20.832 17.4453 18.6226 14.1244 14.2422 13.0293C14.0106 12.9716 13.7661 12.9986 13.5527 13.1055L12 13.8828L10.4473 13.1055C10.2972 13.0302 10.1306 12.9939 9.96289 13ZM9.94336 15.0898L11.5527 15.8945C11.6916 15.964 11.8447 16.0001 12 16.0001C12.1553 16.0001 12.3084 15.964 12.4473 15.8945L14.0566 15.0898C17.2317 16.0071 18.7973 18.0857 19 18.373V19H5V18.373C5.2027 18.0857 6.76826 16.0071 9.94336 15.0898Z"
        />
      </svg>
    </span>
  );
};

export default IconPerson;
