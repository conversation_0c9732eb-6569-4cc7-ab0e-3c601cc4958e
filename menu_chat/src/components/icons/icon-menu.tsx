import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconMenu = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M8 2C4.146 2 1 5.146 1 9v6c0 3.854 3.146 7 7 7h8c3.854 0 7-3.146 7-7V9c0-3.854-3.146-7-7-7H8Zm0 2h8c2.774 0 5 2.226 5 5v6a4.98 4.98 0 0 1-3 4.582V8a1 1 0 0 0-1.906-.422S14 11.575 14 15.533C14 17.42 16 17 16 18c0 .928-.251 1.547-.502 2h-5.055c-.09-1.113-.28-2.292-.38-4.506l1.39-.928A3.476 3.476 0 0 0 13 11.678V8a1 1 0 1 0-2 0v3.678c0 .11-.018.217-.041.322H10V8a1 1 0 1 0-2 0v4h-.959A1.47 1.47 0 0 1 7 11.678V8a1 1 0 1 0-2 0v3.678c0 1.16.582 2.245 1.547 2.888l1.39.926c-.099 2.202-.288 3.38-.38 4.486A4.981 4.981 0 0 1 3 15V9c0-2.774 2.226-5 5-5Z"
        />
      </svg>
    </span>
  );
};

export default IconMenu;
