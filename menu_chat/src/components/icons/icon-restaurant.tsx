import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconIngredients = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M8 2C4.14567 2 1 5.14567 1 9V15C1 18.8543 4.14567 22 8 22H16C19.8543 22 23 18.8543 23 15V9C23 5.14567 19.8543 2 16 2H8ZM8 4H16C18.7737 4 21 6.22633 21 9V15C21 17.0602 19.7685 18.8138 18 19.582V16V8C18 7.73478 17.8946 7.48043 17.7071 7.29289C17.5196 7.10536 17.2652 7 17 7C16.8096 7.00008 16.6231 7.05452 16.4626 7.15694C16.302 7.25935 16.1741 7.40547 16.0938 7.57812C16.0938 7.57812 14 11.5752 14 15.5332C14 17.4192 16 17 16 18C16 18.928 15.7489 19.5467 15.498 20H10.4434C10.3523 18.887 10.1623 17.7082 10.0625 15.4941L11.4531 14.5664C12.4181 13.9227 13 12.8371 13 11.6777V8C13.0019 7.86749 12.9774 7.73593 12.928 7.61296C12.8786 7.48999 12.8052 7.37807 12.7122 7.2837C12.6191 7.18933 12.5082 7.11439 12.386 7.06324C12.2637 7.0121 12.1325 6.98576 12 6.98576C11.8675 6.98576 11.7363 7.0121 11.614 7.06324C11.4918 7.11439 11.3809 7.18933 11.2878 7.2837C11.1948 7.37807 11.1214 7.48999 11.072 7.61296C11.0226 7.73593 10.9981 7.86749 11 8V11.6777C11 11.7876 10.9825 11.8952 10.959 12H10V8C10.0019 7.86749 9.97739 7.73593 9.92798 7.61296C9.87857 7.48999 9.8052 7.37807 9.71216 7.2837C9.61912 7.18933 9.50824 7.11439 9.38599 7.06324C9.26373 7.0121 9.13253 6.98576 9 6.98576C8.86748 6.98576 8.73627 7.0121 8.61401 7.06324C8.49176 7.11439 8.38088 7.18933 8.28784 7.2837C8.1948 7.37807 8.12143 7.48999 8.07202 7.61296C8.02261 7.73593 7.99813 7.86749 8 8V12H7.04102C7.0175 11.8952 7 11.7879 7 11.6777V8C7.00183 7.86621 6.9768 7.73341 6.92638 7.60947C6.87597 7.48553 6.80119 7.37297 6.70649 7.27845C6.61179 7.18393 6.49908 7.10937 6.37504 7.05919C6.25101 7.00902 6.11816 6.98424 5.98438 6.98633C5.7195 6.99047 5.46709 7.09953 5.28254 7.28957C5.09799 7.47962 4.99637 7.73512 5 8V11.6777C5 12.8377 5.58195 13.9227 6.54688 14.5664L7.9375 15.4922C7.83838 17.6936 7.64868 18.8726 7.55664 19.9785C4.99308 19.7559 3 17.624 3 15V9C3 6.22633 5.22633 4 8 4Z"
        />
      </svg>
    </span>
  );
};

export default IconIngredients;
