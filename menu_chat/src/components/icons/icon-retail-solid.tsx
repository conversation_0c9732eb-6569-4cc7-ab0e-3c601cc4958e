import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconRetailSolid = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M17.5 19a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Zm-9 0a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3ZM22.773 3.366A1.001 1.001 0 0 0 22 3H3.773a2.506 2.506 0 0 0-1.986-1H1a1 1 0 1 0 0 2h.787c.238 0 .439.163.489.396l2.407 11.232A3.016 3.016 0 0 0 7.617 18H19a1 1 0 1 0 0-2H7.617c-.469 0-.88-.332-.979-.791L6.38 14h12.181a3.006 3.006 0 0 0 2.941-2.411l1.479-7.393a1.003 1.003 0 0 0-.208-.83Z"
        />
      </svg>
    </span>
  );
};

export default IconRetailSolid;
