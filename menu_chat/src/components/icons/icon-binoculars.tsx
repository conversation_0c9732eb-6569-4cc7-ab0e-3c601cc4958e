import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconBinoculars = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M7.365 4a3.841 3.841 0 0 0-3.64 2.668L1.31 14.801c-.05.134-.084.27-.122.408l-.01.033a.998.998 0 0 0-.034.143c-.086.367-.145.74-.145 1.115 0 1.097.36 2.208 1.127 3.07C2.894 20.433 4.083 21 5.5 21c1.417 0 2.606-.567 3.373-1.43.721-.81 1.071-1.842 1.111-2.875a6.93 6.93 0 0 0 4.036-.002C14.123 19.07 16.1 21 18.5 21c2.466 0 4.5-2.034 4.5-4.5 0-.353-.052-.694-.13-1.025a.999.999 0 0 0-.05-.235l-2.545-8.574A3.84 3.84 0 0 0 16.635 4c-1.884 0-3.393 1.369-3.756 3.09A4.53 4.53 0 0 0 12 6.994a4.53 4.53 0 0 0-.879.096C10.758 5.369 9.25 4 7.365 4Zm0 2c1.164 0 2 1.003 1.84 2.127a.999.999 0 0 0-.008.07l-.049.682a1 1 0 0 0 1.975.293 2.493 2.493 0 0 1 1.754 0 1 1 0 0 0 1.975-.293l-.05-.682a.922.922 0 0 0-.007-.07A1.844 1.844 0 0 1 16.635 6c.791 0 1.47.49 1.73 1.262l1.477 4.967A4.397 4.397 0 0 0 18.5 12c-1.636 0-3.068.904-3.855 2.227a4.917 4.917 0 0 1-5.198.05 4.287 4.287 0 0 0-.574-.847C8.106 12.567 6.917 12 5.5 12c-.47 0-.913.067-1.326.182l1.459-4.914v-.002C5.892 6.49 6.572 6 7.365 6ZM5.5 14c.917 0 1.477.308 1.877.758.4.45.623 1.09.623 1.742 0 .653-.223 1.292-.623 1.742-.4.45-.96.758-1.877.758s-1.477-.308-1.877-.758c-.4-.45-.623-1.09-.623-1.742 0-.224.035-.443.086-.656 0-.004.003-.008.004-.012a.87.87 0 0 0 .006-.022l.095-.32c.105-.272.247-.524.432-.732.4-.45.96-.758 1.877-.758Zm13 0c1.36 0 2.5 1.14 2.5 2.5S19.86 19 18.5 19 16 17.86 16 16.5s1.14-2.5 2.5-2.5Z"
        />
      </svg>
    </span>
  );
};

export default IconBinoculars;
