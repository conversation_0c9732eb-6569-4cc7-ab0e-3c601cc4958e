import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconSuperfood = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M16.008 3.029a.538.538 0 0 0-.502.256c-.713 1.152-1.261 2.287-1.41 3.615a27.065 27.065 0 0 0-1.715-1.29l-.01-.009a3.808 3.808 0 0 0-2.754-.59 3.805 3.805 0 0 0-2.412 1.454l-.002.003c-2.677 3.567-4.136 7.916-4.207 11.678a3.814 3.814 0 0 0 1.115 2.738v.003a3.815 3.815 0 0 0 2.737 1.115c3.758-.069 8.109-1.517 11.676-4.184l.003-.002a3.834 3.834 0 0 0 .856-5.193l-.006-.008a28.797 28.797 0 0 0-1.24-1.649c1.329-.122 2.457-.668 3.588-1.408a.66.66 0 0 0-.045-1.132c-1.267-.593-2.875-.775-4.32-.758-.1-1.251-.359-2.881-.895-4.305a.542.542 0 0 0-.457-.334ZM10.28 6.951c.335.008.672.11.974.312C13.772 9 15.927 11.154 17.73 13.75a1.805 1.805 0 0 1-.407 2.468c-3.21 2.4-7.244 3.723-10.503 3.784a1.81 1.81 0 0 1-1.82-1.822c.062-3.26 1.39-7.295 3.8-10.506a1.811 1.811 0 0 1 1.48-.723ZM11 9a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm2 5a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm-5 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconSuperfood;
