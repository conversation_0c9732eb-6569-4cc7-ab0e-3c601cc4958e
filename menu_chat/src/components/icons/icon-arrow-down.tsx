import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconArrowDown = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M19.99 7.986a1 1 0 0 0-.697.307L12 15.586 4.707 8.293a1 1 0 1 0-1.414 1.414l8 8a1 1 0 0 0 1.414 0l8-8a1 1 0 0 0-.717-1.72Z"
        />
      </svg>
    </span>
  );
};

export default IconArrowDown;
