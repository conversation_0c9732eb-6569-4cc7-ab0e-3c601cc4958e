import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconVegan = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M13.17 1.707a8.844 8.844 0 0 0-3.678.93A9.944 9.944 0 0 0 4 11.526v4.283L2.072 20.63a1 1 0 0 0 1.244 1.32l5.846-1.949h3.314a9.944 9.944 0 0 0 8.889-5.492 8.857 8.857 0 0 0-1.658-10.215 8.855 8.855 0 0 0-6.537-2.588Zm.064 1.998a6.842 6.842 0 0 1 5.059 2.004 6.842 6.842 0 0 1 1.283 7.907 7.934 7.934 0 0 1-7.1 4.386H9a.998.998 0 0 0-.317.051l-1.98.66 4.494-4.494 3.355 1.678a1 1 0 1 0 .895-1.79l-2.758-1.38 2.438-2.438 2.318 1.545a1 1 0 1 0 1.11-1.664L15.72 8.282l-1.888-2.834a1 1 0 1 0-1.664 1.109l1.545 2.318-2.438 2.438-1.38-2.758a1 1 0 1 0-1.79.895l1.678 3.355-4.047 4.047.191-.479a1 1 0 0 0 .073-.37v-4.477a7.934 7.934 0 0 1 4.386-7.1 6.864 6.864 0 0 1 2.848-.72Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconVegan;
