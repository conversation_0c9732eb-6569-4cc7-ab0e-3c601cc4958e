import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconGraph = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M3.984 2.986A1 1 0 0 0 3 4v16.014a1 1 0 0 0 1 1h16a1.002 1.002 0 0 0 .716-1.712 1 1 0 0 0-.716-.288H5V4a1 1 0 0 0-1.016-1.014ZM19.98 5.99a1 1 0 0 0-.687.303L15 10.586l-2.293-2.293a1 1 0 0 0-1.414 0l-4 4a1 1 0 1 0 1.414 1.414L12 10.414l2.293 2.293a1 1 0 0 0 1.414 0l5-5a1 1 0 0 0-.727-1.717Z"
        />
      </svg>
    </span>
  );
};

export default IconGraph;
