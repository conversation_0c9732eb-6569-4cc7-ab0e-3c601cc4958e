import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconReportSolid = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M19 3h-4c-.061 0-.115.024-.173.035A2.99 2.99 0 0 0 12 1a2.99 2.99 0 0 0-2.827 2.035C9.115 3.024 9.061 3 9 3H5c-1.103 0-2 .897-2 2v14c0 1.103.897 2 2 2h14c1.103 0 2-.897 2-2V5c0-1.103-.897-2-2-2Zm-7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2Zm4.447 11.895-4 2a1 1 0 0 1-.895 0L10 16.118l-1.553.776a1 1 0 0 1-.895-1.789l2-1a1 1 0 0 1 .895 0l1.553.777 3.553-1.776a1 1 0 1 1 .894 1.789ZM16 11H8a1 1 0 1 1 0-2h8a1 1 0 1 1 0 2Z"
        />
      </svg>
    </span>
  );
};

export default IconReportSolid;
