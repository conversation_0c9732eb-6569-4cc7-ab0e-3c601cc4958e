import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconPrint = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M8 2c-1.093 0-2 .907-2 2v4H5c-1.645 0-3 1.355-3 3v6c0 1.096.905 2 2 2h2v1c0 1.093.907 2 2 2h8c1.093 0 2-.907 2-2v-1h2c1.096 0 2-.904 2-2v-6c0-1.645-1.355-3-3-3h-1V4c0-1.093-.907-2-2-2H8Zm0 2h8v5H8V4Zm-3 6h1a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1h1c.555 0 1 .445 1 1v6h-2v-2a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v2H4v-6c0-.555.445-1 1-1Zm3 6h8v4H8v-4Z"
        />
      </svg>
    </span>
  );
};

export default IconPrint;
