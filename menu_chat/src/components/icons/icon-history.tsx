import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconHistory = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M5 2a2 2 0 0 0-2 2v14c0 2.209 1.567 4 3.5 4h12.014v-.002C20.44 21.99 22 20.204 22 18c0-2.013-1.306-3.66-3-3.94V4a2 2 0 0 0-2-2H5Zm0 2h12v10H6.5a3.12 3.12 0 0 0-1.363.315c-.049.023-.09.06-.137.085V4Zm3 2a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2H8Zm0 4a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2H8Zm-1.5 6c.813 0 1.5.916 1.5 2s-.687 2-1.5 2S5 19.084 5 18c0-.136.01-.268.031-.396C5.174 16.701 5.79 16 6.5 16Zm3.014 0h8.646l.346.002C19.316 16.005 20 16.92 20 18s-.684 1.995-1.494 1.998l-.293.002h-8.7c.301-.59.487-1.267.487-2 0-.733-.186-1.41-.486-2Z"
        />
      </svg>
    </span>
  );
};

export default IconHistory;
