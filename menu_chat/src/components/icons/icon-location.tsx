import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconLocation = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 1c-4.959 0-9 4.041-9 9 0 3.495 2.209 6.391 4.303 8.424 2.094 2.032 4.19 3.262 4.19 3.262a1 1 0 0 0 1.015 0s2.095-1.23 4.19-3.262C18.79 16.39 21 13.494 21 10c0-4.959-4.041-9-9-9Zm0 2c3.877 0 7 3.123 7 7 0 2.623-1.791 5.139-3.697 6.988-1.652 1.604-2.892 2.325-3.303 2.578-.41-.253-1.65-.974-3.303-2.578C6.791 15.138 5 12.623 5 10c0-3.877 3.123-7 7-7Zm0 3c-1.25 0-2.315.505-2.998 1.273C8.319 8.043 8 9.028 8 10s.319 1.958 1.002 2.727C9.685 13.495 10.75 14 12 14c1.25 0 2.315-.505 2.998-1.273C15.681 11.957 16 10.972 16 10s-.319-1.958-1.002-2.727C14.315 6.505 13.25 6 12 6Zm0 2c.75 0 1.185.245 1.502.602.317.356.498.87.498 1.398 0 .528-.181 1.042-.498 1.398-.317.357-.752.602-1.502.602s-1.185-.245-1.502-.602C10.181 11.042 10 10.528 10 10c0-.528.181-1.042.498-1.398C10.815 8.245 11.25 8 12 8Z"
        />
      </svg>
    </span>
  );
};

export default IconLocation;
