import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconDelete = ({ className, size = 24 }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      fill="none"
      viewBox="0 0 24 24"
      className={twMerge("inline-block", className)}
    >
      <path
        fill="currentColor"
        d="M9.5 2a1 1 0 0 0-.986.836L8.152 5H3.658a1 1 0 0 0-.322 0H2a1 1 0 1 0 0 2h.56l.772 12.176A3.014 3.014 0 0 0 6.326 22h11.348c1.578 0 2.9-1.249 2.994-2.824L21.439 7H22a1 1 0 1 0 0-2h-1.332a1 1 0 0 0-.328 0h-4.492l-.362-2.164A1 1 0 0 0 14.5 2h-5Zm.848 2h3.304l.166 1h-3.636l.166-1ZM4.564 7h14.872l-.764 12.055v.002a.987.987 0 0 1-.998.943H6.326a.987.987 0 0 1-.998-.943v-.002L4.564 7Zm3.448 1.986a1 1 0 0 0-1.01 1.084l.5 7a1 1 0 1 0 1.996-.14l-.5-7a1 1 0 0 0-.986-.944Zm3.972 0A1 1 0 0 0 11 10v7a1 1 0 1 0 2 0v-7a1 1 0 0 0-1.016-1.014Zm3.973 0a1 1 0 0 0-.955.944l-.5 7a1 1 0 1 0 1.996.14l.5-7a.999.999 0 0 0-1.041-1.084Z"
      />
    </svg>
  );
};

export default IconDelete;
