import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconXLS = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M14.984 2.986A1 1 0 0 0 14.84 3H9.479a2.494 2.494 0 0 0-2.48 2.48V6H3.421C2.094 6 .998 7.088.998 8.412v7.16A2.44 2.44 0 0 0 3.422 18h3.576v.52c0 1.357 1.12 2.48 2.48 2.48h5.354a.99.99 0 0 0 .326 0h5.365a2.494 2.494 0 0 0 2.48-2.48v-2.37a.998.998 0 0 0 0-.302v-3.735a1.004 1.004 0 0 0 0-.228V8.15a.998.998 0 0 0 0-.302V5.48A2.493 2.493 0 0 0 20.523 3h-5.355a1 1 0 0 0-.184-.014ZM9.48 5h4.519v2H12.53a2.427 2.427 0 0 0-1.95-1H8.997v-.52c0-.268.208-.48.48-.48Zm6.519 0h4.525c.273 0 .48.212.48.48V7h-5.005V5ZM3.422 8h4.41a1 1 0 0 0 .324 0h2.422c.24 0 .424.19.424.414v3.418a.997.997 0 0 0 0 .326v3.416a.416.416 0 0 1-.424.426H8.164a.996.996 0 0 0-.324 0H3.422a.416.416 0 0 1-.424-.428v-7.16c0-.222.184-.412.424-.412Zm9.58 1h.996v2h-.996V9Zm2.996 0h5.006v2h-5.006V9Zm-10.52.002a.5.5 0 0 0-.398.762L6.42 12l-1.34 2.236a.5.5 0 1 0 .857.516l1.067-1.777 1.064 1.777a.5.5 0 1 0 .86-.516L7.586 12l1.342-2.236a.5.5 0 1 0-.86-.514l-1.064 1.777L5.938 9.25a.5.5 0 0 0-.46-.248ZM13.003 13h.996v2h-.996v-2Zm2.996 0h5.006v2h-5.006v-2Zm-3.473 4h1.473v2h-4.52a.474.474 0 0 1-.48-.48V18h1.58c.796 0 1.504-.398 1.947-1Zm3.473 0h5.006v1.52c0 .268-.208.48-.48.48h-4.526v-2Z"
        />
      </svg>
    </span>
  );
};

export default IconXLS;
