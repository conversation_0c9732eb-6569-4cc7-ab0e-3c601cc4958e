import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconHome = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12.031 1.5a1 1 0 0 0-.539.139l-9.5 5.588A1 1 0 1 0 3 8.953V20a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V8.953a1 1 0 1 0 1.008-1.726l-9.5-5.588a1 1 0 0 0-.477-.139ZM12 3.66l7 4.117V19h-3v-7a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v7H5V7.777l7-4.117ZM10 13h4v6h-4v-6Z"
        />
      </svg>
    </span>
  );
};

export default IconHome;
