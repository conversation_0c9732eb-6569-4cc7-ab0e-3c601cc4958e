import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconPage = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <path
            stroke="currentColor"
            strokeMiterlimit="10"
            strokeWidth="2"
            d="M16.84 21h-10a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2Z"
          />
          <path
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            strokeWidth="2"
            d="M8.84 7h6M12.84 12h2"
          />
          <path fill="currentColor" d="M8.84 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" />
          <path
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            strokeWidth="2"
            d="M12.84 17h2"
          />
          <path fill="currentColor" d="M8.84 18a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconPage;
