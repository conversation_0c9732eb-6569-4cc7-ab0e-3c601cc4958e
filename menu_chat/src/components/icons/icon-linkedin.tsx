import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconLinkedin = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12.006 1.986c-5.508 0-9.999 4.498-10 10 0 5.514 4.492 10 10 10 5.507 0 9.998-4.486 9.998-10 0-5.502-4.489-10-9.998-10Zm0 2a7.994 7.994 0 0 1 7.998 8c0 4.433-3.572 8-7.998 8-4.427 0-8-3.567-8-8 0-4.418 3.576-8 8-8ZM8.036 7C7.387 7 7 7.427 7 8c0 .56.389 1 .969 1C8.616 9 9 8.56 9 8c0-.573-.385-1-.965-1ZM7 10v6h2v-6H7Zm4 0v6h2v-3.307c0-.973.875-1.12 1.086-1.12.21 0 .914.213.914 1.12V16h2v-3.307C17 10.8 16.051 10 15 10c-1.051 0-1.717.347-2 .84V10h-2Z"
        />
      </svg>
    </span>
  );
};

export default IconLinkedin;
