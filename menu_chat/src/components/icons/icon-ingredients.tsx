import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconIngredients = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M13.5 1c-.817 0-1.5.683-1.5 1.5v1.377c-.072.152-.314.654-.592 1.566A4.432 4.432 0 0 0 9.5 5c-.121 0-.223.06-.342.07-.235-.678-.685-1.473-1.726-1.972a1 1 0 1 0-.864 1.804c.371.178.574.502.694.785C5.937 6.473 5 7.857 5 9.5c0 .176.078.329.098.5h-1.93a1 1 0 0 0-.326 0H2a1 1 0 1 0 0 2v2a1 1 0 0 0 .039.275l2 7A1 1 0 0 0 5 22h14a1 1 0 0 0 .96-.725l2-7c.027-.09.04-.182.04-.275v-2a1 1 0 1 0 0-2h-.832a1 1 0 0 0-.326 0h-1.43C19.123 6.35 18.164 4.192 18 3.842V2.5c0-.817-.683-1.5-1.5-1.5h-3Zm.5 2h2v1a1 1 0 0 0 .105.447s.993 2 1.3 5.553h-3.503c.02-.171.098-.324.098-.5 0-.996-.339-1.91-.893-2.658.385-1.561.788-2.395.788-2.395A1 1 0 0 0 14 4V3ZM9.5 7C10.893 7 12 8.107 12 9.5c0 .178-.063.334-.098.5H7.098C7.063 9.834 7 9.678 7 9.5 7 8.107 8.107 7 9.5 7ZM4 12h16v1.86L18.246 20H5.754L4 13.86V12Zm3.959.986a1 1 0 0 0-.951 1.139l.5 4a1 1 0 1 0 1.984-.25l-.5-4a1 1 0 0 0-1.033-.889Zm4.025 0A1 1 0 0 0 11 14v4a1 1 0 1 0 2 0v-4a.999.999 0 0 0-1.016-1.014Zm4.028 0a1 1 0 0 0-1.004.889l-.5 4a1 1 0 1 0 1.984.25l.5-4a.999.999 0 0 0-.98-1.139Z"
        />
      </svg>
    </span>
  );
};

export default IconIngredients;
