import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconMenuSolid = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M16 2H8C4.14 2 1 5.141 1 9v6c0 3.729 2.934 6.778 6.614 6.98.111-1.744.273-3.994.346-6.472l-1.414-.942A3.467 3.467 0 0 1 5 11.677V7a1 1 0 1 1 2 0v4.677c0 .11.016.218.04.323H8V7a1 1 0 1 1 2 0v5h.96a1.45 1.45 0 0 0 .04-.323V7a1 1 0 1 1 2 0v4.677a3.47 3.47 0 0 1-1.546 2.89l-1.414.943c.074 2.487.237 4.744.348 6.491H16c.139 0 .274-.013.411-.021.276-1.092.589-2.298.589-3.98 0-1-2-.581-2-2.467 0-3.785 1.91-8.508 2.077-8.915l.005-.013.011-.027.007.001A.994.994 0 0 1 18 6a1 1 0 0 1 1 1V21.315c2.361-1.126 4-3.53 4-6.315V9c0-3.859-3.14-7-7-7Z"
        />
      </svg>
    </span>
  );
};

export default IconMenuSolid;
