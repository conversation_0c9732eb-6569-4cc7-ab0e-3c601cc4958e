import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconDairyfree = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0)">
          <path
            fill="currentColor"
            d="M10 2C8.80078 2 8 2.80078 8 4V5C8 5.5 8.10547 5.88672 8.40625 6.1875C8.28906 6.32812 8.16797 6.49609 8.0625 6.65625L4.20313 2.79688C3.8148 2.40855 3.1852 2.40855 2.79688 2.79688C2.40855 3.1852 2.40855 3.8148 2.79688 4.20313L19.7969 21.2031C20.1852 21.5915 20.8148 21.5915 21.2031 21.2031C21.5915 20.8148 21.5915 20.1852 21.2031 19.7969L18 16.5938V13C18 11.8984 16.9922 8.21094 15.5938 6.3125C15.8945 5.91406 16 5.5 16 5V4C16 2.80078 15.1992 2 14 2H10ZM10 4H14V5H10V4ZM10.1875 7H13.8125C14.5117 7.69922 16 11.4062 16 12.9062V14.5938L9.5 8.125C9.50391 8.11719 9.49609 8.10156 9.5 8.09375C9.75781 7.55859 9.99219 7.14844 10.1875 7ZM7.66242 11.7562C7.13685 11.2306 6.24214 11.4604 6.14571 12.1974C6.10936 12.4752 6.09375 12.7139 6.09375 12.9062V19C6.09375 20.6992 7.39453 22 9.09375 22H15.0938C15.3727 22 15.6394 21.9621 15.8896 21.8915C16.6294 21.6828 16.6209 20.7541 16.0543 20.2346C15.7773 19.9808 15.3757 20 15 20H9C8.39844 20 8 19.6016 8 19V12.9062C8 12.8675 7.9997 12.828 7.99998 12.7873C8.00247 12.4137 7.9266 12.0203 7.66242 11.7562Z"
          />
        </g>
        <defs>
          <clipPath id="clip0">
            <rect width="24" height="24" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconDairyfree;
