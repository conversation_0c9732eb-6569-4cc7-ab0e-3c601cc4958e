import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconFullscreen = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M19 15v4h-4a1 1 0 0 0 0 2h4a2 2 0 0 0 2-2v-4a1 1 0 0 0-2 0ZM15 5h4v4a1 1 0 0 0 2 0V5a2 2 0 0 0-2-2h-4a1 1 0 0 0 0 2ZM5 9V5h4a1 1 0 0 0 0-2H5a2 2 0 0 0-2 2v4a1 1 0 0 0 2 0Zm4 10H5v-4a1 1 0 0 0-2 0v4a2 2 0 0 0 2 2h4a1 1 0 0 0 0-2Z"
        />
      </svg>
    </span>
  );
};

export default IconFullscreen;
