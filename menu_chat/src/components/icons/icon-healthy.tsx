import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconHealthy = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 2c-1.81 0-3.375.989-4.252 2.441C7.213 4.177 6.632 4 6 4 3.803 4 2 5.803 2 8s1.803 4 4 4h.396C7.946 14.596 8 17.215 8 21a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1c0-3.785.054-6.404 1.604-9H18c2.197 0 4-1.803 4-4s-1.803-4-4-4c-.632 0-1.213.177-1.748.441C15.375 2.99 13.811 2 12 2Zm0 2c1.382 0 2.531.927 2.887 2.182a1 1 0 0 0 1.68.425A1.98 1.98 0 0 1 18 6c1.115 0 2 .885 2 2s-.885 2-2 2h-.85a1 1 0 0 0-.29 0H7.138a1 1 0 0 0-.291 0H6c-1.115 0-2-.885-2-2s.885-2 2-2a1.98 1.98 0 0 1 1.434.607 1 1 0 0 0 1.68-.425A2.998 2.998 0 0 1 12 4Zm-3.375 8h2.168c.685 1.278 1.03 2.823 1.207 4.5.148-1.682.463-3.24 1.182-4.5h2.193c-1.157 2.471-1.313 5.083-1.326 8H9.95c-.013-2.917-.17-5.529-1.326-8Z"
        />
      </svg>
    </span>
  );
};

export default IconHealthy;
