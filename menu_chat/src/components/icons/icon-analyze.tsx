import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconAnalyze = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
         xmlns="http://www.w3.org/2000/svg"
         width={size}
         height={size}
         fill="none"
         viewBox="0 0 24 24"
      >
        <path
          d="M9.99996 2C5.66864 2 2.13132 5.47406 2.01168 9.77734C1.97904 9.92062 1.97837 10.0693 2.00973 10.2129L2.01168 10.2168C2.12825 14.5228 5.66667 18 9.99996 18C11.844 18 13.5409 17.3654 14.8964 16.3105L20.2929 21.707C20.3851 21.803 20.4954 21.8796 20.6176 21.9324C20.7397 21.9852 20.8711 22.0131 21.0042 22.0144C21.1372 22.0158 21.2692 21.9906 21.3924 21.9403C21.5155 21.89 21.6275 21.8157 21.7215 21.7216C21.8156 21.6275 21.89 21.5156 21.9403 21.3924C21.9905 21.2692 22.0158 21.1373 22.0144 21.0042C22.013 20.8712 21.9852 20.7397 21.9324 20.6176C21.8796 20.4955 21.803 20.3851 21.707 20.293L16.3105 14.8965C17.1714 13.7903 17.7483 12.4565 17.9316 11H21C21.1325 11.0019 21.264 10.9774 21.387 10.928C21.51 10.8786 21.6219 10.8052 21.7163 10.7122C21.8106 10.6191 21.8856 10.5082 21.9367 10.386C21.9879 10.2637 22.0142 10.1325 22.0142 10C22.0142 9.86748 21.9879 9.73627 21.9367 9.61401C21.8856 9.49176 21.8106 9.38088 21.7163 9.28784C21.6219 9.1948 21.51 9.12143 21.387 9.07202C21.264 9.02261 21.1325 8.99813 21 9H17.1679C17.0599 8.98215 16.9497 8.98215 16.8418 9H14C13.7348 9.00005 13.4804 9.10543 13.2929 9.29297L12 10.5859L8.70699 7.29297C8.6123 7.19823 8.49953 7.12348 8.37539 7.07315C8.25126 7.02282 8.11827 6.99795 7.98434 7C7.72456 7.00414 7.4766 7.10921 7.29293 7.29297L5.5859 9H4.0898C4.56376 6.15536 7.0161 4 9.99996 4C12.0446 4 13.8332 5.01749 14.9179 6.57422C14.9911 6.68635 15.086 6.78265 15.1971 6.85741C15.3081 6.93217 15.4331 6.98388 15.5645 7.00945C15.6959 7.03503 15.8311 7.03396 15.9621 7.0063C16.0931 6.97864 16.2172 6.92497 16.3271 6.84846C16.437 6.77194 16.5303 6.67415 16.6017 6.56087C16.6731 6.4476 16.721 6.32115 16.7426 6.18902C16.7641 6.05688 16.759 5.92176 16.7274 5.79167C16.6958 5.66157 16.6384 5.53914 16.5586 5.43164C15.1153 3.36037 12.7113 2 9.99996 2ZM7.99996 9.41406L11.2929 12.707C11.4805 12.8945 11.7348 12.9998 12 12.9998C12.2651 12.9998 12.5194 12.8945 12.707 12.707L14.414 11H15.9101C15.4362 13.8446 12.9838 16 9.99996 16C7.0161 16 4.56376 13.8446 4.0898 11H5.99996C6.26516 10.9999 6.51948 10.8946 6.70699 10.707L7.99996 9.41406Z"
          fill="currentColor"
        />
      </svg>
    </span>
  );
};

export default IconAnalyze;
