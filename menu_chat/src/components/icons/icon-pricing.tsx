import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconPricing = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          d="M12.1667 1.66659C12.0562 1.66503 11.9466 1.68543 11.8441 1.7266C11.7417 1.76778 11.6484 1.82892 11.5697 1.90645C11.4911 1.98399 11.4287 2.07638 11.386 2.17827C11.3434 2.28015 11.3215 2.38948 11.3215 2.49992C11.3215 2.61036 11.3434 2.71969 11.386 2.82158C11.4287 2.92346 11.4911 3.01585 11.5697 3.09339C11.6484 3.17092 11.7417 3.23206 11.8441 3.27324C11.9466 3.31441 12.0562 3.33482 12.1667 3.33325H12.6533L10.5 5.4882L8.58919 3.57739C8.4329 3.42117 8.22097 3.33341 8 3.33341C7.77902 3.33341 7.56709 3.42117 7.41081 3.57739L4.9108 6.07739C4.83083 6.15418 4.76698 6.24616 4.72299 6.34793C4.679 6.4497 4.65577 6.55923 4.65464 6.6701C4.65351 6.78096 4.67452 6.89094 4.71642 6.99359C4.75833 7.09624 4.8203 7.18949 4.8987 7.26789C4.9771 7.34629 5.07035 7.40825 5.173 7.45016C5.27564 7.49207 5.38562 7.51307 5.49649 7.51195C5.60735 7.51082 5.71688 7.48758 5.81865 7.44359C5.92043 7.39961 6.0124 7.33576 6.08919 7.25578L8 5.34497L9.91081 7.25578C10.0671 7.412 10.279 7.49976 10.5 7.49976C10.721 7.49976 10.9329 7.412 11.0892 7.25578L13.8333 4.51327V4.99992C13.8318 5.11035 13.8522 5.21998 13.8933 5.32246C13.9345 5.42493 13.9957 5.5182 14.0732 5.59684C14.1507 5.67548 14.2431 5.73793 14.345 5.78055C14.4469 5.82317 14.5562 5.84512 14.6667 5.84512C14.7771 5.84512 14.8864 5.82317 14.9883 5.78055C15.0902 5.73793 15.1826 5.67548 15.2601 5.59684C15.3377 5.5182 15.3988 5.42493 15.44 5.32246C15.4812 5.21998 15.5016 5.11035 15.5 4.99992V2.49992C15.5 2.27891 15.4122 2.06696 15.2559 1.91069C15.0996 1.75441 14.8877 1.66661 14.6667 1.66659H12.1667ZM3.83333 8.33325C2.92245 8.33325 2.16666 9.08903 2.16666 9.99992V16.6666C2.16666 17.5775 2.92245 18.3333 3.83333 18.3333H17.1667C18.0776 18.3333 18.8333 17.5775 18.8333 16.6666V9.99992C18.8333 9.08903 18.0776 8.33325 17.1667 8.33325H3.83333ZM3.83333 9.99992H17.1667V16.6666H3.83333V9.99992ZM10.5 10.8333C10.058 10.8333 9.63405 11.0966 9.32149 11.5655C9.00893 12.0343 8.83333 12.6702 8.83333 13.3333C8.83333 13.9963 9.00893 14.6322 9.32149 15.101C9.63405 15.5699 10.058 15.8333 10.5 15.8333C10.942 15.8333 11.3659 15.5699 11.6785 15.101C11.9911 14.6322 12.1667 13.9963 12.1667 13.3333C12.1667 12.6702 11.9911 12.0343 11.6785 11.5655C11.3659 11.0966 10.942 10.8333 10.5 10.8333ZM6.33333 12.4999C6.11232 12.4999 5.90036 12.5877 5.74408 12.744C5.5878 12.9003 5.5 13.1122 5.5 13.3333C5.5 13.5543 5.5878 13.7662 5.74408 13.9225C5.90036 14.0788 6.11232 14.1666 6.33333 14.1666C6.55434 14.1666 6.76631 14.0788 6.92259 13.9225C7.07887 13.7662 7.16666 13.5543 7.16666 13.3333C7.16666 13.1122 7.07887 12.9003 6.92259 12.744C6.76631 12.5877 6.55434 12.4999 6.33333 12.4999ZM14.6667 12.4999C14.4457 12.4999 14.2337 12.5877 14.0774 12.744C13.9211 12.9003 13.8333 13.1122 13.8333 13.3333C13.8333 13.5543 13.9211 13.7662 14.0774 13.9225C14.2337 14.0788 14.4457 14.1666 14.6667 14.1666C14.8877 14.1666 15.0996 14.0788 15.2559 13.9225C15.4122 13.7662 15.5 13.5543 15.5 13.3333C15.5 13.1122 15.4122 12.9003 15.2559 12.744C15.0996 12.5877 14.8877 12.4999 14.6667 12.4999Z"
          fill="#E09600"
        />
      </svg>
    </span>
  );
};

export default IconPricing;
