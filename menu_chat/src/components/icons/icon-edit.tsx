import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconEdit = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M13.984 1.986A1 1 0 0 0 13.84 2H6.5C5.13 2 4 3.13 4 4.5v15C4 20.869 5.13 22 6.5 22H10a1 1 0 1 0 0-2H6.5a.487.487 0 0 1-.5-.5v-15c0-.287.213-.5.5-.5H13v4a1 1 0 0 0 1 1h4v2.5a1 1 0 1 0 2 0V8.168a1 1 0 0 0-.377-.961l-.006-.004-4.826-4.826a1 1 0 0 0-.807-.39ZM15 5.414 16.586 7H15V5.414Zm5.36 8.676c-.4 0-.8.149-1.1.449l-.73.73 2.2 2.202.73-.73c.6-.6.6-1.592 0-2.202-.3-.3-.7-.45-1.1-.45Zm-2.89 2.24-4.39 4.39c-.25.25-.43.56-.52.9l-.44 1.65a.5.5 0 0 0 .61.61l1.65-.44c.34-.09.65-.27.9-.52l4.39-4.39-2.2-2.2Z"
        />
      </svg>
    </span>
  );
};

export default IconEdit;
