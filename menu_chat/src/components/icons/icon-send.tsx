import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconSend = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M5.955 2.002a1 1 0 0 0-.6.232l-2 1.684a1 1 0 0 0-.351.861L3.707 12l-.703 7.22a1 1 0 0 0 .351.862l2 1.684a.999.999 0 0 0 1.084.132l15.438-7.539A2.002 2.002 0 0 0 23 12.563v-1.126c0-.762-.437-1.462-1.123-1.796L6.439 2.1a1 1 0 0 0-.484-.1Zm.191 2.182L21 11.437v1.126L6.146 19.816l-1.1-.925L5.62 13H15a1 1 0 1 0 0-2H5.62l-.573-5.89 1.1-.926Z"
        />
      </svg>
    </span>
  );
};

export default IconSend;
