import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconThumbsup = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M11.938 1a1 1 0 0 0-.963.623L9.482 5.29 7.578 7.074c-.42.395-.716.884-.974 1.387C6.249 8.267 5.928 8 5.5 8h-1C3.13 8 2 9.131 2 10.5v9C2 20.87 3.13 22 4.5 22h1c.679 0 1.295-.284 1.748-.73.506.388 1.071.73 1.752.73h6.46a4.005 4.005 0 0 0 3.65-2.36l2.626-5.837V13.8c.175-.387.264-.807.264-1.23v-1.579c0-1.645-1.355-3-3-3h-3.514c.256-.77.514-1.745.514-2.8 0-1.875-1.076-3.1-2.078-3.633A4.949 4.949 0 0 0 11.937 1Zm.562 2.184c.197.047.245.015.48.14.548.292 1.02.664 1.02 1.867 0 1.546-.867 3.348-.867 3.348a1 1 0 0 0 .89 1.453H19c.565 0 1 .435 1 1v1.578c0 .142-.03.284-.088.41l-2.627 5.84A1.995 1.995 0 0 1 15.461 20H9c-.565 0-1-.435-1-1v-8.28c0-.829.343-1.62.947-2.187l2.063-1.935a1 1 0 0 0 .242-.352L12.5 3.184ZM4.5 10h1c.287 0 .5.213.5.5v9c0 .287-.213.5-.5.5h-1a.487.487 0 0 1-.5-.5v-9c0-.287.213-.5.5-.5Z"
        />
      </svg>
    </span>
  );
};

export default IconThumbsup;
