import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconTiktok = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeMiterlimit={10}
          strokeWidth={2}
          d="M18 20H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2Z"
        />
        <path
          stroke="currentColor"
          strokeLinecap="round"
          strokeMiterlimit={3}
          strokeWidth={2}
          d="M13 8v6a2 2 0 1 1-3.007-1.728M16 11c-1 0-3-1.359-3-3"
        />
      </svg>
    </span>
  );
};

export default IconTiktok;
