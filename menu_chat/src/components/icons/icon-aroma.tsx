import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconAroma = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M9.016.986a1 1 0 0 0-1.037.963l-.028.553c-.249 2.374-1.323 3.9-2.431 5.164-.555.632-1.118 1.184-1.59 1.756-.473.572-.924 1.211-.924 2.07 0 1.925 1.58 3.508 3.5 3.508 1.404 0 1.623.522 2.228 1.303.278.36.612.802 1.155 1.15.542.349 1.256.547 2.115.547.858 0 1.57-.198 2.113-.547.54-.347.874-.786 1.15-1.146.61-.783.827-1.307 2.235-1.307 1.92 0 3.5-1.582 3.5-3.508 0-.863-.45-1.523-.926-2.097-.475-.575-1.039-1.12-1.594-1.743-1.108-1.245-2.178-2.741-2.427-5.136l-.03-.569a1 1 0 0 0-1.998.106l.032.613c0 .016.002.033.004.049.3 2.92 1.7 4.89 2.925 6.267.613.689 1.182 1.246 1.547 1.688.366.441.467.712.467.822a1.49 1.49 0 0 1-1.5 1.508c-2.114 0-3.24 1.343-3.812 2.08l-.005.004c-.266.346-.45.557-.65.686-.2.128-.45.23-1.031.23s-.836-.102-1.035-.23c-.2-.129-.382-.34-.649-.686l-.004-.004C9.744 14.343 8.62 13 6.506 13c-.838 0-1.5-.66-1.5-1.508 0-.075.098-.351.467-.797.368-.446.937-1.011 1.548-1.709 1.224-1.395 2.619-3.381 2.92-6.283l.004-.05.032-.602A1 1 0 0 0 9.016.986ZM6.484 17a1 1 0 0 0-.69.291l-1.5 1.494a1 1 0 0 0 1.21 1.576v1.653a1 1 0 1 0 2 0v-1.649a1 1 0 0 0 1.203-1.58l-1.5-1.494A1 1 0 0 0 6.484 17Zm11.002 0a1 1 0 0 0-.69.291l-1.5 1.494a1 1 0 0 0 1.206 1.578v1.65a1 1 0 1 0 2 0v-1.652a1 1 0 0 0 1.207-1.576l-1.5-1.494a1 1 0 0 0-.723-.291Z"
        />
      </svg>
    </span>
  );
};

export default IconAroma;
