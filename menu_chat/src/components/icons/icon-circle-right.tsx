import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconCircleRight = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 2C6.489 2 2 6.489 2 12s4.489 10 10 10 10-4.489 10-10S17.511 2 12 2Zm0 2c4.43 0 8 3.57 8 8s-3.57 8-8 8-8-3.57-8-8 3.57-8 8-8Zm.99 3.99a1 1 0 0 0-.697 1.717L13.586 11H8a1 1 0 1 0 0 2h5.586l-1.293 1.293a1 1 0 1 0 1.414 1.414l2.912-2.912a.999.999 0 0 0 .004-1.588l-.006-.004-2.91-2.91a1 1 0 0 0-.717-.303Z"
        />
      </svg>
    </span>
  );
};

export default IconCircleRight;
