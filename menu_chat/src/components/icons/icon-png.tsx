import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconPNG = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M13.984 1.986A1 1 0 0 0 13.84 2H6.5C5.131 2 4 3.13 4 4.5V11a1 1 0 1 0 2 0V4.5c0-.287.213-.5.5-.5H13v4a1 1 0 0 0 1 1h4v2a1.002 1.002 0 0 0 1.712.716A1 1 0 0 0 20 11V8.168a1 1 0 0 0-.377-.961l-.006-.004-4.826-4.826a1 1 0 0 0-.807-.39ZM15 5.414 16.586 7H15V5.414Zm4.1 8.486c-1.66 0-3.067 1.325-3.067 2.985v2.23c0 1.659 1.406 2.985 3.067 2.985 1.373 0 2.332-.85 2.707-1.614.374-.763.361-1.494.361-1.494v-.303a1 1 0 0 0-1-1h-1.379a1.001 1.001 0 1 0 0 2h.066c-.115.214-.18.41-.755.41-.624 0-1.067-.45-1.067-.984v-2.23c0-.533.444-.985 1.067-.985.4 0 .726.201.908.483a1 1 0 1 0 1.68-1.088A3.08 3.08 0 0 0 19.1 13.9Zm-4.631.1a1 1 0 0 0-.986 1.016v2.767l-2.311-3.338a1 1 0 0 0-1.822.57v5.97a1 1 0 1 0 2 0v-2.768l2.31 3.338a1 1 0 0 0 1.822-.57v-5.97A1 1 0 0 0 14.47 14Zm-10.42.016a1 1 0 0 0-1 1v3.82a1 1 0 0 0 0 .336v1.812a1 1 0 1 0 2 0v-.976h.97c1.585 0 2.932-1.264 2.932-2.85v-.295c0-1.586-1.348-2.847-2.931-2.847H4.049Zm1 2h.97c.55 0 .932.392.932.847v.295c0 .455-.38.85-.931.85h-.971v-1.992Z"
        />
      </svg>
    </span>
  );
};

export default IconPNG;
