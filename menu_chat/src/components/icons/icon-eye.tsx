import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconEye = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M12 3C4.238 3 .105 11.553.105 11.553a1 1 0 0 0 0 .894S4.238 21 12 21s11.895-8.553 11.895-8.553a1 1 0 0 0 0-.894S19.762 3 12 3Zm0 2c5.863 0 9.348 6.21 9.78 7-.432.79-3.917 7-9.78 7-5.863 0-9.348-6.21-9.78-7 .432-.79 3.917-7 9.78-7Zm0 2c-1.583 0-2.898.63-3.748 1.586C7.402 9.542 7 10.778 7 12s.402 2.458 1.252 3.414S10.417 17 12 17c1.583 0 2.898-.63 3.748-1.586C16.598 14.458 17 13.222 17 12s-.402-2.458-1.252-3.414S13.583 7 12 7Zm0 2c1.083 0 1.769.37 2.252.914.483.544.748 1.308.748 2.086 0 .778-.265 1.542-.748 2.086-.483.544-1.169.914-2.252.914-1.083 0-1.769-.37-2.252-.914C9.265 13.542 9 12.778 9 12c0-.778.265-1.542.748-2.086C10.231 9.37 10.917 9 12 9Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconEye;
