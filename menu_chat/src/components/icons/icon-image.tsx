import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconImage = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M5.75 3A2.765 2.765 0 0 0 3 5.75v12.5A2.765 2.765 0 0 0 5.75 21h12.5A2.765 2.765 0 0 0 21 18.25V5.75A2.765 2.765 0 0 0 18.25 3H5.75Zm0 2h12.5c.425 0 .75.325.75.75v10.836l-3.584-3.584a2.015 2.015 0 0 0-2.832.004l-.58.584-1.59-1.59c-.386-.386-.9-.58-1.414-.58-.514 0-1.028.193-1.414.58L5 14.586V5.75c0-.425.325-.75.75-.75Zm9.75 2a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3ZM9 13.414h.002l3.291 3.293a1 1 0 1 0 1.414-1.414l-.289-.29.584-.587 4.53 4.53a.774.774 0 0 1-.282.054H5.75a.737.737 0 0 1-.75-.75v-.836l4-4Z"
        />
      </svg>
    </span>
  );
};

export default IconImage;
