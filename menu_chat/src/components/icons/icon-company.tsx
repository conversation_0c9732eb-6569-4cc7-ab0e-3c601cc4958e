import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconCompany = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          d="M7 1.00001C6.448 1.00001 6 1.44801 6 2.00001V3.00001C4.895 3.00001 4 3.89501 4 5.00001V21C4 21.552 4.448 22 5 22H19C19.552 22 20 21.552 20 21V5.00001C20 3.89501 19.105 3.00001 18 3.00001V2.00001C18 1.44801 17.552 1.00001 17 1.00001H7ZM8 3.00001H16V5.00001H18V20H14V18H10V20H6V5.00001H8V3.00001ZM9 6.00001V8.00001H11V6.00001H9ZM13 6.00001V8.00001H15V6.00001H13ZM9 10V12H11V10H9ZM13 10V12H15V10H13ZM9 14V16H11V14H9ZM13 14V16H15V14H13Z"
          fill="currentColor"
        />
      </svg>
    </span>
  );
};

export default IconCompany;
