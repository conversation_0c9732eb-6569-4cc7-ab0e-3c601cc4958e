import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconArrowUp = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M4 17a1 1 0 0 0 .697-.307L11.99 9.4l7.293 7.293a1 1 0 1 0 1.414-1.414l-8-8a1 1 0 0 0-1.414 0l-8 8A1 1 0 0 0 4 16.999Z"
        />
      </svg>
    </span>
  );
};

export default IconArrowUp;
