import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconQuestion = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 2C6.489 2 2 6.489 2 12s4.489 10 10 10 10-4.489 10-10S17.511 2 12 2Zm0 2c4.43 0 8 3.57 8 8s-3.57 8-8 8-8-3.57-8-8 3.57-8 8-8Zm0 2c-1.732 0-3.208 1.118-3.758 2.666a1 1 0 1 0 1.885.668A1.979 1.979 0 0 1 12 8c1.117 0 2 .883 2 2s-.883 2-2 2a1 1 0 0 0-1 1v1a1 1 0 1 0 2 0v-.203c1.706-.457 3-1.953 3-3.797 0-2.197-1.803-4-4-4Zm0 10a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"
        />
      </svg>
    </span>
  );
};

export default IconQuestion;
