import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconSparkle = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          stroke="url(#1)"
          strokeLinejoin="round"
          strokeWidth="1.5"
          d="m15 2 .539 2.392a5.385 5.385 0 0 0 4.07 4.07L22 9l-2.392.539a5.385 5.385 0 0 0-4.07 4.07L15 16l-.539-2.392a5.385 5.385 0 0 0-4.07-4.07L8 9l2.392-.539a5.385 5.385 0 0 0 4.07-4.07L15 2Z"
        />
        <path
          stroke="url(#2)"
          strokeLinejoin="round"
          strokeWidth="1.5"
          d="m7 12 .385 1.708a3.846 3.846 0 0 0 2.907 2.907L12 17l-1.708.385a3.846 3.846 0 0 0-2.907 2.907L7 22l-.385-1.708a3.846 3.846 0 0 0-2.907-2.907L2 17l1.708-.385a3.846 3.846 0 0 0 2.907-2.907L7 12Z"
        />
        <defs>
          <linearGradient
            id="1"
            x1="2"
            x2="23.076"
            y1="22"
            y2="20.794"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset=".154" stopColor="#ACA572" />
            <stop offset=".432" stopColor="#FFC857" />
            <stop offset=".764" stopColor="#AFAC83" />
          </linearGradient>
          <linearGradient
            id="2"
            x1="2"
            x2="23.076"
            y1="22"
            y2="20.794"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset=".154" stopColor="#ACA572" />
            <stop offset=".432" stopColor="#FFC857" />
            <stop offset=".764" stopColor="#AFAC83" />
          </linearGradient>
        </defs>
      </svg>
    </span>
  );
};

export default IconSparkle;
