import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconTaste2 = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M6.545 2A3.65 3.65 0 0 0 2.91 5.636c0 1.015.425 1.934 1.103 2.596A4.55 4.55 0 0 0 2 12v9.09a.91.91 0 1 0 1.818 0V12a2.714 2.714 0 0 1 2.727-2.727c1.195 0 2.247.572 2.91 1.456.57.757 2.722 3.63 2.726 3.634a.91.91 0 0 0 1.373.098l4.526-4.528a.97.97 0 0 0-.337-1.59l-3.658-1.41a.91.91 0 0 0-.654 1.697l2.44.941-2.047 2.047a1 1 0 0 1-1.508-.107l-1.408-1.876a5.474 5.474 0 0 0-1.738-1.5 3.615 3.615 0 0 0 1.012-2.499A3.65 3.65 0 0 0 6.545 2Zm0 1.818c1.016 0 1.819.803 1.819 1.818a1.804 1.804 0 0 1-1.819 1.819 1.804 1.804 0 0 1-1.818-1.819c0-1.015.803-1.818 1.818-1.818Zm14.546 7.273a.91.91 0 0 0-.91.909v1.818a.91.91 0 1 0 1.819 0V12a.91.91 0 0 0-.91-.91Zm-3.637 1.818a.91.91 0 1 0 0 1.818.91.91 0 0 0 0-1.818Zm-8.181.91a.91.91 0 0 0-.91.908v6.364a.91.91 0 1 0 1.819 0v-6.364a.91.91 0 0 0-.91-.909Zm7.273 2.726a.91.91 0 0 0-.91.91H12.91a.91.91 0 0 0 0 1.818h2.727V21a1 1 0 0 0 1 1H21a1 1 0 0 0 1-1v-3.454a1 1 0 0 0-1-1h-4.454Zm.909 1.819h2.727v1.818h-2.727v-1.818Z"
        />
      </svg>
    </span>
  );
};

export default IconTaste2;
