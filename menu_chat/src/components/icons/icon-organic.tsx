import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconOrganic = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <g clipPath="url(#a)">
          <g clipPath="url(#b)">
            <path
              fill="currentColor"
              d="M5.031 1.999a1 1 0 0 0-.582.16s-.936.622-1.783 1.877C1.819 5.29.994 7.244.994 9.944c0 2.65.86 4.648 2.13 6.022 1.074 1.163 2.398 1.86 3.68 2.304C6.339 19.362 6 20.561 6 22a1 1 0 1 0 2 0 8.33 8.33 0 0 1 .482-2.844c1.137.415 5.434 1.868 9.368-.016 2.912-1.393 4.487-3.4 5.289-5.056.801-1.656.861-3.047.861-3.047a1 1 0 0 0-.16-.582s-.622-.936-1.877-1.783c-1.256-.848-3.208-1.672-5.908-1.672-1.227 0-2.312.185-3.262.508C11.425 5.01 9.605 3.599 8.078 2.86 6.422 2.06 5.031 2 5.031 2Zm.266 2.1c.229.017.793.021 1.91.562 1.197.58 2.634 1.65 3.785 3.717-.194.127-.38.26-.557.402a7.887 7.887 0 0 0-1.714 1.936C7.343 9.568 6.984 7.819 6.984 7.819a1 1 0 1 0-1.966.36s.472 2.725 2.796 4.367c-.013.037-.033.074-.046.111a13.061 13.061 0 0 0-.74 3.568c-.897-.364-1.755-.88-2.436-1.617-.935-1.011-1.598-2.43-1.598-4.664 0-2.3.676-3.822 1.328-4.789.524-.776.811-.937.975-1.057Zm10.758 4.9c2.3 0 3.822.675 4.789 1.328.776.524.937.813 1.056.976-.018.23-.022.792-.562 1.909-.637 1.316-1.854 2.928-4.354 4.125-2.7 1.292-6.161.389-7.533-.014.925-1.255 2.182-2.147 3.496-2.805 2.555-1.277 5.143-1.523 5.143-1.523a1 1 0 0 0-.18-1.992s-2.912.254-5.857 1.726c-.913.457-1.808 1.105-2.655 1.852.094-.433.089-.794.25-1.24.41-1.128 1.05-2.205 2.04-2.998.99-.794 2.342-1.344 4.367-1.344Z"
            />
          </g>
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
          <clipPath id="b">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconOrganic;
