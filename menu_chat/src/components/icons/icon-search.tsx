import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconSearch = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M10 2c-4.406 0-8 3.594-8 8 0 4.406 3.594 8 8 8a7.948 7.948 0 0 0 4.896-1.69l5.397 5.397a1 1 0 1 0 1.414-1.414l-5.396-5.396A7.948 7.948 0 0 0 18 10c0-4.406-3.594-8-8-8Zm0 2c3.326 0 6 2.674 6 6s-2.674 6-6 6-6-2.674-6-6 2.674-6 6-6Z"
        />
      </svg>
    </span>
  );
};

export default IconSearch;
