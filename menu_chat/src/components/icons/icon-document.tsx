import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconDocument = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M6.5 2C5.13 2 4 3.13 4 4.5v15C4 20.87 5.13 22 6.5 22h11c1.37 0 2.5-1.13 2.5-2.5V8a1 1 0 0 0-.293-.707l-5-5A1 1 0 0 0 14 2H6.5Zm0 2H13v4a1 1 0 0 0 1 1h4v10.5c0 .287-.213.5-.5.5h-11a.487.487 0 0 1-.5-.5v-15c0-.287.213-.5.5-.5ZM15 5.414 16.586 7H15V5.414ZM9 10a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2H9Zm0 3a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2H9Zm0 3a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2H9Z"
        />
      </svg>
    </span>
  );
};

export default IconDocument;
