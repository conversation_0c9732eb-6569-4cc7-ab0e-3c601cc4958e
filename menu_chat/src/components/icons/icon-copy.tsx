import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconCopy = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M3.75 1.5A2.258 2.258 0 0 0 1.5 3.75v12A2.258 2.258 0 0 0 3.75 18H6v.75A2.258 2.258 0 0 0 8.25 21h12a2.258 2.258 0 0 0 2.25-2.25v-12a2.258 2.258 0 0 0-2.25-2.25H18v-.75a2.258 2.258 0 0 0-2.25-2.25h-12Zm0 1.5h12a.74.74 0 0 1 .75.75v12a.74.74 0 0 1-.75.75h-12a.74.74 0 0 1-.75-.75v-12A.74.74 0 0 1 3.75 3ZM18 6h2.25a.74.74 0 0 1 .75.75v12a.74.74 0 0 1-.75.75h-12a.74.74 0 0 1-.75-.75V18h8.25A2.258 2.258 0 0 0 18 15.75V6Z"
        />
      </svg>
    </span>
  );
};

export default IconCopy;
