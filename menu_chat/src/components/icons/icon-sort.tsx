import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconSort = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M4 4a1 1 0 1 0 0 2h12a1 1 0 1 0 0-2H4Zm13.984 4.986A1 1 0 0 0 17 10v7.586l-1.293-1.293a1 1 0 1 0-1.414 1.414l3 3a1 1 0 0 0 1.414 0l3-3a1 1 0 1 0-1.414-1.414L19 17.586V10a1 1 0 0 0-1.016-1.014ZM4 9a1 1 0 1 0 0 2h9a1 1 0 1 0 0-2H4Zm0 5a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2H4Zm0 5a1 1 0 1 0 0 2h3a1 1 0 1 0 0-2H4Z"
        />
      </svg>
    </span>
  );
};

export default IconSort;
