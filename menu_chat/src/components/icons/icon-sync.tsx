import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconSync = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 2C6.489 2 2 6.489 2 12s4.489 10 10 10 10-4.489 10-10S17.511 2 12 2Zm0 2c4.43 0 8 3.57 8 8s-3.57 8-8 8-8-3.57-8-8 3.57-8 8-8Zm-1 2a1 1 0 1 0 0 2 3.973 3.973 0 0 1 3.955 3.541l-.248-.248a1 1 0 1 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0l2-2a1 1 0 1 0-1.414-1.414l-.313.313C16.776 8.486 14.168 6 11 6ZM7.984 9a1 1 0 0 0-.691.293l-2 2a1 1 0 1 0 1.414 1.414l.363-.363C7.26 15.477 9.82 18 13 18a1 1 0 1 0 0-2c-2.035 0-3.634-1.523-3.896-3.482l.189.189a1 1 0 1 0 1.414-1.414l-2-2A1 1 0 0 0 7.984 9Z"
        />
      </svg>
    </span>
  );
};

export default IconSync;
