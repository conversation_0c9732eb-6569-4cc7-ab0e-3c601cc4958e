import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconIngredientsSolid = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M13.5 1c-.817 0-1.5.683-1.5 1.5v1.377c-.054.115-.291.944-.506 1.56A4.728 4.728 0 0 0 9.5 5c-.119 0-.232.01-.348.018-.252-.658-.745-1.453-1.72-1.92a1 1 0 1 0-.864 1.804c.332.16.52.433.645.692-.413.23-.79.502-1.086.836C5.36 7.292 5 8.403 5 9.5c0 .169.067.331.084.5H2a1 1 0 1 0 0 2v2c0 .093.012.185.037.273l2 7c.123.43.517.727.963.727h14c.446 0 .84-.296.963-.725l2-7c.025-.089.037-.182.037-.275v-2a1 1 0 1 0 0-2h-2.652C19.034 6.407 18.16 4.185 18 3.84V2.5c0-.817-.683-1.5-1.5-1.5h-3Zm.5 3h2a1 1 0 0 0 .105.447s.909 2.115 1.233 5.553h-3.422c.017-.169.084-.331.084-.5 0-.935-.323-1.85-.875-2.643.363-1.39.77-2.41.77-2.41A1 1 0 0 0 14 4ZM9.5 7c.874 0 1.412.29 1.809.707l.002.002a.994.994 0 0 0 .072.066c.392.45.617 1.079.617 1.725 0 .187-.144.319-.18.5H7.18C7.144 9.819 7 9.687 7 9.5c0-.653.223-1.292.623-1.742a2.13 2.13 0 0 1 .883-.59h.004a1 1 0 0 0 .379-.115h.002C9.075 7.018 9.276 7 9.5 7Zm2.5 7a1 1 0 0 1 1 1v4a1 1 0 1 1-2 0v-4a1 1 0 0 1 1-1Zm-4.623.008a.995.995 0 0 1 1.115.869l.5 4a.999.999 0 1 1-1.984.246l-.5-4a.999.999 0 0 1 .869-1.115Zm9.246 0a1 1 0 0 1 .87 1.115l-.5 4a1 1 0 1 1-1.985-.246l.5-4a.998.998 0 0 1 1.115-.87Z"
        />
      </svg>
    </span>
  );
};

export default IconIngredientsSolid;
