import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconEmail = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          fill="currentColor"
          d="M3 4a1 1 0 0 0-1 1v2.15a1 1 0 0 0 0 .31V19a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V7.506a.997.997 0 0 0 0-.404V5a1 1 0 0 0-1-1H3Zm1 2h16v.766l-8 5.05-8-5.05V6Zm0 3.13 7.467 4.716a1 1 0 0 0 1.066 0L20 9.13V18H4V9.13Z"
        />
      </svg>
    </span>
  );
};

export default IconEmail;
