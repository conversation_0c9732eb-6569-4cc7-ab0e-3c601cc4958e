import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconAttachment = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
         xmlns="http://www.w3.org/2000/svg"
         width={size}
         height={size}
         fill="none"
         viewBox="0 0 24 24"
      >
        <path
          d="M10 2C7.25048 2 5 4.25048 5 7V15.0039C5 18.856 8.143 22 11.9961 22C15.726 22 18.2925 19.4836 18.8789 16.2559L19.9844 10.1797C20.008 10.0504 20.0059 9.91777 19.9782 9.78931C19.9505 9.66085 19.8978 9.53909 19.8231 9.431C19.7484 9.32291 19.6531 9.23059 19.5427 9.15932C19.4323 9.08805 19.309 9.03922 19.1797 9.01562C19.0504 8.99203 18.9178 8.99412 18.7893 9.02179C18.6608 9.04946 18.5391 9.10216 18.431 9.17688C18.3229 9.25161 18.2306 9.34689 18.1593 9.45729C18.088 9.56769 18.0392 9.69104 18.0156 9.82031L16.9102 15.8984C16.4606 18.3727 14.8921 20 11.9961 20C9.22319 20 7 17.7759 7 15.0039V7C7 5.33152 8.33152 4 10 4C11.6685 4 13 5.33152 13 7V15C13 15.565 12.565 16 12 16C11.435 16 11 15.565 11 15V8C11.0019 7.86749 10.9774 7.73593 10.928 7.61296C10.8786 7.48999 10.8052 7.37807 10.7122 7.2837C10.6191 7.18933 10.5082 7.11439 10.386 7.06324C10.2637 7.0121 10.1325 6.98576 10 6.98576C9.86748 6.98576 9.73627 7.0121 9.61401 7.06324C9.49176 7.11439 9.38088 7.18933 9.28784 7.2837C9.1948 7.37807 9.12143 7.48999 9.07202 7.61296C9.02261 7.73593 8.99813 7.86749 9 8V15C9 16.645 10.355 18 12 18C13.645 18 15 16.645 15 15V7C15 4.25048 12.7495 2 10 2Z"
          fill="#4B4830"
        />
      </svg>
    </span>
  );
};

export default IconAttachment;
