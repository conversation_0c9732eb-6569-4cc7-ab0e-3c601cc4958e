import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconClose = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M18.354 4.623a1 1 0 0 0-.698.307L12 10.586 6.344 4.93A1 1 0 1 0 4.93 6.344L10.586 12 4.93 17.656a1 1 0 1 0 1.414 1.414L12 13.414l5.656 5.656a1 1 0 1 0 1.414-1.414L13.414 12l5.656-5.656a1 1 0 0 0-.716-1.72Z"
        />
      </svg>
    </span>
  );
};

export default IconClose;
