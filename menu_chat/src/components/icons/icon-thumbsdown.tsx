import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconThumbsdown = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12.063 22a1 1 0 0 0 .962-.624l1.493-3.666 1.904-1.785c.42-.395.716-.884.974-1.387.355.194.676.461 1.104.461h1c1.37 0 2.5-1.13 2.5-2.5v-9C22 2.13 20.87 1 19.5 1h-1c-.679 0-1.295.284-1.748.73C16.246 1.342 15.681 1 15 1H8.54a4.005 4.005 0 0 0-3.65 2.36L2.265 9.197v.002A2.996 2.996 0 0 0 2 10.429v1.578c0 1.645 1.355 3 3 3h3.514c-.256.77-.514 1.746-.514 2.8 0 1.876 1.076 3.1 2.078 3.634 1.002.534 1.985.558 1.985.558Zm-.563-2.184c-.197-.048-.245-.015-.48-.14-.548-.293-1.02-.665-1.02-1.868 0-1.546.867-3.348.867-3.348a1 1 0 0 0-.89-1.453H5c-.565 0-1-.435-1-1V10.43c0-.142.03-.284.088-.41l2.627-5.84a1.995 1.995 0 0 1 1.824-1.18H15c.565 0 1 .435 1 1v8.28c0 .829-.343 1.62-.947 2.187l-2.063 1.936a1 1 0 0 0-.242.351L11.5 19.816Zm8-6.817h-1a.487.487 0 0 1-.5-.5v-9c0-.287.213-.5.5-.5h1c.287 0 .5.213.5.5v9c0 .287-.213.5-.5.5Z"
        />
      </svg>
    </span>
  );
};

export default IconThumbsdown;
