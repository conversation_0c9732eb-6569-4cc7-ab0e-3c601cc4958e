import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconMore = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M14 12a2 2 0 1 0-4 0 2 2 0 0 0 4 0Zm0 8a2 2 0 1 0-4 0 2 2 0 0 0 4 0Zm0-16a2 2 0 1 0-4 0 2 2 0 0 0 4 0Z"
        />
      </svg>
    </span>
  );
};

export default IconMore;
