import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconRetail = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <mask
            id="b"
            width="24"
            height="24"
            x="0"
            y="0"
            maskUnits="userSpaceOnUse"
            style={{ maskType: "luminance" }}
          >
            <path fill="#fff" d="M24 0H0v24h24V0Z" />
          </mask>
          <g mask="url(#b)">
            <path
              fill="currentColor"
              d="M1 2a1 1 0 1 0 0 2h.787c.243 0 .438.158.488.395v.002l2.409 11.232A3.01 3.01 0 0 0 7.617 18H19a1 1 0 1 0 0-2H7.617a.992.992 0 0 1-.978-.79L6.379 14h12.182a3.01 3.01 0 0 0 2.941-2.412l1.479-7.393A1.001 1.001 0 0 0 22 3H3.773a2.502 2.502 0 0 0-1.987-1H1Zm3.451 3h16.33l-1.24 6.195a.99.99 0 0 1-.98.805H5.95l-1.5-7Zm4.05 14a1.5 1.5 0 1 0-.001 3 1.5 1.5 0 0 0 0-3Zm9 0a1.5 1.5 0 1 0-.001 3 1.5 1.5 0 0 0 0-3Z"
            />
          </g>
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconRetail;
