import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconRight = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M10.886 5a.899.899 0 0 0-.496.149.877.877 0 0 0-.327.396.86.86 0 0 0 .204.955l4.7 4.624H2.902a.9.9 0 0 0-.636.252.874.874 0 0 0-.265.622.862.862 0 0 0 .265.622.89.89 0 0 0 .636.252h12.067l-4.701 4.624a.876.876 0 0 0-.273.621.862.862 0 0 0 .26.627.891.891 0 0 0 .637.256.901.901 0 0 0 .632-.269l6.217-6.115a.867.867 0 0 0 0-1.236l-6.217-6.116A.89.89 0 0 0 10.886 5ZM22 18a1 1 0 1 1-2 0V6a1 1 0 1 1 2 0v12Z"
        />
      </svg>
    </span>
  );
};

export default IconRight;
