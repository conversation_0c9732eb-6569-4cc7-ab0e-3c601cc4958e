import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconShare = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M14.027 2A1 1 0 0 0 13 3v4.08c-4.632.203-7.94 1.691-9.998 4.006C.792 13.573 0 16.833 0 20a1 1 0 0 0 1.895.447s.615-1.266 2.455-2.646c1.7-1.275 4.466-2.534 8.65-2.727V19a1 1 0 0 0 1.664.748l9-8a1 1 0 0 0 0-1.496l-9-8A1 1 0 0 0 14.027 2ZM15 5.227 21.494 11 15 16.773V14a1 1 0 0 0-1-1c-5.2 0-8.69 1.58-10.85 3.2-.33.247-.44.452-.712.698.366-1.66.948-3.232 2.06-4.484C6.288 10.401 9.2 9 14 9a1 1 0 0 0 1-1V5.227Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconShare;
