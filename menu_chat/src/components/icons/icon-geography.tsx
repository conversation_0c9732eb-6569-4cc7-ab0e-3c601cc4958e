import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconGeography = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          d="M12 2C6.56485 2 2.13297 6.36868 2.01169 11.7754C1.97852 11.9213 1.97852 12.0728 2.01169 12.2188C2.12987 17.6282 6.56288 22 12 22C17.4351 22 21.867 17.6313 21.9883 12.2246C22.0214 12.0787 22.0214 11.9272 21.9883 11.7812C21.8701 6.37182 17.4371 2 12 2ZM12 4C12.3854 4 12.6902 4.13331 13.0488 4.48633C13.4074 4.83934 13.7718 5.42422 14.0703 6.17969C14.5725 7.45082 14.8363 9.20226 14.9238 11H9.07615C9.16364 9.20226 9.42749 7.45082 9.92966 6.17969C10.2281 5.42422 10.5925 4.83934 10.9511 4.48633C11.3098 4.13331 11.6146 4 12 4ZM8.30857 4.90234C8.22614 5.07955 8.14406 5.25858 8.07029 5.44531C7.44031 7.03995 7.15791 9.0008 7.07419 11H4.06833C4.39884 8.33549 6.02744 6.08733 8.30857 4.90234ZM15.6914 4.90234C17.9725 6.08733 19.6011 8.33549 19.9316 11H16.9258C16.842 9.0008 16.5596 7.03995 15.9297 5.44531C15.8559 5.25858 15.7738 5.07955 15.6914 4.90234ZM4.06833 13H7.07419C7.15791 14.9992 7.44031 16.9601 8.07029 18.5547C8.14406 18.7414 8.22614 18.9204 8.30857 19.0977C6.02744 17.9127 4.39884 15.6645 4.06833 13ZM9.07615 13H14.9238C14.8363 14.7977 14.5725 16.5492 14.0703 17.8203C13.7718 18.5758 13.4074 19.1607 13.0488 19.5137C12.6902 19.8667 12.3854 20 12 20C11.6146 20 11.3098 19.8667 10.9511 19.5137C10.5925 19.1607 10.2281 18.5758 9.92966 17.8203C9.42749 16.5492 9.16364 14.7977 9.07615 13ZM16.9258 13H19.9316C19.6011 15.6645 17.9725 17.9127 15.6914 19.0977C15.7738 18.9204 15.8559 18.7414 15.9297 18.5547C16.5596 16.9601 16.842 14.9992 16.9258 13Z"
          fill="currentColor"
        />
      </svg>
    </span>
  );
};

export default IconGeography;
