import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconIncrease = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M18.98 2.99a1.003 1.003 0 0 0-.11.01H15a1 1 0 1 0 0 2h1.586l-3.783 3.783-3.356-1.678a1 1 0 0 0-1.072.114l-5 4a1 1 0 1 0 1.25 1.562l4.5-3.601 3.428 1.715a1 1 0 0 0 1.154-.188L18 6.414V8a1 1 0 1 0 2 0V4.127a1 1 0 0 0-1.02-1.137Zm.004 7.996A1 1 0 0 0 18 12v8a1 1 0 1 0 2 0v-8a1 1 0 0 0-1.016-1.014Zm-10 3A1 1 0 0 0 8 15v5a1 1 0 1 0 2 0v-5a1 1 0 0 0-1.016-1.014Zm5 2A1 1 0 0 0 13 17v3a1 1 0 1 0 2 0v-3a1 1 0 0 0-1.016-1.014Zm-10 1A1 1 0 0 0 3 18v2a1 1 0 1 0 2 0v-2a1 1 0 0 0-1.016-1.014Z"
        />
      </svg>
    </span>
  );
};

export default IconIncrease;
