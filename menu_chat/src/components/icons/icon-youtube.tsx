import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconYoutube = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 3c-2.866 0-5.048.2-6.578.418H5.42C3.456 3.698 2 5.402 2 7.378v9.245c0 1.977 1.458 3.68 3.422 3.959C6.952 20.8 9.134 21 12 21s5.049-.2 6.578-.418h.002c1.964-.28 3.42-1.984 3.42-3.96V7.378c0-1.976-1.457-3.679-3.42-3.96h-.002C17.048 3.202 14.865 3 12 3Zm0 2c2.772 0 4.861.193 6.297.398.988.141 1.703.975 1.703 1.98v9.243c0 1.006-.715 1.84-1.703 1.98C16.86 18.807 14.772 19 12 19s-4.86-.194-6.297-.398A1.976 1.976 0 0 1 4 16.623V7.379c0-1.006.715-1.84 1.703-1.98C7.14 5.193 9.228 5 12 5Zm-1.082 3.813a.926.926 0 0 0-.918.925v4.526a.924.924 0 0 0 1.395.795l3.82-2.262a.926.926 0 0 0 0-1.592l-3.82-2.262a.912.912 0 0 0-.477-.13Z"
        />
      </svg>
    </span>
  );
};

export default IconYoutube;
