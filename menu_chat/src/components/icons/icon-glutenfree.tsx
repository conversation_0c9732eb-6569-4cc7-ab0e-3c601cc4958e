import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconGlutenfree = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M3.328 2.25A.762.762 0 1 0 2.25 3.328L20.672 21.75a.762.762 0 0 0 1.078-1.078l-5.203-5.203c.07-.103.111-.211.164-.305l.562-1.031-.023-.047c1.623-.832 2.68-2.66 2.672-4.078V8.93l-.235-.07C20.94 7.43 21 4.982 21 4.5V3h-1.5c-.486 0-2.933.073-4.383 1.383L15 4.172l-.773-.094c-.103-.012-.2 0-.305 0-1.544 0-3.232 1.166-4.008 2.672l-.047-.023-1.031.562c-.094.053-.202.094-.305.164L3.328 2.25ZM19.5 4.5s0 2.367-.984 3.422c-.695.685-1.436.867-2.016.867-.516 0-.89-.158-1.008-.234-.246-.243-.668-1.7.563-3C17.12 4.5 19.5 4.5 19.5 4.5Zm-5.578 1.078c.053 0 .09-.006.14 0 .13.234.32 1.565-.374 3.422-.472 1.245-1.336 1.878-1.688 1.922l-.727-.727c-.278-.51-.471-1.271-.234-2.203.331-1.31 1.808-2.414 2.883-2.414ZM6.609 9.773c-.035.082-.061.15-.093.235l-.891.492C4.649 11.036 3 12.56 3 14.883c0 .378.056 3.454 2.203 3.937.24.838 1.081 2.18 3.914 2.18 2.323 0 3.847-1.65 4.383-2.625l.492-.89.235-.094-1.172-1.196c-.226.05-.47.094-.727.094-2.054 0-2.508-.8-2.508-1.312 0-.302.343-.753.914-1.079l-.632-.632c-.326.57-.777.914-1.079.914-.512 0-1.312-.454-1.312-2.508 0-.258.044-.501.094-.727L6.609 9.773Zm10.664.118c.627 0 1.058.111 ************ 1.07-1.04 2.584-2.414 2.93a3.393 3.393 0 0 1-.82.117 2.838 2.838 0 0 1-1.43-.398c-.015-.01-.032-.015-.047-.024l-.61-.633c.021-.336.63-1.224 1.899-1.71a6.386 6.386 0 0 1 2.273-.422ZM6.352 11.812s.937 1.014.937 3.141c0 1.462-.964 2.438-1.476 2.438-.513 0-1.313-.454-1.313-2.508s1.852-3.07 1.852-3.07Zm2.695 4.899c2.127 0 3.14.937 3.14.937S11.171 19.5 9.117 19.5s-2.508-.8-2.508-1.313c0-.512.976-1.476 2.438-1.476Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconGlutenfree;
