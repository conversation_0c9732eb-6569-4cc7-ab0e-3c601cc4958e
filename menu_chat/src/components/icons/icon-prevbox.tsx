import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconPreviousBox = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M5.84 3c-1.645 0-3 1.355-3 3v12c0 1.645 1.355 3 3 3h12c1.645 0 3-1.355 3-3V6c0-1.645-1.355-3-3-3h-12Zm0 2h12c.563 0 1 .437 1 1v12c0 .563-.437 1-1 1h-12c-.563 0-1-.437-1-1V6c0-.563.437-1 1-1Zm6.98 1.99a1 1 0 0 0-.687.303l-4 4a1 1 0 0 0 0 1.414l4 4a1 1 0 1 0 1.414-1.414L10.254 12l3.293-3.293a1 1 0 0 0-.727-1.717Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconPreviousBox;
