import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconHighprotein = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <g clipPath="url(#a)">
          <path
            fill="currentColor"
            d="M12 2.074c-5.445 0-10 3.783-10 8.082 0 .548.087.77.17 1.156.342 1.6 2.02 2.482 3.62 2.14a60.64 60.64 0 0 1 2.231-.434C7.586 14.19 7 15.445 7 16.957c0 1.232.373 2.375.879 3.27.252.447.537.833.873 1.146.336.313.733.611 1.347.611H13.9c.614 0 1.012-.298 1.348-.611.336-.313.62-.7.873-1.146.506-.895.879-2.038.879-3.27 0-1.51-.584-2.765-1.02-3.938.764.134 1.514.281 2.229.434 1.602.343 3.28-.54 3.62-2.14.084-.388.17-.608.17-1.157 0-4.299-4.554-8.082-10-8.082Zm0 2c4.495 0 8 3.301 8 6.082 0 .407-.014.22-.125.739h-.002c-.075.35-.622.737-1.246.603-1.928-.412-4.182-.873-6.627-.873-2.446 0-4.7.46-6.627.871-.624.133-1.172-.254-1.246-.603h-.002c-.111-.518-.125-.331-.125-.737 0-2.78 3.505-6.082 8-6.082Zm0 8.551c.593 0 1.179.035 1.758.088.44 1.775 1.242 3.141 1.242 4.244 0 .806-.277 1.676-.621 2.285a2.975 2.975 0 0 1-.495.668c-.058.055-.095.068-.109.074h-3.551c-.014-.006-.05-.019-.109-.074a2.976 2.976 0 0 1-.494-.668C9.277 18.632 9 17.763 9 16.957c0-1.103.796-2.47 1.238-4.244.58-.053 1.167-.088 1.762-.088Z"
          />
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default IconHighprotein;
