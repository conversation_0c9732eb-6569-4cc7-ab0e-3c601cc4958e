import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconDecrease = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M3.979 2.99a1 1 0 0 0-.604 1.791l5 4a1 1 0 0 0 1.072.114l3.356-1.678L16.586 11H15a1 1 0 1 0 0 2h3.848A1 1 0 0 0 20 11.848V8a1.001 1.001 0 1 0-2 0v1.586l-4.293-4.293a1 1 0 0 0-1.154-.188L9.125 6.82l-4.5-3.601a1 1 0 0 0-.646-.229Zm.005 7.996A1 1 0 0 0 3 12v8a1 1 0 1 0 2 0v-8a1 1 0 0 0-1.016-1.014Zm10 3A1 1 0 0 0 13 15v5a1 1 0 1 0 2 0v-5a1 1 0 0 0-1.016-1.014Zm-5 2A1 1 0 0 0 8 17v3a1 1 0 1 0 2 0v-3a1 1 0 0 0-1.016-1.014Zm10 1A1 1 0 0 0 18 18v2a1 1 0 1 0 2 0v-2a1 1 0 0 0-1.016-1.014Z"
        />
      </svg>
    </span>
  );
};

export default IconDecrease;
