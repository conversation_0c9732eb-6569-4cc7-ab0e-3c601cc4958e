import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconRadioSelected = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          d="M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12Z"
          fill="#FFBB32"
        />
        <circle cx="12" cy="12" fill="white" r="2.5" />
      </svg>
    </span>
  );
};

export default IconRadioSelected;
