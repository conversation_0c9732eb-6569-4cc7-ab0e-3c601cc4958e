import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconStar = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12.012 1.488a1 1 0 0 0-.909.559l-2.857 5.79-6.39.929a1 1 0 0 0-.553 1.705l4.623 4.508-1.092 6.363a1 1 0 0 0 1.451 1.055L12 19.39l5.715 3.006a1 1 0 0 0 1.451-1.055l-1.092-6.363 4.623-4.508a1 1 0 0 0-.552-1.705l-6.391-.928-2.857-5.791a1 1 0 0 0-.885-.559ZM12 4.748l2.193 4.445a1 1 0 0 0 .752.547l4.907.713-3.55 3.46a1 1 0 0 0-.288.886l.838 4.883-4.387-2.307a1 1 0 0 0-.93 0l-4.387 2.307.838-4.883a1 1 0 0 0-.289-.887l-3.549-3.459 4.907-.713a1 1 0 0 0 .752-.547L12 4.748Z"
        />
      </svg>
    </span>
  );
};

export default IconStar;
