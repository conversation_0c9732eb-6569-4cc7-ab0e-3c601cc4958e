import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconLeft = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M13.114 19c.177 0 .35-.052.496-.149a.877.877 0 0 0 .327-.396.86.86 0 0 0-.204-.955l-4.7-4.624h12.066a.9.9 0 0 0 .636-.252.875.875 0 0 0 .265-.622.863.863 0 0 0-.265-.622.89.89 0 0 0-.636-.252H9.032l4.701-4.624a.875.875 0 0 0 .273-.621.862.862 0 0 0-.26-.627A.89.89 0 0 0 13.11 5a.9.9 0 0 0-.632.269L6.26 11.384a.867.867 0 0 0 0 1.236l6.217 6.115a.891.891 0 0 0 .637.265ZM2 6a1 1 0 0 1 2 0v12a1 1 0 1 1-2 0V6Z"
        />
      </svg>
    </span>
  );
};

export default IconLeft;
