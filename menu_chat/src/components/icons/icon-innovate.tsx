import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconInnovate = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          d="M12 2C7.59356 2 4 5.59356 4 10C4 12.5953 5.01205 14.3856 6.06055 15.5547C6.58479 16.1392 7.1117 16.5832 7.50781 16.9258C7.90392 17.2684 8.14521 17.558 8.13086 17.5293C8.33742 17.9424 8.67285 19.2206 8.85352 20.1074C9.07631 21.205 10.0558 22 11.1719 22H12.8281C13.9451 22 14.9246 21.203 15.1465 20.1055C15.326 19.2198 15.6616 17.9437 15.8691 17.5293C15.8548 17.558 16.0961 17.2684 16.4922 16.9258C16.8883 16.5832 17.4152 16.1392 17.9395 15.5547C18.9879 14.3856 20 12.5953 20 10C20 5.59356 16.4064 2 12 2ZM12 4C15.3256 4 18 6.67444 18 10C18 12.1027 17.2769 13.3 16.4512 14.2207C16.0383 14.6811 15.594 15.0571 15.1836 15.4121C14.9577 15.6075 14.737 15.7856 14.5332 16H9.4668C9.26304 15.7856 9.04234 15.6075 8.81641 15.4121C8.40599 15.0571 7.96171 14.6811 7.54883 14.2207C6.72307 13.3 6 12.1027 6 10C6 6.67444 8.67444 4 12 4ZM11.623 5.33203C11.417 5.33203 11.2402 5.47769 11.1992 5.67969L11.0664 6.33008C10.4974 6.48908 9.98812 6.78745 9.57812 7.18945L8.94922 6.97852C8.75422 6.91352 8.5405 6.99583 8.4375 7.17383L8.05859 7.82617C7.95559 8.00417 7.99248 8.23119 8.14648 8.36719L8.64453 8.80664C8.57453 9.08164 8.53125 9.36902 8.53125 9.66602C8.53125 9.96302 8.57353 10.2504 8.64453 10.5254L8.14648 10.9648C7.99248 11.1018 7.95559 11.3279 8.05859 11.5059L8.43555 12.1582C8.53855 12.3362 8.75227 12.4195 8.94727 12.3535L9.57617 12.1426C9.98617 12.5446 10.4955 12.843 11.0645 13.002L11.1973 13.6523C11.2383 13.8543 11.4151 14 11.6211 14H12.377C12.583 14 12.7598 13.8543 12.8008 13.6523L12.9336 13.002C13.5026 12.843 14.0119 12.5446 14.4219 12.1426L15.0508 12.3535C15.2458 12.4185 15.4595 12.3362 15.5625 12.1582L15.9395 11.5059C16.0425 11.3279 16.0056 11.1008 15.8516 10.9648L15.3555 10.5254C15.4255 10.2504 15.4688 9.96302 15.4688 9.66602C15.4688 9.36902 15.4265 9.08164 15.3555 8.80664L15.8535 8.36719C16.0075 8.23119 16.0444 8.00417 15.9414 7.82617L15.5645 7.17383C15.4615 6.99583 15.2477 6.91252 15.0527 6.97852L14.4238 7.18945C14.0138 6.78745 13.5045 6.48908 12.9355 6.33008L12.8027 5.67969C12.7617 5.47769 12.5849 5.33203 12.3789 5.33203H11.623ZM12 7.98242C12.93 7.98242 13.6836 8.73602 13.6836 9.66602C13.6836 10.596 12.93 11.3496 12 11.3496C11.07 11.3496 10.3164 10.596 10.3164 9.66602C10.3164 8.73602 11.07 7.98242 12 7.98242ZM10.416 18H13.584C13.4197 18.5896 13.2921 19.1926 13.1875 19.709C13.1514 19.8875 13.0132 20 12.8281 20H11.1719C10.9885 20 10.8484 19.8865 10.8125 19.709V19.707C10.7074 19.1913 10.5803 18.5893 10.416 18Z"
          fill="currentColor"
        />
      </svg>
    </span>
  );
};

export default IconInnovate;
