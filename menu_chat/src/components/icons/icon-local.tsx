import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconLocal = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge("inline-flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          fill="currentColor"
          d="M12 1c-4.959 0-9 4.041-9 9 0 3.495 2.209 6.391 4.303 8.424 2.094 2.032 4.19 3.262 4.19 3.262a1 1 0 0 0 1.015 0s2.095-1.23 4.19-3.262C18.79 16.39 21 13.494 21 10c0-4.959-4.041-9-9-9Zm0 2c3.877 0 7 3.123 7 7 0 2.623-1.791 5.139-3.697 6.988-1.652 1.604-2.892 2.325-3.303 2.578-.41-.253-1.65-.974-3.303-2.578C6.791 15.138 5 12.623 5 10c0-3.877 3.123-7 7-7Zm-.012 3.846a1 1 0 0 0-.543.168L8.783 8.787A1 1 0 0 0 9 10.572v1.668c0 .42.34.76.76.76H11v-2h2v2h1.24a.76.76 0 0 0 .76-.76v-1.668a1.001 1.001 0 0 0 .217-1.785l-2.662-1.773a1 1 0 0 0-.567-.168Z"
        />
      </svg>
    </span>
  );
};

export default IconLocal;
