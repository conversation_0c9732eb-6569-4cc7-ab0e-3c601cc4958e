import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconWorkflow = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M6.484 2a1 1 0 0 0-.691.293l-3.5 3.5a1 1 0 0 0 0 1.414l3.5 3.5a1 1 0 0 0 1.414 0L10.414 7.5H11a1 1 0 1 0 0-2h-.586L7.207 2.293A1 1 0 0 0 6.484 2Zm11.016.5c-1.25 0-2.315.505-2.998 1.273-.683.77-1.002 1.755-1.002 2.727s.319 1.958 1.002 2.727c.484.544 1.202.876 1.998 1.07V11a1 1 0 1 0 2 0v-.703c.796-.194 1.515-.526 1.998-1.07.683-.77 1.002-1.755 1.002-2.727s-.319-1.958-1.002-2.727C19.815 3.005 18.75 2.5 17.5 2.5Zm-11 1.914L8.586 6.5 6.5 8.586 4.414 6.5 6.5 4.414Zm11 .086c.75 0 1.185.245 1.502.602.317.356.498.87.498 1.398 0 .528-.181 1.042-.498 1.398-.317.357-.752.602-1.502.602s-1.185-.245-1.502-.602c-.317-.356-.498-.87-.498-1.398 0-.528.181-1.042.498-1.398.317-.357.752-.602 1.502-.602ZM6.484 11.986A1 1 0 0 0 5.5 13v1H4a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1H7.5v-1a1 1 0 0 0-1.016-1.014Zm10.96 1.016a1.001 1.001 0 0 0-.807.494l-3.5 6A1 1 0 0 0 14 21h7a1 1 0 0 0 .863-1.504l-3.5-6a1 1 0 0 0-.92-.494Zm.056 2.982L19.26 19h-3.52l1.76-3.016ZM5 16h3v3H5v-3Z"
        />
      </svg>
    </span>
  );
};

export default IconWorkflow;
