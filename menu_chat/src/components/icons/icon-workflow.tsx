import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconWorkflow = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M4.469 4a2.49 2.49 0 0 0-1.752.725 2.487 2.487 0 0 0-.72 1.742v11.066a2.488 2.488 0 0 0 .735 1.758A2.499 2.499 0 0 0 4.47 20h5.529c.167 0 .53.139.82.332.29.193.473.375.473.375a1 1 0 0 0 1.414 0s.183-.182.473-.375.653-.332.82-.332h5.53a2.494 2.494 0 0 0 1.753-.727 2.486 2.486 0 0 0 .717-1.74V6.467a2.487 2.487 0 0 0-.736-1.758A2.487 2.487 0 0 0 19.527 4h-5.529c-.754 0-1.452.329-2.002.83-.55-.5-1.247-.83-2-.83H4.47Zm0 2h5.527c.264 0 .52.107.711.297a.982.982 0 0 1 .291.703v11.346c-.329-.136-.568-.346-1-.346h-5.53a.516.516 0 0 1-.333-.143.49.49 0 0 1-.139-.324V6.467c0-.115.05-.23.14-.326A.518.518 0 0 1 4.47 6Zm9.529 0h5.53c.114 0 .236.055.33.14.09.097.14.212.14.327v11.066a.49.49 0 0 1-.139.324.512.512 0 0 1-.332.143h-5.529c-.432 0-.671.21-1 .346V7a1.006 1.006 0 0 1 1-1Zm-6.5 2c-.828 0-1.5 1.666-1.5 2.492 0 .661.419 1.21 1 1.416v3.586a.5.5 0 1 0 1 0v-3.586c.581-.206 1-.755 1-1.416 0-.826-.672-2.492-1.5-2.492Zm7.5 0a1 1 0 1 0 0 2h3.022a1 1 0 1 0 0-2h-3.022Zm0 3a1 1 0 1 0 0 2h3.022a1 1 0 1 0 0-2h-3.022Zm-.021 3a1 1 0 1 0 0 2h3.021a1 1 0 1 0 0-2h-3.021Z"
        />
      </svg>
    </span>
  );
};

export default IconWorkflow;
