import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconDownload = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M4.248 3a2.265 2.265 0 0 0-2.25 2.25v12.5A2.265 2.265 0 0 0 4.248 20h15.5a2.265 2.265 0 0 0 2.25-2.25V7.25A2.265 2.265 0 0 0 19.748 5h-9.129a.503.503 0 0 1-.353-.146l-1.12-1.122A2.502 2.502 0 0 0 7.38 3h-3.13Zm0 2h3.13c.134 0 .26.053.354.146l1.122 1.122A2.502 2.502 0 0 0 10.62 7h9.129c.149 0 .25.101.25.25v10.5c0 .149-.101.25-.25.25H4.25a.237.237 0 0 1-.25-.25V5.25A.236.236 0 0 1 4.248 5Zm7.736 2.986A1 1 0 0 0 10.998 9v4.578l-1.291-1.287a1.003 1.003 0 0 0-1.708.71 1.001 1.001 0 0 0 .294.708l3 2.99a1 1 0 0 0 1.412 0l3-2.99a1.001 1.001 0 1 0-1.41-1.418l-1.297 1.293V9a.999.999 0 0 0-1.014-1.014Z"
        />
      </svg>
    </span>
  );
};

export default IconDownload;
