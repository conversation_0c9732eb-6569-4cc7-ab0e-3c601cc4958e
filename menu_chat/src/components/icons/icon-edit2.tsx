import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconEdit2 = ({ className, size = 24 }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      fill="none"
      viewBox="0 0 24 24"
      className={twMerge("inline-block", className)}
    >
      <path
        fill="currentColor"
        d="M17.734 2.113a1 1 0 0 0-.736.291l-13.78 13.78a1 1 0 0 0-.267.488l-.926 4.107a1 1 0 0 0 1.196 1.196l4.107-.926a1 1 0 0 0 .488-.268l13.78-13.78a1 1 0 0 0 .283-.835s-.18-1.484-1.371-2.674c-1.19-1.19-2.674-1.37-2.674-1.37a1.001 1.001 0 0 0-.1-.009Zm.201 2.182c.26.062.637.09 1.159.611.521.522.549.899.611 1.158L18.25 7.52l-1.77-1.77 1.456-1.455Zm-2.869 2.869 1.77 1.77L6.609 19.16l-2.287.518.516-2.285L15.066 7.164Z"
      />
    </svg>
  );
};

export default IconEdit2;
