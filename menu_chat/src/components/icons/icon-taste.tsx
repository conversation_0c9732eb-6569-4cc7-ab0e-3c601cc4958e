import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconTaste = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M13 .824c-.64 0-1.278.29-1.664.87l.047-.065-2 2.539A5.947 5.947 0 0 0 8 4c-3.302 0-6 2.698-6 6a5.98 5.98 0 0 0 1.086 3.435C2.356 14.488 2 15.748 2 17c0 1.472.485 2.958 1.502 4.102C4.519 22.245 6.083 23 8 23c2.432 0 4.533-1.467 5.473-3.559C14.243 19.8 15.1 20 16 20c3.302 0 6-2.698 6-6a5.98 5.98 0 0 0-1.78-4.254A8.667 8.667 0 0 0 21 6.187V4.766c0-1.505-1.74-2.489-3.03-1.715l-1.568.941c-.232.14-.407.348-.625.508-.195-.961-.505-1.895-1.054-2.719l-.002-.002-.059-.088A1.982 1.982 0 0 0 13 .824Zm0 1.979.002.002.057.086C13.672 3.81 14 4.893 14 6c0 .076.007.15.023.225-.247.325-.495.651-.713.998a6.056 6.056 0 0 0-2.027-2.237l1.67-2.119c.017-.02.032-.042.047-.064Zm6 1.963v1.422c0 .82-.156 1.63-.45 2.392A5.938 5.938 0 0 0 16 8c-.301 0-.595.031-.885.074a6.664 6.664 0 0 1 2.317-2.367L19 4.766ZM8 6a3.97 3.97 0 0 1 3.959 3.594 5.995 5.995 0 0 0-1.441 1.969A5.94 5.94 0 0 0 8 11c-1.348 0-2.518.378-3.45.998A3.948 3.948 0 0 1 4 10c0-2.22 1.78-4 4-4Zm0 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm8 3c.814 0 1.566.242 2.195.654a1 1 0 0 0 .367.291A3.967 3.967 0 0 1 20 14a3.986 3.986 0 0 1-6.023 3.441c.01-.146.023-.292.023-.441a5.981 5.981 0 0 0-1.783-4.258 3.996 3.996 0 0 1 1.275-1.857c.06-.035.118-.075.17-.121A3.97 3.97 0 0 1 16 10Zm1 2a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm-9 1c2.221 0 4 1.779 4 4s-1.779 4-4 4c-1.417 0-2.352-.495-3.002-1.227C4.348 19.043 4 18.028 4 17c0-.944.306-1.866.857-2.578a1 1 0 0 0 .516-.54C5.997 13.352 6.827 13 8 13Zm-2 3a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"
        />
      </svg>
    </span>
  );
};

export default IconTaste;
