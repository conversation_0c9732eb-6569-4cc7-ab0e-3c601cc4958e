import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconReport = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 1a2.99 2.99 0 0 0-2.82 2.014A1 1 0 0 0 9 3H5c-1.093 0-2 .907-2 2v14c0 1.093.907 2 2 2h14c1.093 0 2-.907 2-2V5c0-1.093-.907-2-2-2h-4c-.06 0-.12.005-.18.016A2.99 2.99 0 0 0 12 1Zm0 2a1 1 0 1 1 0 2 1 1 0 0 1 0-2ZM5 5h3v1a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V5h3v14H5V5Zm3 4a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H8Zm8.004 3.994a1 1 0 0 0-.451.111L12 14.883l-1.553-.777a1 1 0 0 0-.894 0l-2 1a1 1 0 1 0 .894 1.789L10 16.117l1.553.777a1 1 0 0 0 .894 0l4-2a1 1 0 0 0-.443-1.9Z"
        />
      </svg>
    </span>
  );
};

export default IconReport;
