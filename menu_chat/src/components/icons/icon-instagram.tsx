import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconInstagram = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M8 3C5.25 3 3 5.25 3 8v8c0 2.75 2.25 5 5 5h8c2.75 0 5-2.25 5-5V8c0-2.75-2.25-5-5-5H8Zm0 2h8c1.669 0 3 1.332 3 3v8c0 1.669-1.331 3-3 3H8c-1.668 0-3-1.331-3-3V8c0-1.668 1.332-3 3-3Zm9 1a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm-5 1c-1.583 0-2.898.63-3.748 1.586C7.402 9.542 7 10.778 7 12s.402 2.458 1.252 3.414S10.417 17 12 17c1.583 0 2.898-.63 3.748-1.586C16.598 14.458 17 13.222 17 12s-.402-2.458-1.252-3.414S13.583 7 12 7Zm0 2c1.083 0 1.769.37 2.252.914.483.544.748 1.308.748 2.086 0 .778-.265 1.542-.748 2.086-.483.544-1.169.914-2.252.914-1.083 0-1.769-.37-2.252-.914C9.265 13.542 9 12.778 9 12c0-.778.265-1.542.748-2.086C10.231 9.37 10.917 9 12 9Z"
        />
      </svg>
    </span>
  );
};

export default IconInstagram;
