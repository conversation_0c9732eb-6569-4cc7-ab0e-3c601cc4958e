import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconHomeSolid = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M12 1.5a.998.998 0 0 0-.508.139l-9.5 5.588A1 1 0 0 0 3 8.953V20a1 1 0 0 0 1 1h4v-9a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v9h4a1 1 0 0 0 1-1V8.953a.998.998 0 0 0 1.361-.357 1 1 0 0 0-.353-1.37l-9.5-5.587A.997.997 0 0 0 12 1.5ZM10 13v8h4v-8h-4Z"
        />
      </svg>
    </span>
  );
};

export default IconHomeSolid;
