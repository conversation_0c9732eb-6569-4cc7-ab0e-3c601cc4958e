import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconStarSolid = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M22.951 9.446a1.002 1.002 0 0 0-.808-.681l-6.389-.928-2.857-5.79c-.337-.683-1.457-.683-1.794 0l-2.857 5.79-6.39.928a1 1 0 0 0-.554 1.706l4.623 4.506-1.091 6.364a1 1 0 0 0 1.451 1.054L12 19.391l5.715 3.005a.997.997 0 0 0 1.053-.076 1 1 0 0 0 .398-.978l-1.091-6.364 4.623-4.506a1 1 0 0 0 .253-1.026Z"
        />
      </svg>
    </span>
  );
};

export default IconStarSolid;
