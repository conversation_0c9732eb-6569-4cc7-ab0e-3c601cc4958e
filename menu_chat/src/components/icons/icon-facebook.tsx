import { twMerge } from "tailwind-merge";
import { IconProps } from "./index";

const IconFacebook = ({ className, size = 24 }: IconProps) => {
  return (
    <span
      className={twMerge(
        `inline-flex items-center justify-center size-[${size}px]`,
        className,
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M8 3C5.25 3 3 5.25 3 8v8c0 2.75 2.25 5 5 5h3.832c.108.018.218.018.326 0H16c2.75 0 5-2.25 5-5V8c0-2.75-2.25-5-5-5H8Zm0 2h8c1.668 0 3 1.332 3 3v8c0 1.668-1.332 3-3 3h-3v-5h2a1 1 0 1 0 0-2h-2v-1c0-.565.435-1 1-1h1a1 1 0 1 0 0-2h-1c-1.645 0-3 1.355-3 3v1h-1a1 1 0 1 0 0 2h1v5H8c-1.668 0-3-1.332-3-3V8c0-1.668 1.332-3 3-3Z"
        />
      </svg>
    </span>
  );
};

export default IconFacebook;
