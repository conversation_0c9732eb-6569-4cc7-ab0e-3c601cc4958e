import { useState } from "react";
import PreviousReportsFilters from "./previousReportsFilters";
import { IconReport, IconDelete, IconDownload } from "@/components/icons";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Button from "@/components/base/button";
import Link from "next/link";

const PreviousReports = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <section>
      <PreviousReportsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 mx-auto max-w-[742px] rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>Report Name</Th>
              <Th>Report Link</Th>
              <Th>Date</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <IconReport size={20} className="text-neutral-500" />
                  Consumer Insights Report - Salted Caramel
                </div>
              </Td>
              <Td>
                <Link href="/reports/salted_caramel.pdf" target="_blank" download="salted_caramel.pdf">
                  <Button variant="secondary" size="xs" className="gap-1">
                    <IconDownload size={14} />
                    Download
                  </Button>
                </Link>
              </Td>
              <Td className="min-w-[150px]">
                <span className="inline-flex h-[25.5px] items-center group-hover/tr:hidden">
                  06/15/2025
                </span>
                <Button
                  size="xs"
                  variant="tertiary"
                  className="hidden group-hover/tr:inline-flex"
                >
                  <IconDelete size={16} className="text-neutral-500" /> Delete
                </Button>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <IconReport size={20} className="text-neutral-500" />
                  Consumer Insights Report - Parmesan
                </div>
              </Td>
              <Td>
                <Link href="/reports/parmesan.pdf" target="_blank" download="parmesan.pdf">
                  <Button variant="secondary" size="xs" className="gap-1">
                    <IconDownload size={14} />
                    Download
                  </Button>
                </Link>
              </Td>
              <Td className="min-w-[150px]">
                <span className="inline-flex h-[25.5px] items-center group-hover/tr:hidden">
                  06/15/2025
                </span>
                <Button
                  size="xs"
                  variant="tertiary"
                  className="hidden group-hover/tr:inline-flex"
                >
                  <IconDelete size={16} className="text-neutral-500" /> Delete
                </Button>
              </Td>
            </Tr>
          </Tbody>
        </Table>
      </div>
    </section>
  );
};

export default PreviousReports;
