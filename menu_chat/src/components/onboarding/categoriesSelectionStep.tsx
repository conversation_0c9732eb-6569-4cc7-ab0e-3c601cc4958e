import React from "react";
import Heading from "@/components/base/heading";
import ToggleButton from "@/components/onboarding/toggleButton";
import { onboardingData } from "@/data/onboarding";

interface CategoriesSelectionStepProps {
  selectedCategories: string[];
  onCategoryToggle: (category: string) => void;
}

const CategoriesSelectionStep: React.FC<CategoriesSelectionStepProps> = ({
  selectedCategories,
  onCategoryToggle,
}) => {
  return (
    <div className="">
      <Heading level={2} className="text-neutral-1000 mb-1">
        Select categories of your interest.
      </Heading>
      <Heading level={4} className="mb-8 text-neutral-600">
        This will help us customize the app to your preferences.
      </Heading>

      <div className="flex flex-wrap gap-2">
        {onboardingData.categoriesOptions.map((category) => (
          <ToggleButton
            key={category}
            label={category}
            isSelected={selectedCategories.includes(category)}
            onClick={() => onCategoryToggle(category)}
          />
        ))}
      </div>
    </div>
  );
};

export default CategoriesSelectionStep;
