import React, { useState } from "react";
import { useRouter } from "next/router";
import { AnimatePresence, motion } from "motion/react";
import Logo from "@/components/base/logo";
import Button from "@/components/base/button";
import Heading from "@/components/base/heading";
import Link from "next/link";
import { IconArrowLeft } from "@/components/icons";
import RoleSelectionStep from "@/components/onboarding/roleSelectionStep";
import InsightsSelectionStep from "@/components/onboarding/insightsSelectionStep";
import CategoriesSelectionStep from "@/components/onboarding/categoriesSelectionStep";

interface OnboardingData {
  insights: string[];
  categories: string[];
  roles: string[];
}

const FlowContent = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [data, setData] = useState<OnboardingData>({
    insights: [],
    categories: [],
    roles: [],
  });

  const handleInsightToggle = (insight: string) => {
    setData((prev) => ({
      ...prev,
      insights: prev.insights.includes(insight)
        ? prev.insights.filter((i) => i !== insight)
        : [...prev.insights, insight],
    }));
  };

  const handleCategoryToggle = (category: string) => {
    setData((prev) => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter((c) => c !== category)
        : [...prev.categories, category],
    }));
  };

  const handleRoleSelect = (role: string) => {
    setData((prev) => ({
      ...prev,
      roles: prev.roles.includes(role)
        ? prev.roles.filter((r) => r !== role)
        : [...prev.roles, role],
    }));
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.push("/login");
    }
  };

  const handleContinue = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete onboarding - you can redirect to dashboard or save data
      console.log("Onboarding completed:", data);
      router.push("/");
    }
  };

  const componentFade = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.2 } },
    exit: { opacity: 0, transition: { duration: 0.2 } },
  };

  return (
    <div className="bg-muted-100 font-worksans mx-auto flex min-h-screen max-w-2xl flex-col px-8">
      {/* Header */}
      <Logo className="mt-32 mb-14 text-neutral-900" />
      <button
        onClick={handleBack}
        className="font-worksans flex cursor-pointer items-center gap-2 text-neutral-700 transition-colors hover:text-neutral-900"
      >
        <IconArrowLeft size={16} />
        Back
      </button>

      {/* Main Content */}
      <div className="py-8">
        <AnimatePresence mode="wait">
          {currentStep === 1 && (
            <motion.div
              key="step1"
              variants={componentFade}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <RoleSelectionStep
                selectedRoles={data.roles}
                onRoleToggle={handleRoleSelect}
              />
            </motion.div>
          )}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              variants={componentFade}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <InsightsSelectionStep
                selectedInsights={data.insights}
                onInsightToggle={handleInsightToggle}
              />
            </motion.div>
          )}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              variants={componentFade}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <CategoriesSelectionStep
                selectedCategories={data.categories}
                onCategoryToggle={handleCategoryToggle}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-start gap-8">
        <Button
          onClick={handleContinue}
          variant="primary"
          size="md"
          className=""
          disabled={
            (currentStep === 1 && data.roles.length === 0) ||
            (currentStep === 2 && data.insights.length === 0) ||
            (currentStep === 3 && data.categories.length === 0)
          }
        >
          Continue
        </Button>
        <Link href="/">
          <Heading level={4} className="text-neutral-600 underline">
            Skip
          </Heading>
        </Link>
      </div>

      {/* Progress Indicator */}
      <div className="mt-auto flex justify-center gap-2 pb-24">
        {[1, 2, 3].map((step) => (
          <motion.div
            key={step}
            className="bg-muted-300 h-0.5 w-full"
            initial={{ opacity: 0.3 }}
            animate={{
              opacity: step <= currentStep ? 1 : 0.3,
              backgroundColor: step <= currentStep ? "#1f2937" : "#d1d5db",
            }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          />
        ))}
      </div>
    </div>
  );
};

export default FlowContent;
