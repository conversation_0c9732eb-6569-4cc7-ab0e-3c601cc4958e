import React from "react";
import Heading from "@/components/base/heading";
import ToggleButton from "@/components/onboarding/toggleButton";
import { onboardingData } from "@/data/onboarding";

interface InsightsSelectionStepProps {
  selectedInsights: string[];
  onInsightToggle: (insight: string) => void;
}

const InsightsSelectionStep: React.FC<InsightsSelectionStepProps> = ({
  selectedInsights,
  onInsightToggle,
}) => {
  return (
    <div className="">
      <Heading level={2} className="text-neutral-1000 mb-1">
        What kinds of insights are you most interested in?
      </Heading>
      <Heading level={4} className="mb-8 text-neutral-600">
        This will help us customize the app to your preferences.
      </Heading>

      <div className="flex flex-wrap gap-2">
        {onboardingData.insightsOptions.map((insight) => (
          <ToggleButton
            key={insight}
            label={insight}
            isSelected={selectedInsights.includes(insight)}
            onClick={() => onInsightToggle(insight)}
          />
        ))}
      </div>
    </div>
  );
};

export default InsightsSelectionStep;
