import { useRouter } from "next/router";
import Link from "next/link";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";

export const OnboardingStart = () => {
  const router = useRouter();

  return (
    <div className="">
      <Heading level={2} className="mb-1">
        Let&apos;s get you started!
      </Heading>
      <Paragraph className="font-archivo mb-8 font-semibold text-neutral-600">
        Answer a few quick questions to personalize your experience and set up
        the app just for you.
      </Paragraph>
      <div className="mx-auto flex items-center justify-start gap-8">
        <Button onClick={() => router.push("/onboarding/flow")}>Start</Button>
        <Link href="/"  prefetch={false} >
          <Heading level={4} className="text-neutral-700 underline">
            Skip onboarding
          </Heading>
        </Link>
      </div>
    </div>
  );
};
