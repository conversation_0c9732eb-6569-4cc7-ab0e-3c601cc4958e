import React from "react";
import Button from "@/components/base/button";

interface ToggleButtonProps {
  label: string;
  isSelected: boolean;
  onClick: () => void;
}

const ToggleButton: React.FC<ToggleButtonProps> = ({
  label,
  isSelected,
  onClick,
}) => {
  return (
    <Button
      onClick={onClick}
      className={`active:bg-muted-200 h-8 w-fit rounded-md border-[1px] px-3 py-1.5 text-sm font-medium whitespace-nowrap text-neutral-900 ${
        isSelected
          ? "bg-muted-200 border-muted-300 hover:bg-muted-200 hover:border-muted-300"
          : "hover:bg-muted-200 hover:border-muted-300 border-muted-100 bg-white"
      }`}
      size="md"
    >
      {label}
    </Button>
  );
};

export default ToggleButton;
