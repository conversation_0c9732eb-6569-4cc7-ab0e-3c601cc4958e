import React from "react";
import Heading from "@/components/base/heading";
import ToggleButton from "@/components/onboarding/toggleButton";
import { onboardingData } from "@/data/onboarding";

interface RoleSelectionStepProps {
  selectedRoles: string[];
  onRoleToggle: (role: string) => void;
}

const RoleSelectionStep: React.FC<RoleSelectionStepProps> = ({
  selectedRoles,
  onRoleToggle,
}) => {
  return (
    <div className="">
      <Heading level={2} className="text-neutral-1000 mb-1">
        Which one describes your role best?
      </Heading>
      <Heading level={4} className="mb-8 text-neutral-600">
        This will help us customize the app to your preferences.
      </Heading>

      <div className="flex flex-wrap gap-2">
        {onboardingData.roleOptions.map((role) => (
          <ToggleButton
            key={role}
            label={role}
            isSelected={selectedRoles.includes(role)}
            onClick={() => onRoleToggle(role)}
          />
        ))}
      </div>
    </div>
  );
};

export default RoleSelectionStep;
