import Button from "@/components/base/button";
import Input from "@/components/base/input";
import {
  IconDownload,
  IconClose,
  IconSearch,
  IconCircleFilled,
  IconWorkflow,
  IconCompany,
  IconTaste,
  IconSparkleSolid,
  IconRetail,
} from "@/components/icons";
import FilterDropdown from "@/components/base/filterDropdown";
import SortDropdown from "@/components/base/sortDropdown";
import { RETAIL_SORT } from "@/data/retail";
import { Table as TanStackTable } from "@tanstack/react-table";
import { exportTableToCSV } from "@/lib/exportCsv";
import { useMemo, type ReactNode } from "react";

type FilterOption = { label: string; value: string };
type GroupOption = { label: ReactNode; filters: FilterOption[] };
type Level = "l0" | "l1" | "brand" | "claims" | "flavours" | "textures" | "retailer_name";

const PREFIX: Record<Level, `${Level}::`> = { l0: "l0::", l1: "l1::", brand: "brand::", claims: "claims::", flavours: "flavours::", textures: "textures::", retailer_name: "retailer_name::" };
const addPrefix = (level: Level, value: string) => `${PREFIX[level]}${value}`;
const stripPrefix = (v: string): { level: Level | null; value: string } => {
  for (const [level, prefix] of Object.entries(PREFIX) as [Level, string][]) {
    if (v.startsWith(prefix)) {
      return { level, value: v.slice(prefix.length) };
    }
  }
  return { level: null, value: v };
};

type FiltersShape = RetailProductTableFilters | string[];

interface RetailFiltersProps<T, F extends FiltersShape> {
  search: string;
  onSearch: (value: string) => void;
  filters: F;
  onFilter: (value: F) => void;
  sort: string;
  onSort: (value: string) => void;
  table?: TanStackTable<T>;
  tableName?: string;
  filterOptions?: RetailProductTableFilters;
  sortOptions?: SortOption[];
  onExport?: () => void;
}

function toObj(input: FiltersShape): RetailProductTableFilters {
  if (Array.isArray(input)) return { l0: input, l1: undefined };
  return input;
}
function toFlat(obj: RetailProductTableFilters): string[] {
  return [
    ...(obj.l0 ?? []),
    ...(obj.l1 ?? []),
    ...(obj.brand ?? []),
    ...(obj.claims ?? []),
    ...(obj.flavours ?? []),
    ...(obj.textures ?? []),
    ...(obj.retailer_name ?? []),
  ];
}

const RetailFilters = <T, F extends FiltersShape>({
  search,
  onSearch,
  filters,
  onFilter,
  sort,
  onSort,
  table,
  tableName,
  filterOptions,
  sortOptions,
  onExport,
}: RetailFiltersProps<T, F>) => {
  const isArrayInput = Array.isArray(filters);
  const filtersObj = toObj(filters);

  const dynamicFilters: GroupOption[] = useMemo(
    () => [
      ...(filterOptions?.l0?.length
        ? [
            {
              label: (<><IconWorkflow size={20} className="text-neutral-500" /> Main Category</>),
              filters:
                filterOptions.l0.map((value) => ({
                  label: value,
                  value: addPrefix("l0", value),
                })) ?? [],
            } as GroupOption,
          ]
        : []),
      ...(filterOptions?.l1?.length
        ? [
            {
              label: (<><IconWorkflow size={20} className="text-neutral-500" /> Category</>),
              filters:
                filterOptions.l1.map((value) => ({
                  label: value,
                  value: addPrefix("l1", value),
                })) ?? [],
            } as GroupOption,
          ]
        : []),
      
      ...(filterOptions?.brand?.length
        ? [
            {
              label: (<> <IconCompany size={20} className="text-neutral-500" /> Brand</>),
              filters:
                filterOptions.brand.map((value) => ({
                  label: value,
                  value: addPrefix("brand", value),
                })) ?? [],
            } as GroupOption,
          ]
        : []),
      ...(filterOptions?.claims?.length
        ? [
            {
              label: (<> <IconSparkleSolid size={20} className="text-neutral-500" /> Claim</>),
              filters:
                filterOptions.claims.map((value) => ({
                  label: value,
                  value: addPrefix("claims", value),
                })) ?? [],
            } as GroupOption,
          ]
        : []),
      ...(filterOptions?.flavours?.length
        ? [
            {
              label: (<><IconTaste size={20} className="text-neutral-500" /> Flavour</>),
              filters:
                filterOptions.flavours.map((value) => ({
                  label: value,
                  value: addPrefix("flavours", value),
                })) ?? [],
            } as GroupOption,
          ]
        : []),
      ...(filterOptions?.textures?.length
        ? [
            {
              label: (<><IconCircleFilled size={20} className="text-neutral-500" /> Texture</>),
              filters:
                filterOptions.textures.map((value) => ({
                  label: value,
                  value: addPrefix("textures", value),
                })) ?? [],
            } as GroupOption,
          ]
        : []),
      ...((filterOptions?.retailer_name?.length || filterOptions?.banner_name?.length)
        ? [
            {
              label: (<><IconRetail size={20} className="text-neutral-500" /> Retails</>),
              filters:
                [...(filterOptions.retailer_name ?? []), ...(filterOptions.banner_name ?? [])].map((value) => ({
                  label: value,
                  value: addPrefix("retailer_name", value),
                })) ?? [],
            } as GroupOption,
          ]
        : []),
    ],
    [filterOptions?.l0, filterOptions?.l1, filterOptions?.brand, filterOptions?.claims, filterOptions?.flavours, filterOptions?.textures, filterOptions?.retailer_name, filterOptions?.banner_name],
  );

  const selectedForUI: string[] = useMemo(() => {
    const l0 = (filtersObj.l0 ?? []).map((v) => addPrefix("l0", v));
    const l1 = (filtersObj.l1 ?? []).map((v) => addPrefix("l1", v));
    const brand = (filtersObj.brand ?? []).map((v) => addPrefix("brand", v));
    const claims = (filtersObj.claims ?? []).map((v) => addPrefix("claims", v));
    const flavours = (filtersObj.flavours ?? []).map((v) => addPrefix("flavours", v));
    const textures = (filtersObj.textures ?? []).map((v) => addPrefix("textures", v));
    const retailer_name = (filtersObj.retailer_name ?? []).map((v) => addPrefix("retailer_name", v));
    return [...l0, ...l1, ...brand, ...claims, ...flavours, ...textures, ...retailer_name];
  }, [filtersObj.l0, filtersObj.l1, filtersObj.brand, filtersObj.claims, filtersObj.flavours, filtersObj.textures, filtersObj.retailer_name]);

  const handleFilterSelect = (valuesWithPrefix: string[]) => {
    const buckets: Record<Level, string[]> = { l0: [], l1: [], brand: [], claims: [], flavours: [], textures: [], retailer_name: [] };
    for (const v of valuesWithPrefix) {
      const parsed = stripPrefix(v);
      if (parsed.level) buckets[parsed.level].push(parsed.value);
    }
    const nextObj: RetailProductTableFilters = {
      l0: buckets.l0.length ? buckets.l0 : undefined,
      l1: buckets.l1.length ? buckets.l1 : undefined,
      
      brand: buckets.brand.length ? buckets.brand : undefined,
      claims: buckets.claims.length ? buckets.claims : undefined,
      flavours: buckets.flavours.length ? buckets.flavours : undefined,
      textures: buckets.textures.length ? buckets.textures : undefined,
      retailer_name: buckets.retailer_name.length ? buckets.retailer_name : undefined,
    };

    if (isArrayInput) {
      const nextFlat: string[] = toFlat(nextObj);
      onFilter(nextFlat as unknown as F);
    } else {
      onFilter(nextObj as unknown as F);
    }
  };

  const handleRemoveChipPrefixed = (prefixed: string) => {
    const parsed = stripPrefix(prefixed);
    if (!parsed.level) return;
    const level = parsed.level;
    const value = parsed.value;
    const nextObj: RetailProductTableFilters = {
      l0: level === "l0" ? filtersObj.l0?.filter((f) => f !== value) : filtersObj.l0,
      l1: level === "l1" ? filtersObj.l1?.filter((f) => f !== value) : filtersObj.l1,
      
      brand: level === "brand" ? filtersObj.brand?.filter((f) => f !== value) : filtersObj.brand,
      claims: level === "claims" ? filtersObj.claims?.filter((f) => f !== value) : filtersObj.claims,
      flavours: level === "flavours" ? filtersObj.flavours?.filter((f) => f !== value) : filtersObj.flavours,
      textures: level === "textures" ? filtersObj.textures?.filter((f) => f !== value) : filtersObj.textures,
      retailer_name: level === "retailer_name" ? filtersObj.retailer_name?.filter((f) => f !== value) : filtersObj.retailer_name,
    };
    if (isArrayInput) {
      const nextFlat: string[] = toFlat(nextObj);
      onFilter(nextFlat as unknown as F);
    } else {
      onFilter(nextObj as unknown as F);
    }
  };

  const handleReset = () => {
    if (isArrayInput) {
      const emptyArr: string[] = [];
      onFilter(emptyArr as unknown as F);
    } else {
      const emptyObj: RetailProductTableFilters = {
        l0: undefined,
        l1: undefined,
        brand: undefined,
        claims: undefined,
        flavours: undefined,
        textures: undefined,
        retailer_name: undefined,
      };
      onFilter(emptyObj as unknown as F);
    }
  };

  return (
    <>
      <div className="flex items-center justify-between gap-3 pt-3 pb-6">
        <Button
          variant="primary"
          size="sm"
          onClick={() => (onExport ? onExport() : exportTableToCSV(table, tableName ?? "export"))}
        >
          <IconDownload size={20} />
          Export
        </Button>
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => onSearch(e.target.value)}
            aria-label="Search products"
          />
          <FilterDropdown
            selectedValue={selectedForUI}
            options={dynamicFilters}
            onSelect={(values) => handleFilterSelect(values)}
          />
          <SortDropdown
            selectedValue={sort}
            options={sortOptions ?? RETAIL_SORT}
            onSelect={(v) => onSort(v)}
          />
        </div>
      </div>

      {selectedForUI.length ? (
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1.5">
            {selectedForUI.map((prefixed) => {
              const { level, value } = stripPrefix(prefixed);
              const labelPrefix = level === "l0" ? "Main Category: " : level === "l1" ? "Category: " : level === "retailer_name" ? "Retail: " : "";
              const key = `${level ?? ""}:${value}`;
              return (
                <span
                  key={key}
                  className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
                >
                  {labelPrefix}{value}
                  <button
                    type="button"
                    aria-label={`Remove filter ${value}`}
                    className="flex size-4 items-center justify-center"
                    onClick={() => handleRemoveChipPrefixed(prefixed)}
                  >
                    <IconClose
                      size={16}
                      className="text-muted-900 cursor-pointer"
                    />
                  </button>
                </span>
              );
            })}
          </div>
          <Button variant="ghost" size="sm" onClick={handleReset}>
            Reset
          </Button>
        </div>
      ) : null}
    </>
  );
};

export default RetailFilters;
