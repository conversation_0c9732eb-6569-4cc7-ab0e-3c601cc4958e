import Button from "@/components/base/button";
import Input from "@/components/base/input";
import { IconDownload, IconClose, IconSearch, IconCategory, IconChefHat, IconMore } from "@/components/icons";
import FilterDropdown from "@/components/base/filterDropdown";
import SortDropdown from "@/components/base/sortDropdown";
import { RETAIL_SORT } from "@/data/retail";
import { Table as TanStackTable } from "@tanstack/react-table";
import { exportTableToCSV } from "@/lib/exportCsv";
import { useMemo, type ReactNode } from "react";

type FilterOption = { label: string; value: string };
type GroupOption = { label: ReactNode; filters: FilterOption[] };
type Level = "l0" | "l1" | "l2";

const PREFIX: Record<Level, `${Level}::`> = { l0: "l0::", l1: "l1::", l2: "l2::" };
const addPrefix = (level: Level, value: string) => `${PREFIX[level]}${value}`;
const stripPrefix = (v: string): { level: Level | null; value: string } => {
  if (v.startsWith(PREFIX.l0)) return { level: "l0", value: v.slice(PREFIX.l0.length) };
  if (v.startsWith(PREFIX.l1)) return { level: "l1", value: v.slice(PREFIX.l1.length) };
  if (v.startsWith(PREFIX.l2)) return { level: "l2", value: v.slice(PREFIX.l2.length) };
  return { level: null, value: v };
};

type FiltersShape = RetailProductTableFilters | string[];

interface RetailFiltersProps<T, F extends FiltersShape> {
  search: string;
  onSearch: (value: string) => void;
  filters: F;
  onFilter: (value: F) => void;
  sort: string;
  onSort: (value: string) => void;
  table?: TanStackTable<T>;
  tableName?: string;
  filterOptions?: RetailProductTableFilters;
}

function toObj(input: FiltersShape): RetailProductTableFilters {
  if (Array.isArray(input)) return { l0: input, l1: undefined, l2: undefined };
  return input;
}
function toFlat(obj: RetailProductTableFilters): string[] {
  return [...(obj.l0 ?? []), ...(obj.l1 ?? []), ...(obj.l2 ?? [])];
}

const RetailFilters = <T, F extends FiltersShape>({
  search,
  onSearch,
  filters,
  onFilter,
  sort,
  onSort,
  table,
  tableName,
  filterOptions,
}: RetailFiltersProps<T, F>) => {
  const isArrayInput = Array.isArray(filters);
  const filtersObj = toObj(filters);

  const dynamicFilters: GroupOption[] = useMemo(
    () => [
      {
        label: (
          <>
            <IconCategory size={20} className="text-neutral-500" /> Main
            Category
          </>
        ),
        filters:
          filterOptions?.l0?.map((value) => ({
            label: value,
            value: addPrefix("l0", value),
          })) ?? [],
      },
      {
        label: (
          <>
            <IconChefHat size={20} className="text-neutral-500" /> Category
          </>
        ),
        filters:
          filterOptions?.l1?.map((value) => ({
            label: value,
            value: addPrefix("l1", value),
          })) ?? [],
      },
      {
        label: (
          <>
            <IconMore size={20} className="text-neutral-500" /> Sub Category
          </>
        ),
        filters:
          filterOptions?.l2?.map((value) => ({
            label: value,
            value: addPrefix("l2", value),
          })) ?? [],
      },
    ],
    [filterOptions?.l0, filterOptions?.l1, filterOptions?.l2],
  );

  const selectedForUI: string[] = useMemo(() => {
    const l0 = (filtersObj.l0 ?? []).map((v) => addPrefix("l0", v));
    const l1 = (filtersObj.l1 ?? []).map((v) => addPrefix("l1", v));
    const l2 = (filtersObj.l2 ?? []).map((v) => addPrefix("l2", v));
    return [...l0, ...l1, ...l2];
  }, [filtersObj.l0, filtersObj.l1, filtersObj.l2]);

  const handleFilterSelect = (valuesWithPrefix: string[]) => {
    const buckets: Record<Level, string[]> = { l0: [], l1: [], l2: [] };
    for (const v of valuesWithPrefix) {
      const parsed = stripPrefix(v);
      if (parsed.level) buckets[parsed.level].push(parsed.value);
    }
    const nextObj: RetailProductTableFilters = {
      l0: buckets.l0.length ? buckets.l0 : undefined,
      l1: buckets.l1.length ? buckets.l1 : undefined,
      l2: buckets.l2.length ? buckets.l2 : undefined,
    };

    if (isArrayInput) {
      const nextFlat: string[] = toFlat(nextObj);
      onFilter(nextFlat as unknown as F);
    } else {
      onFilter(nextObj as unknown as F);
    }
  };

  const flatSelected = useMemo(() => toFlat(filtersObj), [filtersObj]);

  const handleRemoveChip = (filter: string) => {
    const nextObj: RetailProductTableFilters = {
      l0: filtersObj.l0?.filter((f) => f !== filter),
      l1: filtersObj.l1?.filter((f) => f !== filter),
      l2: filtersObj.l2?.filter((f) => f !== filter),
    };
    if (isArrayInput) {
      const nextFlat: string[] = toFlat(nextObj);
      onFilter(nextFlat as unknown as F);
    } else {
      onFilter(nextObj as unknown as F);
    }
  };

  const handleReset = () => {
    if (isArrayInput) {
      const emptyArr: string[] = [];
      onFilter(emptyArr as unknown as F);
    } else {
      const emptyObj: RetailProductTableFilters = {
        l0: undefined,
        l1: undefined,
        l2: undefined,
      };
      onFilter(emptyObj as unknown as F);
    }
  };

  return (
    <>
      <div className="flex items-center justify-between gap-3 pt-3 pb-6">
        <Button
          variant="primary"
          size="sm"
          onClick={() => exportTableToCSV(table, tableName)}
        >
          <IconDownload size={20} />
          Export
        </Button>
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => onSearch(e.target.value)}
            aria-label="Search products"
          />
          <FilterDropdown
            selectedValue={selectedForUI}
            options={dynamicFilters}
            onSelect={(values) => handleFilterSelect(values)}
          />
          <SortDropdown
            selectedValue={sort}
            options={RETAIL_SORT}
            onSelect={(v) => onSort(v)}
          />
        </div>
      </div>

      {flatSelected.length ? (
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1.5">
            {flatSelected.map((filter) => (
              <span
                key={filter}
                className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
              >
                {filter}
                <button
                  type="button"
                  aria-label={`Remove filter ${filter}`}
                  className="flex size-4 items-center justify-center"
                  onClick={() => handleRemoveChip(filter)}
                >
                  <IconClose
                    size={16}
                    className="text-muted-900 cursor-pointer"
                  />
                </button>
              </span>
            ))}
          </div>
          <Button variant="ghost" size="sm" onClick={handleReset}>
            Reset
          </Button>
        </div>
      ) : null}
    </>
  );
};

export default RetailFilters;
