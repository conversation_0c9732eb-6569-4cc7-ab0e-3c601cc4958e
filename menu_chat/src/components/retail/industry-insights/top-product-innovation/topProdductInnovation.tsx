import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Image from "next/image";
import Tag from "@/components/base/tag";
import { IconWorkflow, IconIncrease } from "@/components/icons";

interface TopProductInnovationProps {
  className?: string;
}

const TopProductInnovation = ({ className }: TopProductInnovationProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Top Product Innovation</Heading>
          <Info
            tooltipId="top-product-innovation"
            content="New products launched in last quarter."
          />
        </div>
        <Link href="/retail/top-product-innovation">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="grid grid-cols-4 gap-5">
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="px-2 py-1">
              <Tag>ProBioCheese</Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Category
                </Paragraph>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Dairy & Eggs
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Brand
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  GarenGood Foods
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Change
                </Paragraph>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5.70%
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Feb 2025
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="px-2 py-1">
              <Tag>CalmFizz</Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Category
                </Paragraph>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Beverages
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Brand
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  PureWave Beverages
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Change
                </Paragraph>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  83.0%
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Feb 2025
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="px-2 py-1">
              <Tag>ZeroFuel</Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Category
                </Paragraph>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Beverages
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Brand
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  VitalBoost Labs
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Change
                </Paragraph>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  65.0%
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Feb 2025
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="px-2 py-1">
              <Tag>FruitCycle Bites</Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Category
                </Paragraph>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Pantry
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Brand
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  EcoSnack Co.
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Change
                </Paragraph>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  58.68%
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Apr 2025
                </Paragraph>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </Card>
  );
};

export default TopProductInnovation;
