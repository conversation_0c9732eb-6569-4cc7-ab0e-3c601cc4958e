import { useState } from "react";
import {
  IconGraph,
  IconIncrease,
  IconDecrease,
  IconDonut,
  IconCircle,
  IconCircleFilled,
  IconCircleArrowDown,
} from "@/components/icons";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import RetailFilters from "@/components/retail/retailFilters";
import Favorite from "@/components/base/favorite";

const TopProductInnovationTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <RetailFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Name</Th>
              <Th>Brand</Th>
              <Th>Category</Th>
              <Th>Lifecycle Stages</Th>
              <Th>Saturation</Th>
              <Th>Change</Th>
              <Th>First Appearance</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Tyson Beef Strips</Tag>
              </Td>
              <Td>Tyson</Td>
              <Td>
                <Tag variant="white">Meat</Tag>
              </Td>
              <Td>
                <Tag variant="magenta">
                  <IconDonut size={16} />
                  New
                </Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">Low</Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>Jan 2025</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Beyond Burger</Tag>
              </Td>
              <Td>Beyond</Td>
              <Td>
                <Tag variant="white">Natural & Organic</Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} />
                  Emergence
                </Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">High</Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>Dec 2025</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Uncle Ben’s Original</Tag>
              </Td>
              <Td>Uncle Ben’s</Td>
              <Td>
                <Tag variant="white">Frozen</Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>
                <Tag variant="blueOutline">Medium</Tag>
              </Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
              <Td>Mar 2025</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Dole Bananas</Tag>
              </Td>
              <Td>Dole</Td>
              <Td>
                <Tag variant="white">Fresh Fruit & Veg...</Tag>
              </Td>
              <Td>
                <Tag variant="red">
                  <IconCircleArrowDown size={16} />
                  Declining
                </Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">Low</Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  24%
                </Tag>
              </Td>
              <Td>Feb 2025</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Heinz Tomato Ketchup</Tag>
              </Td>
              <Td>Heinz</Td>
              <Td>
                <Tag variant="white">Deli</Tag>
              </Td>
              <Td>
                <Tag variant="magenta">
                  <IconDonut size={16} />
                  New
                </Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">Low</Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>Jan 2025</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Lay’s Classic</Tag>
              </Td>
              <Td>{"Lay's"}</Td>
              <Td>
                <Tag variant="white">Pantry</Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} />
                  Emergence
                </Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">High</Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>Dec 2025</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Wonder Bread</Tag>
              </Td>
              <Td>Wonder Bakery</Td>
              <Td>
                <Tag variant="white">Bakery</Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>
                <Tag variant="blueOutline">Medium</Tag>
              </Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
              <Td>Mar 2025</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Freshly Chicken Parm</Tag>
              </Td>
              <Td>Heinz</Td>
              <Td>
                <Tag variant="white">Freshly</Tag>
              </Td>
              <Td>
                <Tag variant="red">
                  <IconCircleArrowDown size={16} />
                  Declining
                </Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">Low</Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  24%
                </Tag>
              </Td>
              <Td>Feb 2025</Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default TopProductInnovationTable;
