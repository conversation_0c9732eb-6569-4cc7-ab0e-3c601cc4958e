import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Image from "next/image";
import Tag from "@/components/base/tag";
import { IconWorkflow, IconIncrease } from "@/components/icons";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { topProductInnovation } from "@/api/retailers";

interface TopProductInnovationProps {
  className?: string;
}

const TopProductInnovation = ({ className }: TopProductInnovationProps) => {
  const { data: session } = useSession();
  const { data, isLoading, error } = useQuery<
    TopProductInnovationResult,
    Error
  >({
    queryKey: ["top_product_innovation"],
    queryFn: () =>
      topProductInnovation({ auth: session?.user.authorization as string }),
    enabled: !!session?.user?.authorization,
  });

  if (isLoading) return <div>Loading...</div>;
  if (error || !data) return <div>Error loading data</div>;

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Top Product Innovation</Heading>
          <Info
            tooltipId="top-product-innovation"
            content="New products launched in last quarter."
          />
        </div>
        <Link href="/retail/product-innovation">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="grid grid-cols-4 gap-5">
          {data.data.map((product, index) => (
            <Card key={index} className="p-3">
              <Image
                src={product.image_url}
                alt={product.name}
                width={100}
                height={232}
                className="mb-1 h-[140px] w-full rounded-lg object-cover"
              />
              <div className="px-2 py-1">
                <Tag className="inline-block max-w-full truncate rounded-md bg-neutral-100 px-2 py-0.5 text-sm font-medium break-words whitespace-normal text-neutral-800">
                  {product.name}
                </Tag>
              </div>
              <div className="flex w-full flex-col">
                <div className="flex items-center justify-between gap-2 px-3 py-1">
                  <Paragraph className="text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
                    Category
                  </Paragraph>
                  <Tag variant="white">
                    <IconWorkflow size={16} className="text-neutral-500" />
                    {product.category}
                  </Tag>
                </div>
              </div>
              <div className="flex w-full flex-col">
                <div className="flex items-center justify-between gap-2 px-3 py-1">
                  <Paragraph className="text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
                    Brand
                  </Paragraph>
                  <Paragraph size="sm" className="font-medium">
                    {product.brand}
                  </Paragraph>
                </div>
              </div>
              <div className="flex w-full flex-col">
                <div className="flex items-center justify-between gap-2 px-3 py-1">
                  <Paragraph className="text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
                    Change
                  </Paragraph>
                  <Tag size="xs" variant="greenOutline">
                    <IconIncrease size={16} />
                    {product.change.toFixed(1)}%
                  </Tag>
                </div>
              </div>
              <div className="flex w-full flex-col">
                <div className="flex items-center justify-between gap-2 px-3 py-1">
                  <Paragraph className="text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
                    First Appearance
                  </Paragraph>
                  <Paragraph size="sm" className="font-medium">
                    {product.first_appearance}
                  </Paragraph>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default TopProductInnovation;
