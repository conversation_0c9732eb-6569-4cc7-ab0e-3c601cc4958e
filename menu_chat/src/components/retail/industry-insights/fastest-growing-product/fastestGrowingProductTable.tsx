import { useState } from "react";
import {
  IconGraph,
  IconIncrease,
  IconDecrease,
  IconDonut,
  IconCircle,
  IconCircleFilled,
  IconCircleArrowDown,
} from "@/components/icons";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import RetailFilters from "@/components/retail/retailFilters";

const FastestGrowingRetailTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <RetailFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>Category</Th>
              <Th>Lifecycle Stage</Th>
              <Th>Social Mentions</Th>
              <Th>Saturation</Th>
              <Th>Product Number</Th>
              <Th>Change</Th>
              <Th>Liked By</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Tag variant="white">Meat</Tag>
              </Td>
              <Td>
                <Tag variant="magenta">
                  <IconDonut size={16} />
                  New
                </Tag>
              </Td>
              <Td>98,332</Td>
              <Td>
                <Tag variant="greenOutline">High</Tag>
              </Td>
              <Td>8743</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>95.16%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">Natural & Organic</Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} />
                  Emergence
                </Tag>
              </Td>
              <Td>2,332</Td>
              <Td>
                <Tag variant="blueOutline">Medium</Tag>
              </Td>
              <Td>2934</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
              <Td>9,54%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">Beer, Wine & Liquor</Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>42,572</Td>
              <Td>
                <Tag variant="greenOutline">High</Tag>
              </Td>
              <Td>9845</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>23,19%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">Fresh Fruits & Vegetables</Tag>
              </Td>
              <Td>
                <Tag variant="red">
                  <IconCircleArrowDown size={16} />
                  Declining
                </Tag>
              </Td>
              <Td>12,332</Td>
              <Td>
                <Tag variant="redOutline">Low</Tag>
              </Td>
              <Td>3487</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  24%
                </Tag>
              </Td>
              <Td>23,19%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">Bakery</Tag>
              </Td>
              <Td>
                <Tag variant="magenta">
                  <IconDonut size={16} />
                  New
                </Tag>
              </Td>
              <Td>98,332</Td>
              <Td>
                <Tag variant="greenOutline">High</Tag>
              </Td>
              <Td>8743</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>95.16%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">Coffee</Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} />
                  Emergence
                </Tag>
              </Td>
              <Td>2,332</Td>
              <Td>
                <Tag variant="blueOutline">Medium</Tag>
              </Td>
              <Td>2934</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
              <Td>9,54%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">Dairy & Eggs</Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>42,572</Td>
              <Td>
                <Tag variant="greenOutline">High</Tag>
              </Td>
              <Td>9845</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>23,19%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">Frozen</Tag>
              </Td>
              <Td>
                <Tag variant="red">
                  <IconCircleArrowDown size={16} />
                  Declining
                </Tag>
              </Td>
              <Td>12,332</Td>
              <Td>
                <Tag variant="redOutline">Low</Tag>
              </Td>
              <Td>3487</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  24%
                </Tag>
              </Td>
              <Td>23,19%</Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default FastestGrowingRetailTable;
