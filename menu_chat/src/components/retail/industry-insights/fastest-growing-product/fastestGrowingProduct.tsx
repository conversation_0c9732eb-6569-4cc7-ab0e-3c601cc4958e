import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Link from "next/link";
import LineChart from "@/components/charts/lineChart";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fastestGrowingIngredientCategory } from "@/api/products";

interface FastestGrowingProductProps {
  className?: string;
}

const FastestGrowingProduct = ({ className }: FastestGrowingProductProps) => {
  const { data: session } = useSession();
  const { data, isLoading, error } = useQuery({
    queryFn: () => fastestGrowingIngredientCategory({ auth: session?.user.authorization as string }),
    queryKey: ["fastest_growing_ingredient_category"],
    enabled: !!session?.user?.authorization,
  });

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  if (isLoading) return <div>Loading...</div>;
  if (error || !data) return <div>Error loading data</div>;

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Fastest Growing Product Categories</Heading>
          <Info
            tooltipId="fastest-growing-product-categories"
            content="Product categories with most new products in last quarter."
          />
        </div>
        <div>
          <Link href="/retail/fastest-growing-product">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="border-muted-100 rounded-lg border p-4">
          <LineChart
            dataItems={data.data}
            labels={data.labels}
            symbol="%"
            height={250}
          />
        </div>
      </div>
    </Card>
  );
};

export default FastestGrowingProduct;
