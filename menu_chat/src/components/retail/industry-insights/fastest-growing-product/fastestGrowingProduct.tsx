import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { <PERSON><PERSON><PERSON><PERSON> } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import Link from "next/link";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";

interface FastestGrowingProductProps {
  className?: string;
}

const chartData: LineSeriesItem[] = [
  {
    id: 1,
    label: "Pantry",
    color: "#8BC539",
    data: [0.0, 0.1, 0.3, 1.0, 2.2, 3.5, 4.5, 5.5, 6.3, 7.0, 7.8],
  },
  {
    id: 2,
    label: "<PERSON><PERSON> & Eggs",
    color: "#FFBE05",
    data: [0.4, 0.4, 0.3, 2.8, 6.5, 10.2, 9.8, 10.0, 9.8, 10.2, 11.5],
  },
  {
    id: 3,
    label: "Meat",
    color: "#D273D8",
    data: [0.1, 0.3, 0.4, 2.5, 4.7, 4.0, 4.3, 5.5, 6.5, 7.2, 8.1],
  },
  {
    id: 4,
    label: "Beverages",
    color: "#9571AD",
    data: [0.2, 0.5, 1.9, 2.5, 3.6, 3.2, 3.9, 5.2, 5.6, 6.2, 7.2],
  },
  {
    id: 5,
    label: "Fresh Fruits & Vegetables",
    color: "#FF6D56",
    data: [0.3, 0.6, 0.8, 1.9, 2.8, 3.0, 4.5, 4.9, 5.2, 5.5, 6.2],
  },
];

const FastestGrowingProduct = ({ className }: FastestGrowingProductProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Fastest Growing Product Categories</Heading>
          <Info
            tooltipId="fastest-growing-product-categories"
            content="Product categories with most new products in last quarter."
          />
        </div>
        <div>
          <Link href="/retail/fastest-growing-product">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={MENU_ADOPTION_CURVE_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="border-muted-100 rounded-lg border p-4">
          <LineChart
            dataItems={chartData}
            labels={[
              "Feb 5",
              "Feb 12",
              "Feb 19",
              "Mar 5",
              "Mar 12",
              "Mar 19",
              "Mar 26",
              "Apr 2",
              "Apr 9",
              "Apr 16",
              "Apr 23",
            ]}
            symbol="%"
            height={250}
          />
        </div>
      </div>
    </Card>
  );
};

export default FastestGrowingProduct;
