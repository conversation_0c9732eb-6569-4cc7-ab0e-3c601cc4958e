import FastestGrowingProduct from "./fastest-growing-product/fastestGrowingProduct";
import PopularFlavorTrendst from "./popular-flavor-trends/popularFlavorTrends";
import TopProductInnovation from "./top-product-innovation/topProductInnovation";
import ProductLifecycle from "./product-lifecycle/productLifecycle";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const RetailIndustryInsights = () => {
  return (
    <section className="mt-[60px] py-4">
      <div className="flex flex-col gap-5">
        <ProductLifecycle />
        <div className="grid grid-cols-2 gap-5">
          <FastestGrowingProduct />
          <PopularFlavorTrendst />
        </div>
        <TopProductInnovation />
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default RetailIndustryInsights;
