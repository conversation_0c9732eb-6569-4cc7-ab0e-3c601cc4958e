import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconTaste2 } from "@/components/icons";

interface PopularFlavorTrendstProps {
  className?: string;
}

const PopularFlavorTrendst = ({ className }: PopularFlavorTrendstProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Popular Flavor Trends</Heading>
          <Info
            tooltipId="popular-flavor-trends"
            content="Most popular flavors in last quarter."
          />
        </div>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Saturation</Th>
            <Th>Liked By</Th>
            <Th>NPD Opportunity</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <div className="flex items-center gap-0.5">
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Spicy
                </Tag>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Sweet
                </Tag>
              </div>
            </Td>
            <Td>
              <Tag variant="blueOutline">Medium</Tag>
            </Td>
            <Td>68.05%</Td>
            <Td>34.05%</Td>
          </Tr>
          <Tr>
            <Td>
              <div className="flex items-center gap-0.5">
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Pickled
                </Tag>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Fermented
                </Tag>
              </div>
            </Td>
            <Td>
              <Tag variant="greenOutline">High</Tag>
            </Td>
            <Td>40.34%</Td>
            <Td>28.05%</Td>
          </Tr>
          <Tr>
            <Td>
              <div className="flex items-center gap-0.5">
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Exotic
                </Tag>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Tropical
                </Tag>
              </div>
            </Td>
            <Td>
              <Tag variant="blueOutline">Medium</Tag>
            </Td>
            <Td>27.52%</Td>
            <Td>68.05%</Td>
          </Tr>
          <Tr>
            <Td>
              <div className="flex items-center gap-0.5">
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Floral Botanicals
                </Tag>
              </div>
            </Td>
            <Td>
              <Tag variant="greenOutline">High</Tag>
            </Td>
            <Td>27.52%</Td>
            <Td>68.05%</Td>
          </Tr>
          <Tr>
            <Td>
              <div className="flex items-center gap-0.5">
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Spicy
                </Tag>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Sweet
                </Tag>
              </div>
            </Td>
            <Td>
              <Tag variant="redOutline">Low</Tag>
            </Td>
            <Td>43.13%</Td>
            <Td>43.05%</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default PopularFlavorTrendst;
