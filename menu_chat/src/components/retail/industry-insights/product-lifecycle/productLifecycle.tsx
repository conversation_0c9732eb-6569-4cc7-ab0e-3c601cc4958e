import { useEffect, useMemo, useState, useCallback } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore, IconClose, IconCategory } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import ProductLifecycleItem from "./productLifecycleItem";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getRetailLifecycleFilters, getRetailProductLifecycle } from "@/api/retailer_products";
import FilterDropdown from "@/components/base/filterDropdown";
import Switch from "@/components/base/switch";

type Level = "l0" | "l1" | "l2";
type AllLevel = Level | "gri";
type StageKey = "new" | "emergence" | "mainstream" | "declining";

const PREFIX: Record<AllLevel, `${AllLevel}::`> = { l0: "l0::", l1: "l1::", l2: "l2::", gri: "gri::", };

const addPrefix = (lvl: AllLevel, v: string) => `${PREFIX[lvl]}${v}`;
const stripPrefix = (v: string): { level: AllLevel | null; value: string } => {
  if (v.startsWith(PREFIX.l0)) return { level: "l0", value: v.slice(PREFIX.l0.length) };
  if (v.startsWith(PREFIX.l1)) return { level: "l1", value: v.slice(PREFIX.l1.length) };
  if (v.startsWith(PREFIX.l2)) return { level: "l2", value: v.slice(PREFIX.l2.length) };
  if (v.startsWith(PREFIX.gri)) return { level: "gri", value: v.slice(PREFIX.gri.length) };
  return { level: null, value: v };
};

type LifecycleCardItem = {
  name: string;
  guid: string;
  penetration: number;
  change: number;
  image_url?: string | null;
};

interface ProductLifecycleProps {
  className?: string;
}

const ProductLifecycle = ({ className }: ProductLifecycleProps) => {
  const { data: session } = useSession();
  const auth = session?.user?.authorization;

  const [selected, setSelected] = useState<string>("");
  const [filters, setFilters] = useState<RetailProductTableFilters>({
    l0: [],
    l1: [],
    l2: [],
    gri: [],
  });
  const [isClaimed, setIsClaimed] = useState<boolean>(false);

  useEffect(() => {
    if (!isClaimed && (filters.gri?.length ?? 0) > 0) {
      setFilters((prev) => ({ ...prev, gri: [] }));
    }
  }, [isClaimed, filters.gri?.length]);

  const { data: filterOptions } = useQuery<RetailProductTableFilters>({
    queryKey: ["retail_lifecycle_filters", auth, isClaimed],
    queryFn: () => getRetailLifecycleFilters({ auth: auth as string, claimed: isClaimed }),
    enabled: !!auth,
    staleTime: 86_400_000,
  });

  const { data: lifecycleItems } = useQuery<RetailLifecycleItem[]>({
    queryKey: ["retail_lifecycle", auth, filters, isClaimed],
    queryFn: () => getRetailProductLifecycle({ auth: auth as string, filters, claimed: isClaimed }),
    enabled: !!auth,
  });

  const grouped = useMemo(() => {
    const base: Record<StageKey, LifecycleCardItem[]> = {
      new: [],
      emergence: [],
      mainstream: [],
      declining: [],
    };

    for (const it of lifecycleItems ?? []) {
      const bucket: StageKey = it.type ?? "mainstream";
      const name = isClaimed ? (it.claim ?? "") : (it.gri ?? "");
      const guid = isClaimed ? "#" : it.guid || "#";

      base[bucket].push({
        name,
        guid,
        penetration: Number(it.penetration),
        change: Number(it.change),
        image_url: it.image_url ?? null,
      });
    }
    return base;
  }, [lifecycleItems, isClaimed]);

  const dynamicOptions: FilterOption[] = useMemo(() => {
    const opts: FilterOption[] = [
      {
        label: (
          <>
            <IconCategory size={20} className="text-neutral-500" /> Main Category
          </>
        ),
        filters: filterOptions?.l0?.map((v) => ({ label: v, value: addPrefix("l0", v) })) ?? [],
      },
      {
        label: (
          <>
            <IconCategory size={20} className="text-neutral-500" /> Category
          </>
        ),
        filters: filterOptions?.l1?.map((v) => ({ label: v, value: addPrefix("l1", v) })) ?? [],
      },
      {
        label: (
          <>
            <IconCategory size={20} className="text-neutral-500" /> Sub Category
          </>
        ),
        filters: filterOptions?.l2?.map((v) => ({ label: v, value: addPrefix("l2", v) })) ?? [],
      },
    ];

    if (isClaimed && (filterOptions?.gri?.length ?? 0) > 0) {
      opts.push({
        label: (
          <>
            <IconCategory size={20} className="text-neutral-500" /> GRI
          </>
        ),
        filters: (filterOptions?.gri ?? []).map((v) => ({ label: v, value: addPrefix("gri", v) })),
      });
    }

    return opts;
  }, [filterOptions, isClaimed]);

  const selectedForUI = useMemo(
    () => [
      ...(filters.l0 ?? []).map((v) => addPrefix("l0", v)),
      ...(filters.l1 ?? []).map((v) => addPrefix("l1", v)),
      ...(filters.l2 ?? []).map((v) => addPrefix("l2", v)),
      ...(filters.gri ?? []).map((v) => addPrefix("gri", v)),
    ],
    [filters.l0, filters.l1, filters.l2, filters.gri],
  );

  const handleFilterSelect = useCallback((valuesWithPrefix: string[]) => {
    const buckets: Record<AllLevel, string[]> = { l0: [], l1: [], l2: [], gri: [] };
    for (const v of valuesWithPrefix) {
      const parsed = stripPrefix(v);
      if (parsed.level) buckets[parsed.level].push(parsed.value);
    }
    setFilters({ l0: buckets.l0, l1: buckets.l1, l2: buckets.l2, gri: buckets.gri });
  }, []);

  const flatTags = useMemo(
    () => [...(filters.l0 ?? []), ...(filters.l1 ?? []), ...(filters.l2 ?? []), ...(filters.gri ?? [])],
    [filters],
  );

  const newKey        = useMemo(() => `new-${grouped.new.length}-${grouped.new[0]?.name ?? "none"}`, [grouped.new]);
  const emergenceKey  = useMemo(() => `emergence-${grouped.emergence.length}-${grouped.emergence[0]?.name ?? "none"}`, [grouped.emergence]);
  const mainstreamKey = useMemo(() => `mainstream-${grouped.mainstream.length}-${grouped.mainstream[0]?.name ?? "none"}`, [grouped.mainstream]);
  const decliningKey  = useMemo(() => `declining-${grouped.declining.length}-${grouped.declining[0]?.name ?? "none"}`, [grouped.declining]);

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            Product Lifecycle Stage Distribution For Fastest Growing Product in Each Category
          </Heading>
          {flatTags.length > 0 && (
            <div className="ml-2 flex items-center justify-between gap-2">
              <div className="flex items-center gap-1.5">
                <span>—</span>
                {flatTags.map((filter) => (
                  <span
                    key={filter}
                    className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
                  >
                    {filter}
                    <button
                      type="button"
                      className="flex size-4 items-center justify-center"
                      onClick={() => {
                        const next = { ...filters };
                        if (next.l0?.includes(filter)) next.l0 = next.l0.filter((f) => f !== filter);
                        if (next.l1?.includes(filter)) next.l1 = next.l1.filter((f) => f !== filter);
                        if (next.l2?.includes(filter)) next.l2 = next.l2.filter((f) => f !== filter);
                        if (next.gri?.includes(filter)) next.gri = next.gri.filter((f) => f !== filter);
                        setFilters(next);
                      }}
                      aria-label={`Remove filter ${filter}`}
                    >
                      <IconClose size={16} className="text-muted-900 cursor-pointer" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Switch checked={isClaimed} onChange={setIsClaimed} label="Show Claims" />
          <FilterDropdown
            selectedValue={selectedForUI}
            options={dynamicOptions}
            onSelect={handleFilterSelect}
            showReset={true}
          />
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(v) => setSelected(v)}
          >
            <Button variant="ghost" size="xs" className="w-6" aria-label="Chart actions">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>

      <div className="overflow-x-auto">
        <div className="grid min-w-[1116px] grid-cols-4 gap-4 p-4">
          <ProductLifecycleItem key={newKey} type="new" items={grouped.new} />
          <ProductLifecycleItem key={emergenceKey} type="emergence" items={grouped.emergence} />
          <ProductLifecycleItem key={mainstreamKey} type="mainstream" items={grouped.mainstream} />
          <ProductLifecycleItem key={decliningKey} type="declining" items={grouped.declining} />
        </div>
      </div>
    </Card>
  );
};

export default ProductLifecycle;
