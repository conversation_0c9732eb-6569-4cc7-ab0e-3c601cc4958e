import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import ProductLifecycleItem from "./productLifecycleItem";

interface ProductLifecycleProps {
  className?: string;
}

const ProductLifecycle = ({ className }: ProductLifecycleProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const items = [
    {
      type: "new" as const,
      data: [
        {
          name: "Life Savers",
          guid: "1",
          penetration: 2,
          change: 1,
          image_url: "/",
        },
        {
          name: "Tree Top",
          guid: "2",
          penetration: 3,
          change: 0,
          image_url: "/",
        },
        {
          name: "Special K",
          guid: "3",
          penetration: 1,
          change: 1,
          image_url: "/",
        },
        {
          name: "Sierra Mist",
          guid: "4",
          penetration: 2,
          change: -1,
          image_url: "/",
        },
        {
          name: "Heineken",
          guid: "5",
          penetration: 2,
          change: 2,
          image_url: "/",
        },
        {
          name: "Tazo Awake",
          guid: "6",
          penetration: 1,
          change: 0,
          image_url: "/",
        },
        {
          name: "Red Bull",
          guid: "7",
          penetration: 1,
          change: 0,
          image_url: "/",
        },
      ],
    },
    {
      type: "emergence" as const,
      data: [
        {
          name: "De Cecco Penne",
          guid: "8",
          penetration: 4,
          change: 1,
          image_url: "/",
        },
        {
          name: "Rold Gold",
          guid: "9",
          penetration: 5,
          change: 2,
          image_url: "/",
        },
        {
          name: "Mango",
          guid: "10",
          penetration: 3,
          change: 1,
          image_url: "/",
        },
        { name: "Bai", guid: "11", penetration: 3, change: 0, image_url: "/" },
        {
          name: "Life Savers",
          guid: "12",
          penetration: 4,
          change: 1,
          image_url: "/",
        },
        {
          name: "Dole Bananas",
          guid: "13",
          penetration: 3,
          change: 2,
          image_url: "/",
        },
        {
          name: "KIND bar",
          guid: "14",
          penetration: 3,
          change: 2,
          image_url: "/",
        },
        {
          name: "Cheddar",
          guid: "15",
          penetration: 3,
          change: 2,
          image_url: "/",
        },
        {
          name: "Nasoya Organic Tofu",
          guid: "16",
          penetration: 3,
          change: 2,
          image_url: "/",
        },
        {
          name: "Jolly Rancher",
          guid: "17",
          penetration: 3,
          change: 2,
          image_url: "/",
        },
      ],
    },
    {
      type: "mainstream" as const,
      data: [
        {
          name: "Lay’s Classic",
          guid: "18",
          penetration: 7,
          change: 0,
          image_url: "/",
        },
        {
          name: "KIND bar",
          guid: "19",
          penetration: 8,
          change: -1,
          image_url: "/",
        },
        {
          name: "Wonder Bread",
          guid: "20",
          penetration: 6,
          change: 0,
          image_url: "/",
        },
        {
          name: "Boom Chicka Pop",
          guid: "21",
          penetration: 6,
          change: 1,
          image_url: "/",
        },
        {
          name: "Special K",
          guid: "22",
          penetration: 5,
          change: 0,
          image_url: "/",
        },
        {
          name: "Guinness",
          guid: "23",
          penetration: 5,
          change: 0,
          image_url: "/",
        },
        {
          name: "Nescafe Classic",
          guid: "24",
          penetration: 5,
          change: 0,
          image_url: "/",
        },
        { name: "Bai", guid: "25", penetration: 5, change: 0, image_url: "/" },
        {
          name: "Oatly Oat Milk",
          guid: "26",
          penetration: 5,
          change: 0,
          image_url: "/",
        },
        { name: "Tazo", guid: "27", penetration: 5, change: 0, image_url: "/" },
      ],
    },
    {
      type: "declining" as const,
      data: [
        {
          name: "Heinz Ketchup",
          guid: "28",
          penetration: 9,
          change: 0,
          image_url: "/",
        },
        {
          name: "Oatly Oat Milk",
          guid: "29",
          penetration: 10,
          change: -1,
          image_url: "/",
        },
        {
          name: "Rold Gold",
          guid: "30",
          penetration: 8,
          change: 0,
          image_url: "/",
        },
        {
          name: "Tazo Awake",
          guid: "31",
          penetration: 7,
          change: 0,
          image_url: "/",
        },
        {
          name: "Heineken",
          guid: "32",
          penetration: 6,
          change: -1,
          image_url: "/",
        },
        { name: "Bai", guid: "33", penetration: 7, change: 0, image_url: "/" },
        {
          name: "Post Toasties",
          guid: "34",
          penetration: 7,
          change: 0,
          image_url: "/",
        },
        {
          name: "KIND bar",
          guid: "35",
          penetration: 7,
          change: 0,
          image_url: "/",
        },
        {
          name: "Lindt Excellence",
          guid: "36",
          penetration: 7,
          change: 0,
          image_url: "/",
        },
      ],
    },
  ];

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            Product Lifecycle Stage Distribution For Fastest Growing Product in
            Each Category
          </Heading>
        </div>
        <div>
          <Dropdown
            options={MENU_ADOPTION_CURVE_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="overflow-x-auto">
        <div className="grid min-w-[1116px] grid-cols-4 gap-4 p-4">
          {items.map((item, key) => (
            <ProductLifecycleItem
              key={`item-${key}-${item.data.length}`}
              type={item.type}
              items={item.data}
            />
          ))}
        </div>
      </div>
    </Card>
  );
};

export default ProductLifecycle;
