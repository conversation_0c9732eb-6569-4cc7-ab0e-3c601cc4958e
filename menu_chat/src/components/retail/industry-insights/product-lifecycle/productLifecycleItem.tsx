import { useEffect, useRef } from "react";
import { twMerge } from "tailwind-merge";
import Tag from "@/components/base/tag";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import {
  IconCircle,
  IconCircleFilled,
  IconDonut,
  IconIncrease,
  IconCircleArrowDown,
} from "@/components/icons";
import HoverCard from "@/components/base/hoverCard";
import Image from "next/image";
import titleCase from "voca/title_case";
import { isValidUrl } from "@/components/utils/helpers";
import Link from "next/link";

interface ProductLifecycleItemProps {
  className?: string;
  type: "new" | "emergence" | "mainstream" | "declining";
  items: Array<{
    name: string;
    guid: string;
    penetration: number;
    change: number;
    image_url?: string | null;
  }>;
}

const typeConfig = {
  new: { variant: "magenta", icon: IconDonut, label: "New", description: "Innovation products owned by a few retailers", height: 130, curveBias: 30 },
  emergence: { variant: "yellow", icon: IconCircle, label: "Emergence", description: "Products owned by up to 10 retailers", height: 240, curveBias: 100 },
  mainstream: { variant: "violet", icon: IconCircleFilled, label: "Mainstream", description: "Products owned by almost all retailers", height: 350, curveBias: 100 },
  declining: { variant: "salmon", icon: IconCircleArrowDown, label: "Declining", description: "Retailers are reducing the product offering", height: 300, curveBias: -90 },
} as const;

const ProductLifecycleItem = ({ className, type, items }: ProductLifecycleItemProps) => {
  const config = typeConfig[type];
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const tags = Array.from(container.children) as HTMLElement[];

    const minGap = 14;

    const curveBiasFactor = config.curveBias;

    let currentX = minGap;
    let currentY = minGap;
    let rowHeight = 0;

    tags.forEach((tag) => {
      const tagWidth = tag.offsetWidth;
      const tagHeight = tag.offsetHeight;

      if (currentX + tagWidth + minGap > containerRect.width) {
        currentX = minGap;
        currentY += rowHeight + minGap;
        rowHeight = 0;
      }

      const normalizedX = currentX / containerRect.width;
      const curveBias = normalizedX * curveBiasFactor;
      const adjustedY = currentY - curveBias;

      const willOverflow = adjustedY + tagHeight > containerRect.height;

      if (willOverflow) {
        tag.style.display = "none";
      } else {
        tag.style.position = "absolute";
        tag.style.transition = "transform 0.5s ease";
        tag.style.transform = `translate(${currentX}px, ${adjustedY}px)`;

        currentX += tagWidth + minGap;
        rowHeight = Math.max(rowHeight, tagHeight);
      }
    });
  }, [config.curveBias]);

  return (
    <div className={twMerge("flex flex-col items-start gap-4", className)}>
      <div className="border-muted-200 oveflow-hidden relative flex h-[465px] w-full flex-col justify-end rounded-lg border p-1">
        {type === "new" && (/* SVG kept */
          <svg xmlns="http://www.w3.org/2000/svg" width={259} height={465} fill="none" viewBox="0 0 259 465" className="absolute inset-0 h-full w-full rounded-lg" preserveAspectRatio="none">
            <g clipPath="url(#a)"><path fill="#FAEFFB" d="M105 288.504C81.603 297.398-5 327.208-5 327.208v137.43h1149V237.605c-101-149-205-216.117-318-216.117s-274 83.015-417 145.015c-143 62-183 76.001-304 122.001Z"/><path stroke="#DF9DE4" strokeWidth="2" d="M-5 327.208s86.603-29.81 110-38.704c121-46 161-60.001 304-122.001 143-62 304-145.015 417-145.015s217 67.118 318 216.117"/></g>
            <defs><clipPath id="a"><path fill="#fff" d="M0 8a8 8 0 0 1 8-8h243a8 8 0 0 1 8 8v449a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/></clipPath></defs>
          </svg>
        )}
        {type === "emergence" && (/* SVG kept */
          <svg xmlns="http://www.w3.org/2000/svg" width={259} height={465} viewBox="0 0 259 465" fill="none" className="absolute inset-0 h-full w-full rounded-lg" preserveAspectRatio="none">
            <g stroke="#FFC933" clipPath="url(#a)"><path fill="#FFF3D2" d="M-172 289.504c-23.397 8.894-110 38.704-110 38.704v137.43H867V238.605c-101-149-205-216.117-318-216.117s-274 83.015-417 145.015c-143 62-183 76.001-304 122.001Z"/><path strokeWidth="2" d="M-282 328.208s86.603-29.81 110-38.704c121-46 161-60.001 304-122.001 143-62 304-145.015 417-145.015s217 67.118 318 216.117"/></g>
            <defs><clipPath id="a"><path fill="#fff" d="M0 8a8 8 0 0 1 8-8h243a8 8 0 0 1 8 8v449a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/></clipPath></defs>
          </svg>
        )}
        {type === "mainstream" && (/* SVG kept */
          <svg xmlns="http://www.w3.org/2000/svg" width={259} height={465} viewBox="0 0 259 465" fill="none" className="absolute inset-0 h-full w-full rounded-lg" preserveAspectRatio="none">
            <g clipPath="url(#a)"><path stroke="#DFDDCE" strokeWidth=".5" d="M28 466.492v-264M50.556 466.492v-227M73.111 466.492v-304M95.666 466.492v-183M118.222 466.492v-256M140.778 466.492v-346M163.334 466.492l-.001-290M185.889 466.492v-223M208.444 466.492v-319M231 466.492v-261"/><path fill="#F2EFFF" stroke="#BDA4CB" d="M-442 289.996c-23.397 8.895-110 38.704-110 38.704v137.43H597V239.098C496 90.098 392 22.98 279 22.98S5 105.997-138 167.996c-143 62-183 76-304 122Z"/><path stroke="#BDA4CB" strokeWidth="2" d="M-552 327.7s86.603-29.809 110-38.704c121-46 161-60 304-122S166 21.98 279 21.98s217 67.118 318 216.118"/></g>
            <defs><clipPath id="a"><path fill="#fff" d="M0 8.492a8 8 0 0 1 8-8h243a8 8 0 0 1 8 8v449a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8v-449Z"/></clipPath></defs>
          </svg>
        )}
        {type === "declining" && (/* SVG kept */
          <svg xmlns="http://www.w3.org/2000/svg" width={259} height={465} viewBox="0 0 259 465" fill="none" className="absolute inset-0 h-full w-full rounded-lg" preserveAspectRatio="none">
            <g stroke="#FF9985" clipPath="url(#a)"><path fill="#FBE8E3" d="M-722 289.035c-23.397 8.895-110 38.704-110 38.704v137.43H317V238.137C216 89.137 112 22.019-1 22.019s-274 83.016-417 145.016c-143 62-183 76-304 122Z"/><path strokeWidth="2" d="M-832 327.208s86.603-29.81 110-38.704c121-46 161-60.001 304-122.001 143-62 304-145.015 417-145.015s217 67.118 318 216.117"/></g>
            <defs><clipPath id="a"><path fill="#fff" d="M0 8a8 8 0 0 1 8-8h243a8 8 0 0 1 8 8v449a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/></clipPath></defs>
          </svg>
        )}
        <div
          ref={containerRef}
          className={twMerge(
            type === "new" && `h-[130px]`,
            type === "emergence" && `h-[240px]`,
            type === "mainstream" && `h-[350px]`,
            type === "declining" && `h-[300px]`,
          )}
        >
          {(items ?? []).map((item, index) => (
            <HoverCard
              key={index}
              content={
                <div className="flex min-w-44 flex-col gap-2">
                  <div className="flex items-center justify-start gap-2">
                    {item.image_url && isValidUrl(item.image_url) ? (
                      <Image
                        src={item.image_url}
                        alt={item.name}
                        width={36}
                        height={36}
                        className="size-9 rounded-sm object-cover"
                      />
                    ) : (
                      <div className="flex size-9 items-center justify-center rounded-sm bg-gray-200">
                        <span className="text-xs text-gray-500">N/A</span>
                      </div>
                    )}
                    <Tag size="sm">
                      {titleCase(item.name.slice(0, 20))}
                      {item.name.length > 20 && <span>...</span>}
                    </Tag>
                  </div>
                  <div className="flex items-end justify-between gap-2">
                    <div className="flex flex-col gap-1">
                      <Heading level={3}>{item.penetration?.toFixed(2)}%</Heading>
                      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                        Penetration
                      </Paragraph>
                    </div>
                    <div className="flex flex-col gap-1 text-right">
                      <Tag variant="greenOutline">
                        <IconIncrease size={16} />
                        {item.change?.toFixed(2)}%
                      </Tag>
                      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                        change
                      </Paragraph>
                    </div>
                  </div>
                </div>
              }
            >
              <Link href={item.guid === "#" ? "#" : `/retail/details/${item.guid}`} aria-disabled={item.guid === "#"}>
                <Tag
                  variant={config.variant}
                  size="sm"
                  className={twMerge(
                    "cursor-pointer border",
                    config.variant === "yellow" && "border-[#FFC933]",
                    config.variant === "magenta" && "border-[#DF9DE4]",
                    config.variant === "violet" && "border-[#BDA4CB]",
                    config.variant === "salmon" && "border-[#FF9985]",
                  )}
                >
                  {titleCase(item.name.slice(0, 15))}
                  {item.name.length > 15 && <span>...</span>}
                </Tag>
              </Link>
            </HoverCard>
          ))}
        </div>
      </div>
      {config && (
        <div>
          <Tag variant={config.variant} className="mb-1">
            <config.icon size={16} className="text-muted-900" />
            {config.label}
          </Tag>
          <Paragraph size="xs" className="text-neutral-600">
            {titleCase(config.description)}
          </Paragraph>
        </div>
      )}
    </div>
  );
};

export default ProductLifecycleItem;
