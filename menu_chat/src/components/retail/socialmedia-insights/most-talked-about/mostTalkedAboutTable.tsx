import { useState } from "react";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import {
  IconTaste2,
  IconGraph,
  IconIncrease,
  IconDecrease,
} from "@/components/icons";
import SentimentBar from "@/components/charts/sentimentBar";

const MostTalkedAboutTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>Name</Th>
              <Th>Sentiment</Th>
              <Th>Engagement/Views</Th>
              <Th>Posts Number</Th>
              <Th>Growth</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Sour
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Umami
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 15,
                    "somewhat-dislike": 35,
                    neutral: 15,
                    "somewhat-like": 10,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  2,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Tangy
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 5,
                    "somewhat-dislike": 25,
                    neutral: 30,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Salty
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Tropical
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 15,
                    "somewhat-dislike": 35,
                    neutral: 15,
                    "somewhat-like": 10,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  2,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Savory
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 5,
                    "somewhat-dislike": 25,
                    neutral: 30,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Smoky
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Spicy
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 15,
                    "somewhat-dislike": 35,
                    neutral: 15,
                    "somewhat-like": 10,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  2,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  Sweet
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 5,
                    "somewhat-dislike": 25,
                    neutral: 30,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default MostTalkedAboutTable;
