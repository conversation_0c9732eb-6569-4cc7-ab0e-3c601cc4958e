import { useState } from "react";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import Favorite from "@/components/base/favorite";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import { IconGraph, IconIncrease, IconDecrease } from "@/components/icons";
import SentimentBar from "@/components/charts/sentimentBar";

const RetailSocialMediaTrendsTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Name</Th>
              <Th>Sentiment</Th>
              <Th>Engagement/Views</Th>
              <Th>Posts Number</Th>
              <Th>Growth</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Tyson Beef Strips</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Beyond Burger</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 15,
                    "somewhat-dislike": 35,
                    neutral: 15,
                    "somewhat-like": 10,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>52,265</Td>
              <Td>52,265</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  2,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Uncle Ben’s Original</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 5,
                    "somewhat-dislike": 25,
                    neutral: 30,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>34,265</Td>
              <Td>34,265</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Dole Bananas</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 15,
                    neutral: 40,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>24,265</Td>
              <Td>24,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Wonder Bread</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  2,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Freshly Chicken Parm</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 20,
                    "somewhat-dislike": 35,
                    neutral: 25,
                    "somewhat-like": 15,
                    "strongly-like": 5,
                  }}
                />
              </Td>
              <Td>74,265</Td>
              <Td>74,265</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Heinz Tomato Ketchup</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Lay’s Classic</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 15,
                    "somewhat-dislike": 35,
                    neutral: 15,
                    "somewhat-like": 10,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>52,265</Td>
              <Td>52,265</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  2,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Nescafé Classic</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 5,
                    "somewhat-dislike": 25,
                    neutral: 30,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>34,265</Td>
              <Td>34,265</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Lipton Black Tea</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 15,
                    neutral: 40,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>24,265</Td>
              <Td>24,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Dole Bananas</Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  2,70%
                </Tag>
              </Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default RetailSocialMediaTrendsTable;
