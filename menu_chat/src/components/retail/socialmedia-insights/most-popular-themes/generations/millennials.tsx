import BarChart, { Bar<PERSON>hartItem } from "@/components/charts/barChart";

// TODO: get actual data from API
const chartData: BarChartItem[] = [
  {
    id: 1,
    label: "Health & Wellness",
    color: "#78C5E3",
    value: 55,
    symbol: "%",
  },
  { id: 2, label: "Global Flavors", color: "#FAAB61", value: 25, symbol: "%" },
  { id: 3, label: "Thai", color: "#DF9DE4", value: 10, symbol: "%" },
  { id: 4, label: "Mexican", color: "#FF9985", value: 30, symbol: "%" },
  { id: 5, label: "Asian", color: "#BDA4CB", value: 75, symbol: "%" },
];

const Millennials = () => {
  return (
    <div className="border-muted-200 rounded-lg border p-2">
      <BarChart dataItems={chartData} symbol="%" maxValue={100} height={270} />
    </div>
  );
};

export default Millennials;
