import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import Tabs, { TabContent } from "@/components/base/tabs";
import { CHART_DROPDOWN } from "@/data/ingredients";
import GenAlpha from "./generations/genAlpha";
import GenZ from "./generations/genZ";
import Millennials from "./generations/millennials";
import GenX from "./generations/genX";
import BabyBoomers from "./generations/babyBoomers";

interface MostPopularThemesProps {
  className?: string;
}

const MostPopularThemes = ({ className }: MostPopularThemesProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            Most Popular Themes on Social Media Among Generations
          </Heading>
          <Info
            tooltipId="most-popular-themes"
            content="Top 5 most popular themes for the 5 generations. Based on the social media posts and trends"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <Tabs
          navigation={[
            "Gen Alpha",
            "Gen Z",
            "Millennials",
            "Gen X",
            "Baby Boomers",
          ]}
        >
          <TabContent>
            <GenAlpha />
          </TabContent>
          <TabContent>
            <GenZ />
          </TabContent>
          <TabContent>
            <Millennials />
          </TabContent>
          <TabContent>
            <GenX />
          </TabContent>
          <TabContent>
            <BabyBoomers />
          </TabContent>
        </Tabs>
      </div>
    </Card>
  );
};

export default MostPopularThemes;
