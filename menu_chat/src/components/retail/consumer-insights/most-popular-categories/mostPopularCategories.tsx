import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";

interface MostPopularCategoriesProps {
  className?: string;
}

const MostPopularCategories = ({ className }: MostPopularCategoriesProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular Product Categories</Heading>
          <Info
            tooltipId="most-popular-product-categories"
            content="Most popular product categories used in specific diet types. Data gathered from social media on February 2025."
          />
        </div>
        <Link href="/retail/fastest-growing-product">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Liked By</Th>
            <Th>Products Number</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag>Walmart</Tag>
            </Td>
            <Td>85.4%</Td>
            <Td>9534</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Bakery</Tag>
            </Td>
            <Td>42.4%</Td>
            <Td>3423</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Meat</Tag>
            </Td>
            <Td>38.8%</Td>
            <Td>8754</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostPopularCategories;
