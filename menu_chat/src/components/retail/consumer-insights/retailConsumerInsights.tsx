import MostTalkedAboutProducts from "./most-talked-about-products/mostTalkedAboutProducts";
import MostLikedProducts from "./most-liked-products/mostLikedProducts";
import TopProductClaims from "./top-product-claims/topProductClaims";
import ConsumerFavoriteRetailers from "./consumers-favorite-retailers/consumersFavoriteRetailers";
import MostPopularCategories from "./most-popular-categories/mostPopularCategories";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const RetailConsumerInsights = () => {
  return (
    <section className="mt-[60px] py-4">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-2 gap-5">
          <MostTalkedAboutProducts />
          <MostLikedProducts />
        </div>
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-7">
            <TopProductClaims />
          </div>
          <div className="col-span-5 flex flex-col gap-5">
            <ConsumerFavoriteRetailers />
            <MostPopularCategories />
          </div>
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default RetailConsumerInsights;
