import BarChart, { BarChartItem } from "@/components/charts/barChart";

// TODO: get actual data from API
const chartData: BarChartItem[] = [
  {
    id: 1,
    label: "High Protein",
    color: "#78C5E3",
    value: 45,
    symbol: "%",
  },
  { id: 2, label: "Low Sugar", color: "#FAAB61", value: 65, symbol: "%" },
  { id: 3, label: "Omega 3", color: "#DF9DE4", value: 25, symbol: "%" },
  { id: 4, label: "Fiber", color: "#FF9985", value: 30, symbol: "%" },
  { id: 5, label: "Eco-Friendly", color: "#BDA4CB", value: 45, symbol: "%" },
];

const GenZ = () => {
  return (
    <div className="border-muted-100 rounded-lg border p-4">
      <BarChart dataItems={chartData} symbol="%" maxValue={100} />
    </div>
  );
};

export default GenZ;
