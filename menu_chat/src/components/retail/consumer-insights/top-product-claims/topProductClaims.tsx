import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Button from "@/components/base/button";
import Tabs, { TabContent } from "@/components/base/tabs";
import GenAlpha from "./generations/genAlpha";
import GenZ from "./generations/genZ";
import BabyBoomers from "./generations/babyBoomers";
import GenX from "./generations/genX";
import Millennials from "./generations/millennials";

interface TopProductClaimsProps {
  className?: string;
}

const TopProductClaims = ({ className }: TopProductClaimsProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Top Product Claims Among Generations</Heading>
          <Info
            tooltipId="top-product-claims"
            content="Top 5 product claims for the 5 generations. Data gathered from social media on February 2025."
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <Tabs
          navigation={[
            "Gen Alpha",
            "Gen Z",
            "Millennials",
            "Gen X",
            "Baby Boomers",
          ]}
        >
          <TabContent>
            <GenAlpha />
          </TabContent>
          <TabContent>
            <GenZ />
          </TabContent>
          <TabContent>
            <Millennials />
          </TabContent>
          <TabContent>
            <GenX />
          </TabContent>
          <TabContent>
            <BabyBoomers />
          </TabContent>
        </Tabs>
      </div>
    </Card>
  );
};

export default TopProductClaims;
