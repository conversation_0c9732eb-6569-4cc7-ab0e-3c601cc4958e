import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";

interface MostLikedProductsProps {
  className?: string;
}

const MostLikedProducts = ({ className }: MostLikedProductsProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Liked Products by Consumers</Heading>
          <Info
            tooltipId="most-liked-products"
            content="Top 5 most liked ingredients based on the survey conducted in February 2025. (130,452 participants)"
          />
        </div>
        <Link href="/retail/most-liked-products">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Saturation</Th>
            <Th>Change</Th>
            <Th>Liked By</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag>Frapuccino</Tag>
            </Td>
            <Td>
              <Tag size="xs" variant="redOutline">
                Low
              </Tag>
            </Td>
            <Td>5.70%</Td>
            <Td>87.44%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Lamb shank</Tag>
            </Td>
            <Td>
              <Tag size="xs" variant="greenOutline">
                High
              </Tag>
            </Td>
            <Td>2.52%</Td>
            <Td>15.5%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Frittata</Tag>
            </Td>
            <Td>
              <Tag size="xs" variant="blueOutline">
                Medium
              </Tag>
            </Td>
            <Td>15.44%</Td>
            <Td>12.5%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Cauliflower rice</Tag>
            </Td>
            <Td>
              <Tag size="xs" variant="blueOutline">
                Medium
              </Tag>
            </Td>
            <Td>2.44%</Td>
            <Td>8.44%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Rasberry Tart</Tag>
            </Td>
            <Td>
              <Tag size="xs" variant="greenOutline">
                High
              </Tag>
            </Td>
            <Td>12.44%</Td>
            <Td>32.44%</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostLikedProducts;
