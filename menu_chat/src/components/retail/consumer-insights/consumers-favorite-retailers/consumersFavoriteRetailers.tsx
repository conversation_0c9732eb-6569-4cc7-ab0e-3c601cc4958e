import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";

interface ConsumerFavoriteRetailersProps {
  className?: string;
}

const ConsumerFavoriteRetailers = ({
  className,
}: ConsumerFavoriteRetailersProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Consumers’ Favorite Retailers</Heading>
          <Info
            tooltipId="consumers-favorite-retailers"
            content="Based on the survey data collected in February 2025. (130,452 participants)"
          />
        </div>
        <Link href="/retail/consumers-favorite-retailers">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Liked By</Th>
            <Th># of Products</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>Walmart</Td>
            <Td>85.4%</Td>
            <Td>9,008</Td>
          </Tr>
          <Tr>
            <Td>Krooger</Td>
            <Td>12.9%</Td>
            <Td>8,006</Td>
          </Tr>
          <Tr>
            <Td>Amazon</Td>
            <Td>17.9%</Td>
            <Td>8,667</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default ConsumerFavoriteRetailers;
