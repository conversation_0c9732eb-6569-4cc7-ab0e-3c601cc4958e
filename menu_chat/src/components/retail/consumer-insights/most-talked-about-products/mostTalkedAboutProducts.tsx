import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import SentimentBar from "@/components/charts/sentimentBar";
import { IconGraph, IconIncrease, IconDecrease } from "@/components/icons";

interface MostTalkedAboutProductsProps {
  className?: string;
}

const MostTalkedAboutProducts = ({
  className,
}: MostTalkedAboutProductsProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Talked About Products</Heading>
          <Info
            tooltipId="most-talked-about-products"
            content="Top 5 most talked about products and their sentiments. Data gathered from social media on February 2025."
          />
        </div>
        <Link href="/retail/most-talked-about-products">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Sentiment</Th>
            <Th>Penetration</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag>Lundberg Organic Rice</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  "strongly-dislike": 10,
                  "somewhat-dislike": 25,
                  neutral: 25,
                  "somewhat-like": 25,
                  "strongly-like": 15,
                }}
              />
            </Td>
            <Td>
              <Tag size="xs" variant="blueOutline">
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Lightlife tempeh</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  "somewhat-dislike": 10,
                  neutral: 45,
                  "somewhat-like": 20,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>
              <Tag size="xs" variant="greenOutline">
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>De Cecco Penne</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  neutral: 60,
                  "somewhat-like": 15,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>
              <Tag size="xs" variant="redOutline">
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Sunkist Oranges</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  neutral: 55,
                  "somewhat-like": 30,
                  "strongly-like": 15,
                }}
              />
            </Td>
            <Td>
              <Tag size="xs" variant="blueOutline">
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Ben & Jerry’s Cherry...</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  "somewhat-dislike": 15,
                  neutral: 40,
                  "somewhat-like": 20,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>
              <Tag size="xs" variant="redOutline">
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostTalkedAboutProducts;
