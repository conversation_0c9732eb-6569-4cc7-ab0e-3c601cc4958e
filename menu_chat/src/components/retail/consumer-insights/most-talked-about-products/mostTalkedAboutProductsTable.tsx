import { useState } from "react";
import {
  IconGraph,
  IconIncrease,
  IconDecrease,
  IconDonut,
  IconCircle,
  IconCircleFilled,
  IconCircleArrowDown,
  IconWorkflow,
} from "@/components/icons";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import RetailFilters from "@/components/retail/retailFilters";
import Favorite from "@/components/base/favorite";
import SentimentBar from "@/components/charts/sentimentBar";

const MostTalkedAboutProductsTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <RetailFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Name</Th>
              <Th>Category</Th>
              <Th>Lifecycle Stage</Th>
              <Th>Sentiment</Th>
              <Th>Social Mentions</Th>
              <Th>Liked By</Th>
              <Th>Change</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Tyson Beef Strips</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Meat
                </Tag>
              </Td>
              <Td>
                <Tag variant="magenta">
                  <IconDonut size={16} />
                  New
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>98,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Beyond Burger</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Natural & Org...
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} />
                  Emergence
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>2,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  24%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Uncle Ben’s Orig...</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Frozen
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    neutral: 60,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>42,572</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Dole Bananas</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Fresh Fruit & Veg...
                </Tag>
              </Td>
              <Td>
                <Tag variant="red">
                  <IconCircleArrowDown size={16} />
                  Declining
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "somewhat-dislike": 15,
                    neutral: 40,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>12,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Wonder Bread</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Bakery
                </Tag>
              </Td>
              <Td>
                <Tag variant="magenta">
                  <IconDonut size={16} />
                  New
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>98,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Freshly Chicken Parm</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Meat
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} />
                  Emergence
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>2,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  24%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Heinz Tomato Ket...</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Deli
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    neutral: 60,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>42,572</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Dole Bananas</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Fresh Fruit & Veg...
                </Tag>
              </Td>
              <Td>
                <Tag variant="red">
                  <IconCircleArrowDown size={16} />
                  Declining
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "somewhat-dislike": 15,
                    neutral: 40,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>12,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Tyson Beef Strips</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Meat
                </Tag>
              </Td>
              <Td>
                <Tag variant="magenta">
                  <IconDonut size={16} />
                  New
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>98,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Beyond Burger</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Natural & Org...
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} />
                  Emergence
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>2,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  24%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Uncle Ben’s Orig...</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Frozen
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    neutral: 60,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>42,572</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Dole Bananas</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Fresh Fruit & Veg...
                </Tag>
              </Td>
              <Td>
                <Tag variant="red">
                  <IconCircleArrowDown size={16} />
                  Declining
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "somewhat-dislike": 15,
                    neutral: 40,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>12,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Wonder Bread</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Bakery
                </Tag>
              </Td>
              <Td>
                <Tag variant="magenta">
                  <IconDonut size={16} />
                  New
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[200px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>98,332</Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0%
                </Tag>
              </Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default MostTalkedAboutProductsTable;
