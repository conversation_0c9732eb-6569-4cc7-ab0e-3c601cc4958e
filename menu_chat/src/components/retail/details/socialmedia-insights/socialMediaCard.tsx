import Card from "@/components/base/card";
import Paragraph from "@/components/base/paragraph";
import { IconInstagram, IconFacebook } from "@/components/icons";
import { SafeImage } from "@/components/utils/helpers";

interface SocialMediaCardProps {
  imageSrc: string;
  imageAlt: string;
  platform: "instagram" | "facebook";
  username: string;
  socialMediaLabel?: string;
  engagementLabel?: string;
  engagementValue: string | number;
  className?: string;
  imageClassName?: string;
}

const SocialMediaCard = ({
  imageSrc,
  imageAlt,
  platform,
  username,
  socialMediaLabel = "Social Media",
  engagementLabel = "Engagement & Views",
  engagementValue,
  className = "",
  imageClassName = "h-[140px] w-full rounded-lg object-cover",
}: SocialMediaCardProps) => {
  const getPlatformIcon = () => {
    switch (platform.toLowerCase()) {
      case "instagram":
        return <IconInstagram size={20} className="min-w-5 text-neutral-500" />;
      case "facebook":
        return <IconFacebook size={20} className="min-w-5 text-neutral-500" />;
      default:
        return <IconInstagram size={20} className="min-w-5 text-neutral-500" />;
    }
  };

  const getPlatformName = () => {
    switch (platform.toLowerCase()) {
      case "instagram":
        return "Instagram";
      case "facebook":
        return "Facebook";
      default:
        return "Instagram";
    }
  };

  return (
    <Card>
      <div className={`flex flex-col gap-2 p-2 ${className}`}>
        <SafeImage
          src={imageSrc}
          alt={imageAlt}
          width={100}
          height={140}
          className={imageClassName}
        />
        <div className="px-2 py-1">
          <Paragraph
            size="sm"
            className="flex items-center gap-1 font-semibold"
          >
            {getPlatformIcon()}
            <span className="inline-block max-w-[70%] truncate text-neutral-700">
              {username}
            </span>
          </Paragraph>
        </div>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="font-archivo text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
              {socialMediaLabel}
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {getPlatformName()}
            </Paragraph>
          </div>
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="font-archivo text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
              {engagementLabel}
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {engagementValue}
            </Paragraph>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SocialMediaCard;
