import { useState } from "react";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import TagCloudChart from "@/components/charts/tagCloudChart";

const data = [
  { value: "Ice Cream", count: 100, color: "#DF9DE4" },
  { value: "Sandwich", count: 85, color: "#FF9985" },
  { value: "Gluten Free", count: 72, color: "#FF9985" },
  { value: "Sweet", count: 68, color: "#BDA4CB" },
  { value: "Healthy", count: 55, color: "#FFC933" },
  { value: "Green", count: 45, color: "#A2D161" },
  { value: "Vegan", count: 38, color: "#FFC933" },
  { value: "Cabbage", count: 28, color: "#BDA4CB" },
  { value: "Chinese", count: 22, color: "#FAAB61" },
  { value: "Spicy", count: 18, color: "#FAAB61" },
  { value: "Fresh", count: 15, color: "#A2D161" },
  { value: "Organic", count: 12, color: "#78C5E3" },
];

const WordCloud = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Word Cloud</Heading>
          <Info
            tooltipId="word-cloud-tooltip"
            content="Most frequently used words associated with this product"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="border-muted-100 m-3 min-h-[500px] rounded-lg border p-3">
        <div className="mx-auto flex min-h-[500px] max-w-[400px] flex-wrap items-center justify-center text-center">
          <TagCloudChart data={data} minSize={12} maxSize={28} />
        </div>
      </div>
    </Card>
  );
};

export default WordCloud;
