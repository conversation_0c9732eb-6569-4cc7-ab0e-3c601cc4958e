import { useState } from "react";
import Card, { <PERSON>Header } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore, IconIncrease } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";

const lineChartData: LineSeriesItem[] = [
  {
    id: 1,
    label: "Ice Cream Sandwich",
    color: "#FF6D56",
    data: [0, 3, 2.5, 3.0, 5.0, 6, 8],
  },
];

const SocialConversations = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Social Conversations</Heading>
          <Info
            tooltipId="social-conversations-tooltip"
            content="Social media mentions in comments or posts during the selected period"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="flex flex-col gap-4 p-4">
        <div className="border-muted-100 flex items-center justify-between gap-2 rounded-sm border p-3">
          <div>
            <Heading level={5}>Compared</Heading>
            <Paragraph size="xs" className="font-medium text-neutral-600">
              to Previous Period
            </Paragraph>
          </div>
          <div className="flex items-end gap-2">
            <Heading level={2} className="flex items-end gap-1.5">
              <span className="text-4">+</span>
              1.18%
            </Heading>
            <Tag variant="greenOutline" className="px-1">
              <IconIncrease size={16} />
            </Tag>
          </div>
        </div>
        <div className="border-muted-100 rounded-lg border p-3">
          <LineChart
            dataItems={lineChartData}
            labels={[
              "27 May",
              "28 May",
              "29 May",
              "30 May",
              "31 May",
              "1 Jun",
              "2 Jun",
            ]}
            showLegend={false}
            symbol=""
            height={180}
          />
        </div>
      </div>
    </Card>
  );
};

export default SocialConversations;
