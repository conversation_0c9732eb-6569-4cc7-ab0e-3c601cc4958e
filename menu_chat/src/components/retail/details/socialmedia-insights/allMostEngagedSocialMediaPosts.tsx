import DataSourcesReports from "@/components/shared/dataSourcesReports";
import SocialMediaCard from "@/components/retail/details/socialmedia-insights/socialMediaCard";

const AllMostEngagedSocialMediaPosts = () => {
  return (
    <section className="pt-4 pb-24">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-4 gap-5 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="Iblacksmithchicagorestaurant"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="facebook"
            username="Ifuasiansushiplace"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="chinatownmichigan"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="facebook"
            username="Iblacksmithchicagorestaurant"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="Iblacksmithchicagorestaurant"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="Ifuasiansushiplace"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="chinatownmichigan"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="Iblacksmithchicagorestaurant"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="Iblacksmithchicagorestaurant"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="Ifuasiansushiplace"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="chinatownmichigan"
            engagementValue="260,178"
          />
          <SocialMediaCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            platform="instagram"
            username="Iblacksmithchicagorestaurant"
            engagementValue="260,178"
          />
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default AllMostEngagedSocialMediaPosts;
