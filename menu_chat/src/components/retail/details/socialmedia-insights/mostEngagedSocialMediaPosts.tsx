import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Image from "next/image";
import Paragraph from "@/components/base/paragraph";
import { IconInstagram, IconFacebook, IconTiktok } from "@/components/icons";
import Button from "@/components/base/button";
import Link from "next/link";
import { useRetailProduct } from "@/contexts/RetailProductContext";

interface MostEngagedSocialMediaPostsProps {
  className?: string;
}

const MostEngagedSocialMediaPosts = ({
  className,
}: MostEngagedSocialMediaPostsProps) => {
  const { id } = useRetailProduct();

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Engaged Social Media Posts</Heading>
          <Info
            tooltipId="most-engaged-social-media-posts"
            content="Social Media Posts with highest number of engagements/views"
          />
        </div>
        <Link href={`/retail/details/${id}/all-social-media-posts`}>
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="grid grid-cols-4 gap-2 p-4">
        <div className="border-muted-100 flex flex-col gap-2 rounded-lg border p-2">
          <Image
            src="/assets/images/<EMAIL>"
            alt="chocolates"
            width={100}
            height={140}
            className="h-[140px] w-full rounded-lg object-cover"
          />
          <div className="px-2 py-1">
            <Paragraph
              size="sm"
              className="flex items-center gap-1 font-semibold"
            >
              <IconInstagram size={20} className="min-w-5 text-neutral-500" />
              <span className="inline-block max-w-48 truncate">
                Iblacksmithchicagorestaurant
              </span>
            </Paragraph>
          </div>
          <div className="flex w-full flex-col">
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
                Social Media
              </Paragraph>
              <Paragraph size="sm" className="font-medium">
                Instagram
              </Paragraph>
            </div>
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
                Engagement & Views
              </Paragraph>
              <Paragraph size="sm" className="font-medium">
                260,178
              </Paragraph>
            </div>
          </div>
        </div>
        <div className="border-muted-100 flex flex-col gap-2 rounded-lg border p-2">
          <Image
            src="/assets/images/<EMAIL>"
            alt="chocolates"
            width={100}
            height={140}
            className="h-[140px] w-full rounded-lg object-cover"
          />
          <div className="px-2 py-1">
            <Paragraph
              size="sm"
              className="flex items-center gap-1 font-semibold"
            >
              <IconFacebook size={20} className="min-w-5 text-neutral-500" />
              <span className="inline-block max-w-48 truncate">
                Ifuasiansushiplace
              </span>
            </Paragraph>
          </div>
          <div className="flex w-full flex-col">
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                Social Media
              </Paragraph>
              <Paragraph size="sm" className="font-medium">
                Facebook
              </Paragraph>
            </div>
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                Engagement & Views
              </Paragraph>
              <Paragraph size="sm" className="font-medium">
                260,178
              </Paragraph>
            </div>
          </div>
        </div>
        <div className="border-muted-100 flex flex-col gap-2 rounded-lg border p-2">
          <Image
            src="/assets/images/<EMAIL>"
            alt="chocolates"
            width={100}
            height={140}
            className="h-[140px] w-full rounded-lg object-cover"
          />
          <div className="px-2 py-1">
            <Paragraph
              size="sm"
              className="flex items-center gap-1 font-semibold"
            >
              <IconTiktok size={20} className="min-w-5 text-neutral-500" />
              <span className="inline-block max-w-48 truncate">
                chinatownmichigan
              </span>
            </Paragraph>
          </div>
          <div className="flex w-full flex-col">
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                Social Media
              </Paragraph>
              <Paragraph size="sm" className="font-medium">
                Tiktok
              </Paragraph>
            </div>
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                Engagement & Views
              </Paragraph>
              <Paragraph size="sm" className="font-medium">
                260,178
              </Paragraph>
            </div>
          </div>
        </div>
        <div className="border-muted-100 flex flex-col gap-2 rounded-lg border p-2">
          <Image
            src="/assets/images/<EMAIL>"
            alt="chocolates"
            width={100}
            height={140}
            className="h-[140px] w-full rounded-lg object-cover"
          />
          <div className="px-2 py-1">
            <Paragraph
              size="sm"
              className="inline-flex items-center gap-1 font-semibold"
            >
              <IconInstagram size={20} className="min-w-5 text-neutral-500" />
              <span className="inline-block max-w-48 truncate">
                Iblacksmithchicagorestaurant
              </span>
            </Paragraph>
          </div>
          <div className="flex w-full flex-col">
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                Social Media
              </Paragraph>
              <Paragraph size="sm" className="font-medium">
                Instagram
              </Paragraph>
            </div>
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                Engagement & Views
              </Paragraph>
              <Paragraph size="sm" className="font-medium">
                260,178
              </Paragraph>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MostEngagedSocialMediaPosts;
