import Card, { <PERSON>Header } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import {
  IconFacebook,
  IconInstagram,
  IconTiktok,
  IconLinkedin,
  IconYoutube,
  IconGraph,
  IconIncrease,
  IconDecrease,
} from "@/components/icons";

const SocialMediaPopularity = () => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Social Media Popularity</Heading>
          <Info
            tooltipId="social-media-popularity-tooltip"
            content="Product popularity on social media by average engagement"
          />
        </div>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Avg. Engagement</Th>
            <Th>Post Number</Th>
            <Th>Growth</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag variant="ghost">
                <IconInstagram size={16} className="text-neutral-500" />
                <span>Instagram</span>
              </Tag>
            </Td>
            <Td>234,543</Td>
            <Td>26,427</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="ghost">
                <IconTiktok size={16} className="text-neutral-500" />
                <span>TikTok</span>
              </Tag>
            </Td>
            <Td>234,543</Td>
            <Td>26,427</Td>
            <Td>
              <Tag
                size="xs"
                variant="redOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="ghost">
                <IconFacebook size={16} className="text-neutral-500" />
                <span>Facebook</span>
              </Tag>
            </Td>
            <Td>234,543</Td>
            <Td>26,427</Td>
            <Td>
              <Tag
                size="xs"
                variant="blueOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="ghost">
                <IconLinkedin size={16} className="text-neutral-500" />
                <span>LinkedIn</span>
              </Tag>
            </Td>
            <Td>234,543</Td>
            <Td>26,427</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="ghost">
                <IconYoutube size={16} className="text-neutral-500" />
                <span>YouTube</span>
              </Tag>
            </Td>
            <Td>234,543</Td>
            <Td>26,427</Td>
            <Td>
              <Tag
                size="xs"
                variant="redOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default SocialMediaPopularity;
