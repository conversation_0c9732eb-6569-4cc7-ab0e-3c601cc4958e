import { useState, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import DataSourcesReports from "@/components/shared/dataSourcesReports";
import Card from "@/components/base/card";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import Pagination from "@/components/table/pagination";
import { SafeImage } from "@/components/utils/helpers";
import { useRetailProduct } from "@/contexts/RetailProductContext";
import { getRetailOnShelfFilters, getRetailProductOnShelf, type RetailProductOnShelfResponse } from "@/api/retailer_products";
import titleCase from "voca/title_case";
import { IconRetail } from "@/components/icons";
import RetailFilters from "@/components/retail/retailFilters";
import { RETAIL_ONSHELF_SORT } from "@/data/retail";
import { exportToCSV } from "@/lib/exportCsv";

const RetailDetailsOnShelf = () => {
  const { data: session } = useSession();
  const retail = useRetailProduct();

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<RetailProductTableFilters>({ l0: [], l1: [], brand: [] });
  const [sort, setSort] = useState<string>("");

  const auth = session?.user.authorization as string;

  const { data: filterOptions } = useQuery<RetailProductTableFilters>({
    queryFn: () => getRetailOnShelfFilters({ auth, id: retail.id }),
    queryKey: ["retail_on_shelf_filters", retail.id, auth],
    enabled: !!auth && !!retail?.id,
    staleTime: 24 * 60 * 60 * 1000,
  });

  const { data } = useQuery<RetailProductOnShelfResponse>({
    queryFn: () =>
      getRetailProductOnShelf({
        auth,
        id: retail.id,
        page,
        perPage: pageSize,
        search,
        sort,
        filters,
      }),
    queryKey: ["retail_on_shelf", retail.id, page, pageSize, search, sort, filters],
    enabled: !!auth && !!retail?.id,
  });

  const cards = useMemo(() => {
    return (data?.data || []).map((item) => {
      const {
        id,
        name,
        brand,
        banner_name,
        l1,
        price,
        listing_url,
        image_urls,
      } = item;

      const img = (image_urls && image_urls[0]) || "";

      return (
        <Card key={id} className="p-3">
          <SafeImage
            src={img ? img.trim() : ""}
            alt={name}
            width={100}
            height={232}
            className="mb-1 h-[140px] w-full rounded-lg object-cover"
          />
          <div className="mb-2 rounded-lg border border-neutral-200 p-2">
            <div className="mb-2 flex items-center justify-between gap-2">
              <Tag>{brand ? titleCase(brand) : ""}</Tag>
              <Paragraph size="lg" className="font-semibold">
                {price ? `$${price}` : ""}
              </Paragraph>
            </div>
            <Paragraph size="xs" className="font-medium text-neutral-600 truncate">
              {listing_url ? (
                <a href={listing_url} target="_blank" rel="noopener noreferrer" className="underline">
                  {titleCase(name)}
                </a>
              ) : (
                titleCase(name)
              )}
            </Paragraph>
          </div>

          <Paragraph
            size="sm"
            className="mb-1 flex items-center gap-2 px-2 font-medium"
          >
            <IconRetail size={16} className="text-neutral-500" />
            {banner_name ? titleCase(banner_name) : "—"}
          </Paragraph>

          <div className="flex w-full flex-col">
            <div className="flex items-center justify-between gap-2 px-3 py-1">
              <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                Category
              </Paragraph>
              <Tag variant="white">
                {l1 ? titleCase(l1) : ""}
              </Tag>
            </div>
          </div>
        </Card>
      );
    });
  }, [data?.data]);

  const handlePageChange = (newPage: number) => setPage(newPage);
  const handlePageSizeChange = (size: number) => setPageSize(size);

  const handleExport = () => {
    const rows = (data?.data ?? []).map((p) => ({
      name: p.name,
      brand: p.brand ?? "",
      banner: p.banner_name ?? "",
      category: p.l1 ?? "",
      price: p.price ?? "",
      listing_url: p.listing_url ?? "",
    }));
    exportToCSV("on_shelf_products.csv", rows as unknown as object[]);
  };

  return (
    <section className="mt-[60px] py-4">
      <RetailFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={setFilters}
        sort={sort}
        onSort={setSort}
        table={undefined}
        tableName="On Shelf Products"
        filterOptions={filterOptions}
        sortOptions={RETAIL_ONSHELF_SORT}
        onExport={handleExport}
      />

      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-4 gap-5">{cards}</div>
        <Pagination
          currentPage={page}
          perPage={pageSize}
          totalResults={data?.total_count ?? 0}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default RetailDetailsOnShelf;
