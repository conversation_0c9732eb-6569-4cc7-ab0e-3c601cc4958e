import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import Tabs, { TabContent } from "@/components/base/tabs";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Brand from "@/components/retail/details/general/most-popular/brand";
import Flavors from "@/components/retail/details/general/most-popular/flavors";
import Formats from "@/components/retail/details/general/most-popular/formats";
import Claims from "@/components/retail/details/general/most-popular/claims";
import Textures from "@/components/retail/details/general/most-popular/textures";

interface MostPopularProps {
  className?: string;
}

const MostPopular = ({ className }: MostPopularProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <Tabs
          navigation={["Brand", "Flavors", "Formats", "Claims", "Textures"]}
        >
          <TabContent>
            <Brand />
          </TabContent>
          <TabContent>
            <Flavors />
          </TabContent>
          <TabContent>
            <Formats />
          </TabContent>
          <TabContent>
            <Claims />
          </TabContent>
          <TabContent>
            <Textures />
          </TabContent>
        </Tabs>
      </div>
    </Card>
  );
};

export default MostPopular;
