import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import {
  IconMore,
  IconTaste2,
} from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getRetailProductNewFlavors } from "@/api/retailer_products";
import { useRetailProduct } from "@/contexts/RetailProductContext";

interface NewFlavorsProps {
  className?: string;
}

const NewFlavors = ({ className }: NewFlavorsProps) => {
  const { data: session } = useSession();
  const { id } = useRetailProduct();

  const { data: flavors, isLoading, error } = useQuery<RetailProductFlavor[], Error>({
    queryKey: ["retail_product_new_flavors", id],
    queryFn: () => getRetailProductNewFlavors({ auth: session?.user.authorization as string, id: id }),
    enabled: !!session?.user?.authorization,
  });

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  if (isLoading) return <div>Loading...</div>;
  if (error || !flavors) return <div>Error loading new flavors</div>;

  return (
    <Card className={twMerge("relative", className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>New Flavors</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            {/*<Th>Saturation</Th>*/}
            {/*<Th>Change</Th>*/}
            <Th>First Appeared</Th>
          </Tr>
        </Thead>
        <Tbody>
          {flavors.map((flavor) => (
            <Tr key={flavor.id}>
              <Td>
                <div className="flex items-center gap-0.5">
                  <Tag variant="white">
                    <IconTaste2 size={16} className="text-neutral-500" />
                    {flavor.name}
                  </Tag>
                </div>
              </Td>
              {/*<Td>*/}
                {/* <Tag variant="redOutline">Low</Tag>  */}
              {/*</Td>*/}
              {/*<Td>*/}
                {/* <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>  */}
              {/*</Td>*/}
              <Td>{flavor.created_at}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default NewFlavors;
