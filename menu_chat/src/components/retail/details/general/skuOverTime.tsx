import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import Tag from "@/components/base/tag";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { IconIncrease, IconMore } from "@/components/icons";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";

interface SKUOverTimeProps {
  className?: string;
}

const lineChartData: LineSeriesItem[] = [
  {
    id: 1,
    label: "Ice Cream Sandwich",
    color: "#FF6D56",
    data: [0, 3, 2.5, 3.0, 5.0, 6, 8],
  },
];

const SKUOverTime = ({ className }: SKUOverTimeProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge("relative", className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Number of SKU’s Over Time</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="flex flex-col gap-5 p-4">
        <div className="grid grid-cols-2 gap-5">
          <Card className="p-4">
            <div className="mb-6">
              <Heading level={5}>Compared</Heading>
              <Paragraph size="sm" className="font-medium text-neutral-600">
                to Previous Period
              </Paragraph>
            </div>
            <div className="flex items-end justify-between">
              <Heading level={2} className="flex items-end gap-1.5">
                <span className="text-4">+</span>3.42%
              </Heading>
              <Tag variant="greenOutline" className="px-1">
                <IconIncrease size={16} /> 12.23%
              </Tag>
            </div>
          </Card>
          <Card className="p-4">
            <div className="mb-6">
              <Heading level={5}>Prediction</Heading>
              <Paragraph size="sm" className="font-medium text-neutral-600">
                For Upcoming Period
              </Paragraph>
            </div>
            <Heading level={2} className="flex items-end gap-1.5">
              <span className="text-4">+</span>9.26%
            </Heading>
          </Card>
        </div>
        <div className="border-muted-100 rounded-lg border p-3">
          <LineChart
            dataItems={lineChartData}
            labels={[
              "27 May",
              "28 May",
              "29 May",
              "30 May",
              "31 May",
              "1 Jun",
              "2 Jun",
            ]}
            showLegend={false}
            symbol=""
            height={255}
          />
        </div>
      </div>
    </Card>
  );
};

export default SKUOverTime;
