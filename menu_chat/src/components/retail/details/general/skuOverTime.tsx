import { useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import Tag from "@/components/base/tag";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { IconIncrease, IconDecrease, IconMore } from "@/components/icons";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getRetailProductPenetrationTrendingLine } from "@/api/retailer_products";
import { useRetailProduct } from "@/contexts/RetailProductContext";

interface SKUOverTimeProps {
  className?: string;
}

const SKUOverTime = ({ className }: SKUOverTimeProps) => {
  const { data: session } = useSession();
  const { id } = useRetailProduct();
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const { data, isLoading, error } = useQuery({
    queryKey: ["retail_penetration_trending_line", id],
    queryFn: () =>
      getRetailProductPenetrationTrendingLine({
        auth: session?.user.authorization as string,
        id,
      }),
    enabled: !!session?.user?.authorization && !!id,
  });

  const labels = useMemo(() => {
    if (!data?.trend_line) return [] as string[];
    return data.trend_line.map((p) => `Q${p.q}_${p.year}`);
  }, [data]);

  const lineChartData: LineSeriesItem[] = useMemo(() => {
    if (!data?.trend_line) return [];
    const values = data.trend_line.map((p) => p.percent);
    return [
      {
        id: 1,
        label: data.gri,
        color: "#FF6D56",
        data: values,
        percentData: values,
      },
    ];
  }, [data]);

  // Compute dynamic Y max: 10% above max value, rounded to a nice number
  const yMax = useMemo(() => {
    const series = data?.trend_line?.map((p) => p.percent) ?? [];
    if (!series.length) return 100; // fallback
    const maxVal = Math.max(...series);
    const padded = Math.ceil(maxVal * 1.1);
    const roundUpToNiceNumber = (num: number) => {
      if (num <= 0) return 10;
      const magnitude = Math.pow(10, Math.floor(Math.log10(num)));
      return Math.ceil(num / magnitude) * magnitude;
    };
    return roundUpToNiceNumber(padded);
  }, [data]);

  const compared = data?.compared ?? "-";
  const prediction = data?.prediction ?? "-";
  const growthNumber = typeof data?.growth === "number" ? data.growth : 0;
  const isNegativeGrowth = growthNumber < 0;
  const growthDisplay = isNegativeGrowth
    ? `${Math.abs(growthNumber).toFixed(2)}%`
    : `${growthNumber.toFixed(2)}%`;

  return (
    <Card className={twMerge("relative", className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Number of SKU’s Over Time</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="flex flex-col gap-5 p-4">
        <div className="grid grid-cols-2 gap-5">
          <Card className="p-4">
            <div className="mb-6">
              <Heading level={5}>Compared</Heading>
              <Paragraph size="sm" className="font-medium text-neutral-600">
                to Previous Period
              </Paragraph>
            </div>
            <div className="flex items-end justify-between">
              <Heading level={2} className="flex items-end gap-1.5">
                {compared}
              </Heading>
              <Tag variant={isNegativeGrowth ? "redOutline" : "greenOutline"} className="px-1">
                {isNegativeGrowth ? (
                  <IconDecrease size={16} />
                ) : (
                  <IconIncrease size={16} />
                )}
                {growthDisplay}
              </Tag>
            </div>
          </Card>
          <Card className="p-4">
            <div className="mb-6">
              <Heading level={5}>Prediction</Heading>
              <Paragraph size="sm" className="font-medium text-neutral-600">
                For Upcoming Period
              </Paragraph>
            </div>
            <Heading level={2} className="flex items-end gap-1.5">
              {prediction}
            </Heading>
          </Card>
        </div>
        <div className="border-muted-100 rounded-lg border p-3">
          {isLoading ? (
            <div className="py-4 text-center text-neutral-600">Loading...</div>
          ) : error ? (
            <div className="py-4 text-center text-red-500">Error loading trend</div>
          ) : !data || lineChartData.length === 0 ? (
            <div className="py-4 text-center text-neutral-600">No data available</div>
          ) : (
            <LineChart
              dataItems={lineChartData}
              labels={labels}
              showLegend={false}
              symbol="%"
              height={255}
              yMax={yMax}
              yTitle={{ display: true, text: `${yMax}%` }}
            />
          )}
        </div>
      </div>
    </Card>
  );
};

export default SKUOverTime;
