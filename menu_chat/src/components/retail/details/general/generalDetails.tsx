import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import ImageSlider from "@/components/base/imageSlider";
import {
  IconCircleFilled,
  IconAroma,
  IconMenuAdoption,
  IconTaste2,
  IconGlutenfree,
} from "@/components/icons";
import CollapseCard from "@/components/base/collapseCard";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getRetailProductDetail } from "@/api/retailer_products";
import { useRetailProduct } from "@/contexts/RetailProductContext";

const GeneralDetails = () => {
  const { data: session } = useSession();
  const { id } = useRetailProduct();

  const { data, isLoading, error } = useQuery<RetailProductDetail, Error>({
    queryKey: ["retail_product_detail", id],
    queryFn: () => getRetailProductDetail({ auth: session?.user.authorization as string, id: id }),
    enabled: !!session?.user?.authorization,
  });

  if (isLoading) return <div>Loading...</div>;
  if (error || !data) return <div>Error loading product details</div>;

  return (
    <Card className="p-4">
      <div className="flex items-start justify-between gap-2">
        <div className="flex w-1/2 gap-2">
          <div className="w-1/2">
            <Heading level={3} className="mb-3">
              {data.name}
            </Heading>
            <div className="flex flex-wrap gap-1">
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                {data.category}
              </Tag>
              <Tag variant="violet">
                <IconCircleFilled size={16} />
                Mainstream
              </Tag>
            </div>
          </div>
          <div className="flex w-1/2 flex-col gap-2">
            <div className="flex flex-col gap-2 rounded-lg border border-neutral-100 p-3">
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Claims
                </Paragraph>
                <Tag variant="white">
                  <IconGlutenfree size={16} className="text-muted-500" />
                  {data.claim}
                </Tag>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Aroma
                </Paragraph>
                <div className="flex items-center gap-2">
                  <Tag variant="white">
                    <IconAroma size={16} className="text-muted-500" />
                    {data.aroma}
                  </Tag>
                </div>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Taste
                </Paragraph>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-muted-500" />
                  {data.taste}
                </Tag>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Texture
                </Paragraph>
                <Tag variant="white">
                  <IconCircleFilled size={16} className="text-muted-500" />
                  {data.texture}
                </Tag>
              </div>
            </div>
            <CollapseCard text={data.description} />
          </div>
        </div>
        <ImageSlider
          className="w-1/2"
          thumbnailPosition="right"
          images={data.images}
        />
      </div>
    </Card>
  );
};

export default GeneralDetails;
