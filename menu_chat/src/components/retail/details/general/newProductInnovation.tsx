import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import {
  IconMore,
  // IconGraph,
  // IconIncrease,
  // IconDecrease,
} from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getRetailProductNewInnovations } from "@/api/retailer_products";
import { useRetailProduct } from "@/contexts/RetailProductContext";

interface NewProductInnovationProps {
  className?: string;
}

const NewProductInnovation = ({ className }: NewProductInnovationProps) => {
  const { data: session } = useSession();
  const { id } = useRetailProduct();

  const { data: innovations, isLoading, error } = useQuery<RetailProductInnovation[], Error>({
    queryKey: ["retail_product_new_innovations", id],
    queryFn: () => getRetailProductNewInnovations({ auth: session?.user.authorization as string, id: id }),
    enabled: !!session?.user?.authorization,
  });

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  if (isLoading) return <div>Loading...</div>;
  if (error || !innovations) return <div>Error loading new product innovations</div>;

  return (
    <Card className={twMerge("relative", className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>New Product Innovation</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Brand</Th>
            <Th>Retailer Name</Th>
            <Th>First Appearance</Th>
          </Tr>
        </Thead>
        <Tbody>
          {innovations.map((innovation) => (
            <Tr key={innovation.name}>
              <Td>
                <Tag> {truncateByWords(innovation.name, 40)} </Tag>
              </Td>
              <Td>{innovation.brand}</Td>
              <Td>{innovation.retailer_name}</Td>
              <Td>{innovation.first_appearance}</Td>
              {/*<Td>*/}
                {/* <Tag variant="greenOutline">High</Tag> // */}
              {/*</Td>*/}
              {/*<Td>*/}
                {/* <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag> */}
              {/*</Td>*/}
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

const truncateByWords = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;

  const words = text.substring(0, maxLength).split(' ');
  words.pop();
  return words.join(' ') + '...';
};

export default NewProductInnovation;
