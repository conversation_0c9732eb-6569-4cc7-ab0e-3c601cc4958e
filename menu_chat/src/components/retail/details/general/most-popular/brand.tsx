import BarChart, { BarChartItem } from "@/components/charts/barChart";
import Paragraph from "@/components/base/paragraph";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getRetailProductTopBrands } from "@/api/retailer_products";
import { useRetailProduct } from "@/contexts/RetailProductContext";

const Brand = () => {
  const { data: session } = useSession();
  const { id } = useRetailProduct();

  const { data: brands, isLoading, error } = useQuery<TopBrandChartData, Error>({
    queryKey: ["retail_product_top_brands", id],
    queryFn: () => getRetailProductTopBrands({ auth: session?.user.authorization as string, id: id }),
    enabled: !!session?.user?.authorization,
  });

  const chartData: BarChartItem[] = brands ? brands.data : [];

  if (isLoading) return <div>Loading...</div>;
  if (error || !brands) return <div>Error loading top brands</div>;

  return (
    <>
      <div className="font-archivo bg-muted-500/10 mb-1 flex items-center justify-between rounded-sm px-2 py-1 text-neutral-500 uppercase">
        <Paragraph className="text-[11px] font-medium">Name</Paragraph>
        <Paragraph className="text-[11px] font-medium">{"SKU's"}</Paragraph>
      </div>
      <div className="border-muted-100 rounded-lg border p-4 pt-0">
        <BarChart dataItems={chartData} height={320} symbol="" maxValue={brands.maxValue} withSecondaryValue={true} />
      </div>
    </>
  );
};

export default Brand;
