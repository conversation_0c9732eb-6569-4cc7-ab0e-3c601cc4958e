import BarChart, { BarChartItem } from "@/components/charts/barChart";
import Paragraph from "@/components/base/paragraph";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getRetailProductTopTextures } from "@/api/retailer_products";
import { useRetailProduct } from "@/contexts/RetailProductContext";

const Textures = () => {
  const { data: session } = useSession();
  const { id } = useRetailProduct();

  const { data: textures, isLoading, error } = useQuery<RetailProductChartData, Error>({
    queryKey: ["retail_product_top_textures", id],
    queryFn: () => getRetailProductTopTextures({ auth: session?.user.authorization as string, id: id }),
    enabled: !!session?.user?.authorization,
  });

  const chartData: BarChartItem[] = textures ? textures.data : [];

  if (isLoading) return <div>Loading...</div>;
  if (error || !textures) return <div>Error loading top textures</div>;

  return (
    <>
      <div className="font-archivo bg-muted-500/10 mb-1 flex items-center justify-between rounded-sm px-2 py-1 text-neutral-500 uppercase">
        <Paragraph className="text-[11px] font-medium">Name</Paragraph>
        <Paragraph className="text-[11px] font-medium">Percent</Paragraph>
      </div>
      <div className="border-muted-100 rounded-lg border p-4 pt-0">
        <BarChart
          dataItems={chartData}
          height={320}
          symbol="%"
          maxValue={textures.maxValue}
        />
      </div>
    </>
  );
};

export default Textures;