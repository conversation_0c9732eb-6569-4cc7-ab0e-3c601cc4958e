import { useState } from "react";
import Card from "@/components/base/card";
import Button from "@/components/base/button";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import BarChart, { BarChartItem } from "@/components/charts/barChart";
import Tabs, { TabContent } from "@/components/base/tabs";
import { CHART_DROPDOWN } from "@/data/ingredients";

const ConsumedBy = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  // TODO: get actual data from API
  const chartData1: BarChartItem[] = [
    {
      id: 1,
      label: "Gen Z",
      color: "#D2D0BC",
      value: 25,
      symbol: "%",
    },
    {
      id: 2,
      label: "Millenials",
      color: "#A2D161",
      value: 65,
      symbol: "%",
    },
    {
      id: 3,
      label: "Gen X",
      color: "#D2D0BC",
      value: 40,
      symbol: "%",
    },
  ];

  const chartData2: BarChartItem[] = [
    {
      id: 1,
      label: "Gen Z",
      color: "#D2D0BC",
      value: 45,
      symbol: "%",
    },
    {
      id: 2,
      label: "Millenials",
      color: "#A2D161",
      value: 35,
      symbol: "%",
    },
    {
      id: 3,
      label: "Gen X",
      color: "#D2D0BC",
      value: 65,
      symbol: "%",
    },
  ];

  return (
    <Card className="p-4">
      <div className="relative flex flex-wrap items-center justify-between gap-2">
        <Tabs navigation={["Consumed by Generation", "Consumed by Ethnicity"]}>
          <TabContent>
            <div className="border-muted-100 rounded-lg border px-4 py-2">
              <BarChart
                dataItems={chartData1}
                symbol="%"
                maxValue={100}
                height={320}
              />
            </div>
          </TabContent>
          <TabContent>
            <div className="border-muted-100 rounded-lg border px-4 py-2">
              <BarChart
                dataItems={chartData2}
                symbol="%"
                maxValue={100}
                height={320}
              />
            </div>
          </TabContent>
        </Tabs>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
          className="absolute top-0 right-0"
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
    </Card>
  );
};

export default ConsumedBy;
