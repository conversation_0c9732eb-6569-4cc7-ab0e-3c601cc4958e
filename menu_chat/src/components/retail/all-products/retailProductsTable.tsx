import { useState, useEffect } from "react";
import Pagination from "@/components/table/pagination";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import Link from "next/link";
import { getRetailProducts } from "@/api/retailer_products";

interface RetailProductsTableProps {
  auth: string;
  search?: string;
  filters?: RetailProductTableFilters;
  sort?: string;
}

const RetailProductsTable = ({
  auth,
  search = "",
  filters = { l0: [], l1: [], l2: [] },
  sort = "",
}: RetailProductsTableProps) => {
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [products, setProducts] = useState<RetailProduct[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let cancelled = false;

    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await getRetailProducts({
          auth,
          page,
          perPage,
          sort,
          search,
          filters,
        });
        if (cancelled) return;
        setProducts(data.data || []);
        setTotalResults(data.total_count || 0);
      } catch (error) {
        if (cancelled) return;
        console.error("Failed to fetch products:", error);
        setProducts([]);
        setTotalResults(0);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };

    fetchData();
    return () => { cancelled = true; };
  }, [auth, page, perPage, sort, search, filters]);

  return (
    <div className="py-3">
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>Name</Th>
              <Th>Main Category</Th>
              <Th>Category</Th>
              <Th>Sub Category</Th>
              <Th>Product Count</Th>
            </Tr>
          </Thead>
          <Tbody>
            {loading ? (
              <Tr>
                <td colSpan={5}>Loading...</td>
              </Tr>
            ) : products.length > 0 ? (
              products.map((product, index) => (
                <Tr key={index}>
                  <Td>
                    <Link href={`/retail/details/${product.id}`} passHref>
                      <Tag className="cursor-pointer hover:underline">
                        {product.gri}
                      </Tag>
                    </Link>
                  </Td>
                  <Td>
                    <Tag variant="white">{product.main_category}</Tag>
                  </Td>
                  <Td>
                    <Tag variant="white">{product.category}</Tag>
                  </Td>
                  <Td>
                    <Tag variant="white">{product.sub_category}</Tag>
                  </Td>
                  <Td>
                    <Tag variant="white">
                      {product.products_count?.toString() || "0"}
                    </Tag>
                  </Td>
                </Tr>
              ))
            ) : (
              <Tr>
                <td colSpan={5}>No products found</td>
              </Tr>
            )}
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default RetailProductsTable;
