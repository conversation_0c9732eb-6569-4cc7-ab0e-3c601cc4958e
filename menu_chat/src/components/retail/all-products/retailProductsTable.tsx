import { useState, useEffect, useMemo,  } from "react";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import {
  ColumnDef,
  ColumnResizeMode,
  getCoreRowModel,
  getPaginationRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { Table as TanStackTable } from "@tanstack/react-table";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import { getRetailProducts } from "@/api/retailer_products";

interface RetailProductsTableProps {
  auth: string;
  search?: string;
  filters?: RetailProductTableFilters;
  sort?: string;
  onSort?: (value: string) => void;
  onTableReady?: (table: TanStackTable<RetailProduct>) => void;
}

const RetailProductsTable = ({
  auth,
  search = "",
  filters = { l0: [], l1: [] },
  sort = "",
  onSort,
  onTableReady,
}: RetailProductsTableProps) => {

  const [page, setPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(10);

  const [pagination, setPagination] = useState({
    pageIndex: page - 1,
    pageSize: perPage,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnSizing, setColumnSizing] = useState({});
  const [columnResizeMode] = useState<ColumnResizeMode>("onChange");

  useEffect(() => {
    if (!sort) {
      setSorting([]);
      return;
    }
    const [id, dir] = sort.split(":");
    if (id && dir) {
      setSorting([{ id, desc: dir === "desc" }]);
    }
  }, [sort]);

  const { data, isLoading } = useQuery({
    queryKey: [
      "retail_products",
      auth,
      page,
      perPage,
      sort,
      search,
      filters,
    ],
    queryFn: () =>
      getRetailProducts({
        auth,
        page,
        perPage,
        sort,
        search,
        filters,
      }),
    enabled: !!auth,
  });

  const products = useMemo(() => data?.data ?? [], [data]);
  const totalResults = data?.total_count ?? 0;

  const columns = useMemo<({ accessorKey: keyof RetailProduct | string } & ColumnDef<RetailProduct>)[]>(
    () => [
      {
        id: "gri",
        accessorKey: "gri",
        header: "Name",
        cell: (info) => {
          const row = info.row.original;
          return (
            <Link href={`/retail/details/${row.id}`}>
              <Tag className="cursor-pointer hover:underline">{row.gri}</Tag>
            </Link>
          );
        },
      },
      {
        id: "l0",
        accessorKey: "main_category",
        header: "Main Category",
        cell: (info) => <Tag variant="white">{info.getValue<string>()}</Tag>,
      },
      {
        id: "l1",
        accessorKey: "category",
        header: "Category",
        cell: (info) => <Tag variant="white">{info.getValue<string>()}</Tag>,
      },
      
      {
        id: "products_count",
        accessorKey: "products_count",
        header: "Product Count",
        cell: (info) => (
          <Tag variant="white">{String(info.getValue<number>() ?? 0)}</Tag>
        ),
      },
    ],
    [],
  );

  const emptyState = (
    <div className="py-6 text-center">
      <p className="text-gray-500">No products found</p>
      <p className="text-sm text-gray-400">Try adjusting your search or filters</p>
    </div>
  );

  const table = useReactTable({
    data: products,
    columns,
    state: {
      pagination,
      sorting,
      columnSizing,
    },
    onPaginationChange: (updater) => {
      const next =
        typeof updater === "function" ? updater(pagination) : updater;
      setPagination(next);
      setPage(next.pageIndex + 1);
      setPerPage(next.pageSize);
    },
    onSortingChange: (updater) => {
      const next = typeof updater === "function" ? updater(sorting) : updater;
      setSorting(next);
      if (onSort) {
        const first = next[0];
        if (first) {
          onSort(`${first.id}:${first.desc ? "desc" : "asc"}`);
        } else {
          onSort("");
        }
      }
    },
    manualSorting: true,
    manualPagination: true,
    rowCount: totalResults,
    enableColumnResizing: true,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode,
    defaultColumn: {
      size: 150,
      minSize: 50,
      maxSize: 500,
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  useEffect(() => {
    if (onTableReady) onTableReady(table as unknown as TanStackTable<RetailProduct>);
  }, [table, onTableReady]);

  useEffect(() => {
    setPagination({ pageIndex: page - 1, pageSize: perPage });
  }, [page, perPage]);

  return (
    <div className="py-3">
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <EnhancedTable
          table={table}
          withBorder
          enableResizing
          emptyState={emptyState}
          isLoading={isLoading}
        />
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default RetailProductsTable;
