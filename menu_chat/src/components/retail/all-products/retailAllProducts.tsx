import { useState } from "react";
import { Table as TanStackTable } from "@tanstack/react-table";
import RetailFilters from "../retailFilters";
import RetailProductsTable from "./retailProductsTable";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getRetailProductsTableFilters } from "@/api/retailer_products";

const RetailAllProducts = () => {
  const { data: session } = useSession();
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<RetailProductTableFilters>({ l0: [], l1: [], });
  const [sort, setSort] = useState<string>("");
  const [table, setTable] = useState<TanStackTable<RetailProduct> | undefined>(undefined);

  const handleFilterChange = (newFilters: RetailProductTableFilters) => {
    setFilters(() => newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const auth = session?.user?.authorization as string;

  const { data: filterOptions, isLoading, error } = useQuery({
    queryKey: ["retail_products_table_filters", auth],
    queryFn: () => getRetailProductsTableFilters({ auth }),
    enabled: !!auth,
    staleTime: 24 * 60 * 60 * 1000
  });

  if (isLoading) return <div>Loading...</div>;
  if (error instanceof Error) return <div>Error: {error.message}</div>;
  if (!filterOptions) return <div>No data</div>;

  return (
    <section className="mt-[60px] py-4">
      <RetailFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
        table={table}
        tableName="Retail Products"
        filterOptions={filterOptions}
      />
      <RetailProductsTable
        auth={auth}
        search={search}
        filters={filters}
        sort={sort}
        onSort={handleSortChange}
        onTableReady={setTable}
      />
    </section>
  );
};

export default RetailAllProducts;
