import Paragraph from "@/components/base/paragraph";
import { SafeImage } from "@/components/utils/helpers";
import Link from "next/link";

const sources = [
  {
    id: 1,
    name: "Holy Greens",
    domain: "websiteaddress.com",
    image: "/assets/images/<EMAIL>",
    url: "websiteaddress.com/en.store/red-p...",
    title: "The Best Portuguese Wines – Holy Greens",
    description:
      "Shop the best Portuguese wines at Granvine! A hand-picked collection that represents Portugal's finest vineyards and traditions.",
  },
  {
    id: 2,
    name: "The Wine Society",
    domain: "websiteaddress.com",
    image: "/assets/images/<EMAIL>",
    url: "websiteaddress.com/en.store/red-p...",
    title: "The Best Portuguese Wines – Holy Greens",
    description:
      "Shop the best Portuguese wines at Granvine! A hand-picked collection that represents Portugal's finest vineyards and traditions.",
  },
  {
    id: 3,
    name: "Holy Greens",
    domain: "websiteaddress.com",
    image: "/assets/images/<EMAIL>",
    url: "websiteaddress.com/en.store/red-p...",
    title: "The Best Portuguese Wines – Holy Greens",
    description:
      "Shop the best Portuguese wines at Granvine! A hand-picked collection that represents Portugal's finest vineyards and traditions.",
  },
];

const AnswersSources = () => {
  return (
    <div className="flex flex-col gap-2">
      <ol className="flex list-inside flex-col gap-8">
        {sources.map((source, index) => (
          <li key={source.id}>
            <div className="mb-2.5 flex items-center gap-2">
              <span className="text-sm font-medium text-neutral-600">
                {index + 1}.
              </span>
              <SafeImage
                src={source.image}
                width={32}
                height={32}
                alt="logo"
                className="h-8 w-8 rounded-full object-cover object-center"
              />
              <div>
                <Paragraph size="xs" className="font-medium">
                  {source.domain}
                </Paragraph>
                <Link
                  href={source.url}
                  className="text-xs font-medium text-neutral-600 hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {source.url}
                </Link>
              </div>
            </div>
            <Paragraph size="sm" className="mb-0.5 font-semibold">
              {source.title}
            </Paragraph>
            <Paragraph size="sm" className="font-medium text-neutral-600">
              {source.description}
            </Paragraph>
          </li>
        ))}
      </ol>
    </div>
  );
};

export default AnswersSources;
