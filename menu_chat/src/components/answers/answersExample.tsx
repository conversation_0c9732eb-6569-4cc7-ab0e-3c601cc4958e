import { useState } from "react";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import {
  IconLocation,
  IconMenuAdoption,
  IconCircleUpright,
  IconCalendar,
  IconIncrease,
  IconDecrease,
  IconGraph,
  IconChefHat,
} from "@/components/icons";
import Card, { CardHeader } from "@/components/base/card";
import Tag from "@/components/base/tag";
import Image from "next/image";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Info from "@/components/base/info";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import SentimentBar from "@/components/charts/sentimentBar";
import DonutChart from "@/components/charts/donutChart";
import BarChart from "@/components/charts/barChart";
import LineChart from "@/components/charts/lineChart";

const AnswersExample = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <>
      <Heading level={3}>Food Statistics in New York.</Heading>
      <Paragraph>
        USA known for its culinary diversity, offers a wide range of dining
        options, from traditional Portuguese cuisine to international flavors.
        Below are some key statistics and insights about restaurants in New
        York:
      </Paragraph>
      <Button variant="secondary" size="xs">
        <IconLocation size={16} className="text-neutral-600" />
        Find on Google Maps
        <IconCircleUpright size={16} className="text-yellow-800" />
      </Button>
      <div className="grid grid-cols-3 gap-3">
        <Card className="p-3">
          <Image
            src="/assets/images/<EMAIL>"
            alt="chocolates"
            width={100}
            height={140}
            className="h-[140px] w-full rounded-sm object-cover"
          />
          <div className="border-muted-200 mt-0.5 rounded-sm border p-2">
            <div className="mb-2 flex items-center justify-between gap-2">
              <Tag>Crispy Bok Choy</Tag>
              <Paragraph size="lg" className="font-semibold">
                $12
              </Paragraph>
            </div>
            <Paragraph size="xs" className="font-medium text-neutral-600">
              Fried bok choy served with potatoes.
            </Paragraph>
          </div>
        </Card>
        <Card className="p-3">
          <Image
            src="/assets/images/<EMAIL>"
            alt="chocolates"
            width={100}
            height={140}
            className="h-[140px] w-full rounded-sm object-cover"
          />
          <div className="border-muted-200 mt-0.5 rounded-sm border p-2">
            <div className="mb-2 flex items-center justify-between gap-2">
              <Tag>Apple Pie</Tag>
              <Paragraph size="lg" className="font-semibold">
                $18
              </Paragraph>
            </div>
            <Paragraph size="xs" className="font-medium text-neutral-600">
              Apple pie dessert with vanilla ice cream.
            </Paragraph>
          </div>
        </Card>
        <Card className="p-3">
          <Image
            src="/assets/images/<EMAIL>"
            alt="chocolates"
            width={100}
            height={140}
            className="h-[140px] w-full rounded-sm object-cover"
          />
          <div className="border-muted-200 mt-0.5 rounded-sm border p-2">
            <div className="mb-2 flex items-center justify-between gap-2">
              <Tag>Wagyu steak</Tag>
              <Paragraph size="lg" className="font-semibold">
                $12
              </Paragraph>
            </div>
            <Paragraph size="xs" className="font-medium text-neutral-600">
              Prepared on BBQ and served with potato.
            </Paragraph>
          </div>
        </Card>
      </div>
      <Button variant="secondary" size="xs">
        <IconMenuAdoption size={16} className="text-neutral-600" />
        See full restaurant menu
        <IconCircleUpright size={16} className="text-yellow-800" />
      </Button>
      <Heading level={3}>Oat milk food service trends</Heading>
      <Paragraph>
        Oat milk is growing in food service trends from perspective of menu
        penetration and Google trends. Its prediction for the future period is
        also positive. There is quite a lot social conversation on the topic and
        social sentiment and consumer experience with oat milk is very positive.
      </Paragraph>
      <div>
        <Heading level={4} className="mb-2.5">
          General metrics:
        </Heading>
        <ul className="list-disc pl-5 text-sm">
          <li>
            Change compared to previous period –{" "}
            <span className="font-semibold">12,23% increase</span>
          </li>
          <li>
            Prediction for upcoming period –{" "}
            <span className="font-semibold">12,23% increase</span>
          </li>
          <li>
            Menu penetration –{" "}
            <span className="font-semibold">1,18%, (3,3% increase)</span>
          </li>
          <li>
            Google trends <span className="font-semibold">51</span>
          </li>
        </ul>
      </div>
      <div className="grid grid-cols-2 gap-3">
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <Heading level={4}>Pairing Trends</Heading>
              <Info
                tooltipId="pairing-trends-tooltip"
                content="Lorem ipsum dolor sit amet consectetur adipisicing elit."
              />
            </div>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </CardHeader>
          <Table className="p-4 pb-0">
            <Thead>
              <Tr>
                <Th>Name</Th>
                <Th>Pairing %</Th>
                <Th>Change</Th>
              </Tr>
            </Thead>
            <Tbody>
              <Tr>
                <Td>
                  <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                    <Tag>Bok Choy</Tag>
                    <Tag>Cranberries</Tag>
                  </div>
                </Td>
                <Td>5,44%</Td>
                <Td>
                  <Tag variant="greenOutline">
                    <IconIncrease size={16} />
                    5,70%
                  </Tag>
                </Td>
              </Tr>
              <Tr>
                <Td>
                  <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                    <Tag>Bok Choy</Tag>
                    <Tag>Pistachio</Tag>
                  </div>
                </Td>
                <Td>5,44%</Td>
                <Td>
                  <Tag variant="blueOutline">
                    <IconGraph size={16} />
                    3,25%
                  </Tag>
                </Td>
              </Tr>
              <Tr>
                <Td>
                  <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                    <Tag>Bok Choy</Tag>
                    <Tag>Grape</Tag>
                  </div>
                </Td>
                <Td>5,44%</Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    3,25%
                  </Tag>
                </Td>
              </Tr>
            </Tbody>
          </Table>
        </Card>
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <Heading level={4}>Menu Type</Heading>
              <Info
                tooltipId="menu-type-tooltip"
                content="Lorem ipsum dolor sit amet consectetur adipisicing elit."
              />
            </div>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </CardHeader>
          <Table className="p-4 pb-0">
            <Thead>
              <Tr>
                <Th>Name</Th>
                <Th>Pairing %</Th>
                <Th>Change</Th>
              </Tr>
            </Thead>
            <Tbody>
              <Tr>
                <Td>
                  <Tag variant="white">
                    <IconChefHat size={16} className="text-neutral-500" />
                    Bok Choy
                  </Tag>
                </Td>
                <Td>5,44%</Td>
                <Td>
                  <Tag variant="greenOutline">
                    <IconIncrease size={16} />
                    5,70%
                  </Tag>
                </Td>
              </Tr>
              <Tr>
                <Td>
                  <Tag variant="white">
                    <IconChefHat size={16} className="text-neutral-500" />
                    Cranberries
                  </Tag>
                </Td>
                <Td>5,44%</Td>
                <Td>
                  <Tag variant="blueOutline">
                    <IconGraph size={16} />
                    3,25%
                  </Tag>
                </Td>
              </Tr>
              <Tr>
                <Td>
                  <Tag variant="white">
                    <IconChefHat size={16} className="text-neutral-500" />
                    Grape
                  </Tag>
                </Td>
                <Td>5,44%</Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    3,25%
                  </Tag>
                </Td>
              </Tr>
            </Tbody>
          </Table>
        </Card>
      </div>
      <Button variant="secondary" size="xs">
        <IconCalendar size={16} className="text-neutral-600" />
        See all general insights
        <IconCircleUpright size={16} className="text-yellow-800" />
      </Button>
      <Heading level={4}>Consumer insights:</Heading>
      <div className="grid grid-cols-2 gap-3">
        <Card className="flex flex-col justify-between p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <Heading level={5}>Social Conversations</Heading>
          </div>
          <div className="flex items-end justify-between">
            <Heading level={2} className="flex items-end gap-1.5">
              <span className="text-3xl">+</span>3,6K
              <span className="text-xs text-neutral-600">
                search of term popularity
              </span>
            </Heading>
            <Tag variant="greenOutline" className="px-1">
              <IconIncrease size={16} /> 17,3 %
            </Tag>
          </div>
        </Card>
        <Card className="p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <div className="flex items-center">
              <Heading level={5}>Social Sentiments</Heading>
            </div>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </div>
          <SentimentBar
            height={36}
            showLabel={true}
            segments={{
              "strongly-dislike": 2.33,
              "somewhat-dislike": 6.45,
              neutral: 23.5,
              "somewhat-like": 20.5,
              "strongly-like": 2,
            }}
          />
          <div className="mt-1 flex items-center justify-between">
            <Paragraph size="xs" className="font-medium text-neutral-600">
              Negative
            </Paragraph>
            <Paragraph size="xs" className="font-medium text-neutral-600">
              in %
            </Paragraph>
            <Paragraph size="xs" className="font-medium text-neutral-600">
              Positive
            </Paragraph>
          </div>
        </Card>
        <Card className="flex flex-col justify-between p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <Heading level={5}>Most Consumed by Gen.</Heading>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </div>
          <div className="flex items-end justify-between">
            <Heading level={2} className="flex items-end gap-1.5">
              <span className="text-3xl">+</span>72,31%
              <span className="text-xs text-neutral-600">Gen Z</span>
            </Heading>
          </div>
        </Card>
        <Card className="flex flex-col justify-between p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <Heading level={5}>Most Consumed by Ethnicity</Heading>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </div>
          <div className="flex items-end justify-between">
            <Heading level={2} className="flex items-end gap-1.5">
              <span className="text-3xl">+</span>63,26%
              <span className="text-xs text-neutral-600">Asian</span>
            </Heading>
          </div>
        </Card>
        <Card className="col-span-2 p-1">
          <div className="mb-4 flex flex-wrap items-center justify-between gap-2 px-4 pt-3">
            <div className="flex items-center">
              <Heading level={5}>Consumer Experience</Heading>
              <Info
                tooltipId="geographic-popularity"
                content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Adipisci rem sit molestias officia mollitia"
              />
            </div>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </div>
          <div className="border-muted-100 grid grid-cols-3 gap-4 border-t p-4">
            <div className="border-muted-100 flex flex-col items-center rounded-lg border p-4">
              <DonutChart data={[{ value: 49, color: "#648E29" }]} size={100} />
              <div className="w-full flex-1">
                <div className="flex items-center justify-between">
                  <Heading level={3}>2824</Heading>
                  <div className="text-green-1000 inline-flex items-center gap-1 rounded-sm bg-green-100 px-1 py-0.5 text-xs font-medium tracking-[-0.3px]">
                    <span className="block size-3 rounded-xs bg-green-700"></span>
                    Strongly like
                  </div>
                </div>
                <Paragraph className="text-xs font-medium text-neutral-600">
                  /3024
                </Paragraph>
                <Paragraph className="text-xs font-medium text-neutral-600">
                  rand among ingredients{" "}
                </Paragraph>
              </div>
            </div>
            <div className="border-muted-100 flex flex-col items-center rounded-lg border p-4">
              <DonutChart
                data={[{ value: 55, color: "#ffc857" }]}
                size={100}
                pattern
              />
              <div className="w-full flex-1">
                <div className="flex items-center justify-between">
                  <Heading level={3}>2824</Heading>
                  <div className="text-yellow-1000 inline-flex items-center gap-1 rounded-sm bg-yellow-100 px-1 py-0.5 text-xs font-medium tracking-[-0.3px]">
                    <span className="block size-3 rounded-xs bg-yellow-500"></span>
                    Tried once or twice
                  </div>
                </div>
                <Paragraph className="text-xs font-medium text-neutral-600">
                  /3024
                </Paragraph>
                <Paragraph className="text-xs font-medium text-neutral-600">
                  rand among ingredients{" "}
                </Paragraph>
              </div>
            </div>
            <div className="border-muted-100 flex flex-col items-center rounded-lg border p-4">
              <DonutChart
                data={[{ value: 49, color: "#8bc539" }]}
                size={100}
                pattern
              />
              <div className="w-full flex-1">
                <div className="flex items-center justify-between">
                  <Heading level={3}>2824</Heading>
                  <div className="text-green-1000 inline-flex items-center gap-1 rounded-sm bg-green-100 px-1 py-0.5 text-xs font-medium tracking-[-0.3px]">
                    <span className="block size-3 rounded-xs bg-green-500"></span>
                    Strongly like
                  </div>
                </div>
                <Paragraph className="text-xs font-medium text-neutral-600">
                  /3024
                </Paragraph>
                <Paragraph className="text-xs font-medium text-neutral-600">
                  rand among ingredients{" "}
                </Paragraph>
              </div>
            </div>
          </div>
        </Card>
        <Card className="col-span-2 flex flex-col justify-between p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <Heading level={5}>Most Common Income Level</Heading>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </div>
          <div className="flex items-end justify-between">
            <Heading level={2} className="flex items-end gap-1.5">
              40k — $80k
              <span className="text-xs text-neutral-600">Gen Z</span>
            </Heading>
          </div>
        </Card>
        <Card className="col-span-2">
          <CardHeader>
            <div className="flex items-center">
              <Heading level={4}>Consumation by Gender</Heading>
              <Info
                tooltipId="consumation-gender-tooltip"
                content="Lorem ipsum dolor, sit amet consectetur adipisicing elit. Adipisci rem sit molestias officia mollitia"
              />
            </div>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </CardHeader>
          <div className="p-4">
            <div className="font-archivo font-semibol bg-muted-500/10 mb-4 flex items-center justify-between rounded-md px-2 py-1 text-[11px] text-neutral-600 uppercase">
              <span>Name</span>
              <span># of Products</span>
            </div>
            <div className="border-muted-100 rounded-lg border">
              <BarChart
                dataItems={[
                  {
                    id: 1,
                    label: "Walmart",
                    color: "#D2D0BC",
                    value: 1045,
                  },
                  {
                    id: 2,
                    label: "Kroger",
                    color: "#A2D161",
                    value: 8306,
                  },
                ]}
                symbol="k"
                maxValue={10000}
                height={150}
              />
            </div>
          </div>
        </Card>
        <Card className="col-span-2">
          <CardHeader>
            <div className="flex items-center">
              <Heading level={4}>Highest Growing Product Category</Heading>
              <Info
                tooltipId="menu-types-tooltip"
                content="Lorem ipsum dolor, sit amet consectetur adipisicing elit. Adipisci rem sit molestias officia mollitia"
              />
            </div>
            <Dropdown
              options={MENU_ADOPTION_CURVE_DROPDOWN}
              selectedValue={selected}
              onSelect={(value) => handleSelect(value)}
            >
              <Button variant="ghost" size="xs" className="w-6">
                <IconMore size={16} />
              </Button>
            </Dropdown>
          </CardHeader>
          <div className="px-4 py-3">
            <div className="border-muted-100 rounded-lg border p-4">
              <LineChart
                dataItems={[
                  {
                    id: 1,
                    label: "Food",
                    color: "#FFBE05",
                    data: [
                      0.4, 0.4, 0.3, 2.8, 6.5, 10.2, 9.8, 10.0, 9.8, 10.2, 11.5,
                    ],
                  },
                  {
                    id: 2,
                    label: "Natural & Organic",
                    color: "#FF6D56",
                    data: [
                      0.4, 0.4, 0.3, 2.5, 4.2, 7.5, 7.2, 7.0, 7.2, 7.8, 8.4,
                    ],
                  },
                  {
                    id: 3,
                    label: "Snacks, Chips, Salsas & Dips",
                    color: "#8BC539",
                    data: [
                      0.0, 0.1, 0.3, 1.0, 2.2, 3.5, 4.5, 5.5, 6.3, 7.0, 7.8,
                    ],
                  },
                  {
                    id: 4,
                    label: "Pantry Essentials",
                    color: "#D273D8",
                    data: [
                      0.1, 0.3, 0.4, 2.5, 3.7, 4.0, 4.3, 5.5, 6.5, 7.2, 8.1,
                    ],
                  },
                ]}
                labels={[
                  "Feb 5",
                  "Feb 12",
                  "Feb 19",
                  "Mar 5",
                  "Mar 12",
                  "Mar 19",
                  "Mar 26",
                  "Apr 2",
                  "Apr 9",
                  "Apr 16",
                  "Apr 23",
                ]}
                symbol="$"
                height={300}
              />
            </div>
          </div>
        </Card>
      </div>
      <Button variant="secondary" size="xs">
        <IconCalendar size={16} className="text-neutral-600" />
        See all consumer insights
        <IconCircleUpright size={16} className="text-yellow-800" />
      </Button>
    </>
  );
};

export default AnswersExample;
