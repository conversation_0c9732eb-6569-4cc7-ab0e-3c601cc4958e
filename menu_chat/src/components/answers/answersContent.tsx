import React, { useEffect, useState } from "react";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import {
  IconLocation,
  IconMenuAdoption,
  IconCircleUpright,
} from "@/components/icons";
import Card from "@/components/base/card";
import Tag from "@/components/base/tag";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { fetchSduiData } from "@/data/sduiData";
import { fetchMarkdownData } from "@/data/markdownData";
import StreamBlockRenderer from "./StreamBlockRenderer";
import MarkdownRenderer from "./MarkdownRenderer";
import { useRouter } from "next/router";

const StaticContent = () => (
  <>
    {/* Sample Answer Content */}
    <Heading level={3}>Food Statistics in New York.</Heading>
    <Paragraph>
      USA known for its culinary diversity, offers a wide range of dining
      options, from traditional Portuguese cuisine to international flavors.
      Below are some key statistics and insights about restaurants in New
      York:
    </Paragraph>
    <Button variant="secondary" size="xs">
      <IconLocation size={16} className="text-neutral-600" />
      Find on Google Maps
      <IconCircleUpright size={16} className="text-yellow-800" />
    </Button>
    <div className="grid grid-cols-3 gap-3">
      <Card className="p-3">
        <Image
          src="/assets/images/<EMAIL>"
          alt="chocolates"
          width={100}
          height={140}
          className="h-[140px] w-full rounded-sm object-cover"
        />
        <div className="border-muted-200 mt-0.5 rounded-sm border p-2">
          <div className="mb-2 flex items-center justify-between gap-2">
            <Tag>Crispy Bok Choy</Tag>
            <Paragraph size="lg" className="font-semibold">
              $12
            </Paragraph>
          </div>
          <Paragraph size="xs" className="font-medium text-neutral-600">
            Fried bok choy served with potatoes.
          </Paragraph>
        </div>
      </Card>
      <Card className="p-3">
        <Image
          src="/assets/images/<EMAIL>"
          alt="chocolates"
          width={100}
          height={140}
          className="h-[140px] w-full rounded-sm object-cover"
        />
        <div className="border-muted-200 mt-0.5 rounded-sm border p-2">
          <div className="mb-2 flex items-center justify-between gap-2">
            <Tag>Apple Pie</Tag>
            <Paragraph size="lg" className="font-semibold">
              $18
            </Paragraph>
          </div>
          <Paragraph size="xs" className="font-medium text-neutral-600">
            Apple pie dessert with vanilla ice cream.
          </Paragraph>
        </div>
      </Card>
      <Card className="p-3">
        <Image
          src="/assets/images/<EMAIL>"
          alt="chocolates"
          width={100}
          height={140}
          className="h-[140px] w-full rounded-sm object-cover"
        />
        <div className="border-muted-200 mt-0.5 rounded-sm border p-2">
          <div className="mb-2 flex items-center justify-between gap-2">
            <Tag>Wagyu steak</Tag>
            <Paragraph size="lg" className="font-semibold">
              $12
            </Paragraph>
          </div>
          <Paragraph size="xs" className="font-medium text-neutral-600">
            Prepared on BBQ and served with potato.
          </Paragraph>
        </div>
      </Card>
    </div>
    <Button variant="secondary" size="xs">
      <IconMenuAdoption size={16} className="text-neutral-600" />
      See full restaurant menu
      <IconCircleUpright size={16} className="text-yellow-800" />
    </Button>
  </>
);

const AnswersContent = () => {
  const router = useRouter();
  const [useSDUI, setUseSDUI] = useState(false);
  const [useMarkdown, setUseMarkdown] = useState(false);
  
  // Check for URL parameters
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      setUseSDUI(urlParams.get('sdui') === 'true');
      setUseMarkdown(urlParams.get('markdown') === 'true');
    }
  }, [router.query]);
  
  // Fetch SDUI data using React Query
  const { 
    data: sduiResponse, 
    isLoading: isSduiLoading, 
    isError: isSduiError 
  } = useQuery({
    queryKey: ['sduiData'],
    queryFn: fetchSduiData,
    // Only fetch if we're using SDUI
    enabled: useSDUI
  });
  
  // Fetch Markdown data using React Query
  const { 
    data: markdownResponse, 
    isLoading: isMarkdownLoading, 
    isError: isMarkdownError 
  } = useQuery({
    queryKey: ['markdownData'],
    queryFn: fetchMarkdownData,
    // Only fetch if we're using Markdown
    enabled: useMarkdown
  });

  // If we're using SDUI and have data, render the StreamBlockRenderer
  if (useSDUI) {
    if (isSduiLoading) {
      return <div>Loading SDUI content...</div>;
    }
    
    if (isSduiError) {
      return <div>Error loading SDUI content. Falling back to static content.</div>;
    }
    
    if (sduiResponse?.data) {
      return <StreamBlockRenderer streamData={sduiResponse.data} />;
    }
  }
  
  // If we're using Markdown and have data, render the MarkdownRenderer
  if (useMarkdown) {
    if (isMarkdownLoading) {
      return <div>Loading Markdown content...</div>;
    }
    
    if (isMarkdownError) {
      return <div>Error loading Markdown content. Falling back to static content.</div>;
    }
    
    if (markdownResponse?.data) {
      return <MarkdownRenderer markdown={markdownResponse.data} />;
    }
  }
  
  // Default: render the static content
  return <StaticContent />;
};

export default AnswersContent;
