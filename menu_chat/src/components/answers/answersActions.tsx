import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import {
  IconShare,
  IconDownload,
  IconSync,
  IconCopy,
  IconThumbsup,
  IconThumbsdown,
} from "@/components/icons";

interface AnswersActionsProps {
  message: string;
}

const AnswersActions = ({
  message = "Let me know if I can help with anything else?",
}: AnswersActionsProps) => {
  return (
    <div className="w-full">
      <Paragraph size="sm" className="mb-5 font-medium">
        {message}
      </Paragraph>
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            size="xs"
            variant="ghost"
            className="group !bg-transparent px-0 text-neutral-600 hover:text-neutral-900"
          >
            Share{" "}
            <IconShare
              size={20}
              className="text-neutral-500 transition-colors group-hover:text-neutral-900"
            />
          </Button>
          <Button
            size="xs"
            variant="ghost"
            className="group !bg-transparent px-0 text-neutral-600 hover:text-neutral-900"
          >
            Export{" "}
            <IconDownload
              size={20}
              className="text-neutral-500 transition-colors group-hover:text-neutral-900"
            />
          </Button>
          <Button
            size="xs"
            variant="ghost"
            className="group !bg-transparent px-0 text-neutral-600 hover:text-neutral-900"
          >
            Regenerate{" "}
            <IconSync
              size={20}
              className="text-neutral-500 transition-colors group-hover:text-neutral-900"
            />
          </Button>
          <Button
            size="xs"
            variant="ghost"
            className="group !bg-transparent px-0 text-neutral-600 hover:text-neutral-900"
          >
            Copy{" "}
            <IconCopy
              size={20}
              className="text-neutral-500 transition-colors group-hover:text-neutral-900"
            />
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="xs"
            variant="ghost"
            className="w-6 !bg-transparent px-0 text-neutral-500 hover:text-neutral-900"
          >
            <span className="sr-only">Thumbsup</span>
            <IconThumbsup size={20} />
          </Button>
          <Button
            size="xs"
            variant="ghost"
            className="w-6 !bg-transparent px-0 text-neutral-500 hover:text-neutral-900"
          >
            <span className="sr-only">Thumbsdown</span>
            <IconThumbsdown size={20} />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AnswersActions;
