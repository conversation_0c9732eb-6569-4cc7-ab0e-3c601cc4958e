import Heading from "@/components/base/heading";
import { IconPage, IconCircleUpright } from "@/components/icons";
import { IngredientQuestion } from "@/data/ingredients";

interface AnswersRelatedQuestionsProps {
  questions: IngredientQuestion[];
  onSelect: (value: string) => void;
}

const AnswersRelatedQuestions = ({
  questions,
  onSelect,
}: AnswersRelatedQuestionsProps) => {
  return (
    <div>
      <div className="mt-12 mb-2.5 flex gap-2">
        <IconPage />
        <Heading level={3}>People also ask</Heading>
      </div>
      {questions.map((item: IngredientQuestion, idx) => (
        <div
          key={idx}
          className="group border-muted-200 hover:border-muted-200 flex w-full cursor-pointer items-center justify-between border-b p-2.5 transition-colors"
          onClick={() => onSelect(item.question)}
        >
          <div className="flex items-center gap-1">
            <span className="text-sm font-medium text-neutral-700 transition-colors group-hover:text-neutral-900">
              {item.question}
            </span>
          </div>
          <IconCircleUpright
            size={20}
            className="text-yellow-800 opacity-0 transition-opacity group-hover:opacity-100"
          />
        </div>
      ))}
    </div>
  );
};
export default AnswersRelatedQuestions;
