import React from 'react';
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Card from "@/components/base/card";
import Tag from "@/components/base/tag";
import { SafeImage } from "@/components/utils/helpers";
import { IconCircleUpright, IconLocation, IconMenuAdoption } from "@/components/icons";

// Define types for our component structure
type Typography = 'TITLE' | 'SUBTITLE' | 'BODY';

interface BaseComponent {
  component_id: string;
  component_type: string;
}

interface TextComponent extends BaseComponent {
  component_type: 'text';
  content: {
    value: string;
    typography: {
      value: Typography;
    };
  };
}

interface LinkComponent extends BaseComponent {
  component_type: 'link';
  content: {
    beforeContentIcon?: string;
    label: string;
    afterContentIcon?: string;
    url?: string;
  };
}

interface CardContent {
  imgURL: string;
  title: string;
  subTitle: string;
  description: string;
}

interface CardComponent extends BaseComponent {
  component_type: 'card';
  content: CardContent;
}

interface HorizontalContainerComponent extends BaseComponent {
  component_type: 'horizontal-container';
  content: {
    componentList: CardComponent[];
  };
}

// Define a more flexible component type that allows string-based component_type
// This helps with compatibility with JSON from the server
interface GenericComponent {
  component_id: string;
  component_type: string;
  content: {
    value?: string;
    typography?: {
      value?: string;
    };
    beforeContentIcon?: string;
    label?: string;
    afterContentIcon?: string;
    url?: string;
    componentList?: Array<{
      component_id: string;
      component_type: string;
      content: CardContent;
    }>;
  };
}

type StreamComponent = 
  | TextComponent 
  | LinkComponent 
  | HorizontalContainerComponent 
  | CardComponent
  | GenericComponent;

// Simplified StreamData structure
interface StreamData {
  components: StreamComponent[];
}

interface StreamBlockRendererProps {
  streamData: StreamData;
  className?: string;
}

// Helper function to render the icon by name
const renderIcon = (iconName: string, size: number = 16, className?: string) => {
  switch (iconName) {
    case 'IconLocation':
      return <IconLocation size={size} className={className || "text-neutral-600"} />;
    case 'IconCircleUpright':
      return <IconCircleUpright size={size} className={className || "text-yellow-800"} />;
    case 'IconMenuAdoption':
      return <IconMenuAdoption size={size} className={className || "text-neutral-600"} />;
    default:
      return null;
  }
};

// Helper function to render text based on typography
const renderText = (component: TextComponent | GenericComponent) => {
  const { value, typography } = component.content;
  
  if (!value) return null;
  
  switch (typography?.value) {
    case 'TITLE':
      return <Heading level={3}>{value}</Heading>;
    case 'SUBTITLE':
      return <Heading level={4}>{value}</Heading>;
    case 'BODY':
      return <Paragraph>{value}</Paragraph>;
    default:
      return <Paragraph>{value}</Paragraph>;
  }
};

// Helper function to render link component
const renderLink = (component: LinkComponent | GenericComponent) => {
  const { beforeContentIcon, label, afterContentIcon, url } = component.content;
  
  if (!label) return null;
  
  // If URL is provided, render as an anchor tag that opens in a new tab
  if (url) {
    return (
      <a 
        href={url} 
        target="_blank" 
        rel="noopener noreferrer" 
        className="inline-block"
      >
        <Button variant="secondary" size="xs">
          {beforeContentIcon && renderIcon(beforeContentIcon)}
          {label}
          {afterContentIcon && renderIcon(afterContentIcon)}
        </Button>
      </a>
    );
  }
  
  // Otherwise render as just a button
  return (
    <Button variant="secondary" size="xs">
      {beforeContentIcon && renderIcon(beforeContentIcon)}
      {label}
      {afterContentIcon && renderIcon(afterContentIcon)}
    </Button>
  );
};

// Helper function to render a card
const renderCard = (card: CardContent) => {
  return (
    <Card className="p-3">
      <SafeImage
        src={card.imgURL || "/assets/images/<EMAIL>"} // Fallback image
        alt={card.title}
        width={100}
        height={140}
        className="h-[140px] w-full rounded-sm object-cover"
      />
      <div className="border-muted-200 mt-0.5 rounded-sm border p-2">
        <div className="mb-2 flex items-center justify-between gap-2">
          <Tag>{card.title}</Tag>
          <Paragraph size="lg" className="font-semibold">
            {card.subTitle}
          </Paragraph>
        </div>
        <Paragraph size="xs" className="font-medium text-neutral-600">
          {card.description}
        </Paragraph>
      </div>
    </Card>
  );
};

// Helper function to render horizontal container with cards
const renderHorizontalContainer = (component: HorizontalContainerComponent | GenericComponent) => {
  const componentList = component.content.componentList || [];
  
  return (
    <div className="grid grid-cols-3 gap-3">
      {componentList.map((card, index) => {
        if (!card.content) return null;
        return (
          <React.Fragment key={card.component_id || index}>
            {renderCard(card.content as CardContent)}
          </React.Fragment>
        );
      })}
    </div>
  );
};

// Main renderer function
const renderComponent = (component: StreamComponent) => {
  switch (component.component_type) {
    case 'text':
      return renderText(component);
    case 'link':
      return renderLink(component);
    case 'horizontal-container':
      return renderHorizontalContainer(component);
    case 'card':
      return renderCard(component.content as CardContent);
    default:
      // Handle unknown component types
      console.warn(`Unknown component type: ${component.component_type}`);
      return null;
  }
};

const StreamBlockRenderer: React.FC<StreamBlockRendererProps> = ({ streamData, className = '' }) => {
  // Return null if no data is available
  if (!streamData || !streamData.components || !Array.isArray(streamData.components)) {
    return null;
  }
  
  const componentsToRender = streamData.components;

  return (
    <div className={`stream-block-renderer ${className}`}>
      <div className="flex flex-col gap-4">
        {componentsToRender.map((component, index) => (
          <div key={component.component_id || index}>
            {renderComponent(component)}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StreamBlockRenderer; 