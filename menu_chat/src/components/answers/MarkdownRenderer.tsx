import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import type { Components } from 'react-markdown';
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import { IconCircleUpright } from "@/components/icons";
import Image from "next/image";
import Card from "@/components/base/card";
import Tag from "@/components/base/tag";
import remarkGfm from 'remark-gfm';

interface MarkdownRendererProps {
  markdown: string;
  className?: string;
  isStreaming?: boolean;
}

interface CardData {
  title: string;
  description: string;
  image: string;
  subTitle: string;
}

// Image mapping for card titles
const TITLE_TO_IMAGE_MAP: Record<string, string> = {
  "yuzu mojito": "https://monin.in/cdn/shop/files/yuzumojito_1000x.jpg?v=1717566146",
  "yuzu & mint punchy": "https://whiteonricecouple.com/recipe/images/yuzu-cocktail-recipe-3.jpg",
  "yuzu miso black cod": "https://www.zupans.com/app/uploads/2023/05/ZUPANS_YUZU_-BLACK_COD_W_horizontal-rotated.jpg",
  "yuzu gin": "https://coupleeatsfood.com/wp-content/uploads/2022/04/yuzu-gin-cocktail-3.jpg",
  "hamachi yuzu soy": "https://www.kinjirushiusa.com/wp-content/uploads/2023/02/Hamachi-carpaccio-Yuzu-Sauce_2-1536x1388.jpg",
  "joto yuzu sake": "https://cdn.shoplightspeed.com/shops/620662/files/24385229/800x1024x1/joto-yuzu-flavored-sake-500-ml.jpg",
  "yuzu & yuzu pie": "https://www.mochimommy.com/wp-content/uploads/2019/11/F54C2C6B-5319-41C5-B020-3267D2856E6D.jpeg",
  "yuzu tuna tataki": "https://www.jamesmartinchef.co.uk/wp-content/uploads/7T4A3608-768x512.jpg",
  "frozen yuzu": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQhsfu4WRJ51eCdNDuV3V7gdm-yC0qPG9NWpw&s",
};

// Default fallback image if title doesn't match any in the map
const DEFAULT_FALLBACK_IMAGE = "https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg";

// Rename the component to avoid conflict with the imported Card
function CardItem({ data }: { data: CardData }) {
  // Check if all card properties are available
  const isCardComplete = data.title && data.image;

  // Validate image URL to ensure it's properly formatted
  const isValidImageUrl = (url: string) => {
    // Check if the URL is non-empty and has more than just the protocol
    return url && url.match(/^(https?:\/\/[^\/]+)/) && url.length > 10;
  };
  
  // Find a matching image from the title map with case-insensitive matching
  const getImageForTitle = (title: string): string => {
    const normalizedTitle = title.toLowerCase().trim();
    
    // Check if the title exists in our mapping (case insensitive)
    for (const [mapTitle, imageUrl] of Object.entries(TITLE_TO_IMAGE_MAP)) {
      if (normalizedTitle === mapTitle) {
        return imageUrl;
      }
    }
    
    // If no exact match found, check for partial matches (if title contains the key)
    for (const [mapTitle, imageUrl] of Object.entries(TITLE_TO_IMAGE_MAP)) {
      if (normalizedTitle.includes(mapTitle) || mapTitle.includes(normalizedTitle)) {
        return imageUrl;
      }
    }
    
    // Return the provided image if valid, otherwise use default fallback
    return isValidImageUrl(data.image) ? data.image : DEFAULT_FALLBACK_IMAGE;
  };
  
  // Determine image URL, first checking for a match in our map
  const imageUrl = getImageForTitle(data.title);
  
  // Format subtitle (assuming it's a numeric amount)
  const formattedSubtitle = (() => {
    // First, try to parse it as a number
    const num = parseFloat(data.subTitle);
    
    if (!isNaN(num)) {
      // For any numeric value, convert to integer by truncating decimal part
      return Math.floor(num).toString();
    }
    
    // For non-numeric values or if parsing fails
    // Check if it contains a decimal part we can remove
    if (typeof data.subTitle === 'string' && data.subTitle.includes('.')) {
      const parts = data.subTitle.split('.');
      return parts[0]; // Return just the integer part
    }
    
    // Return original if not a number and doesn't contain a decimal
    return data.subTitle;
  })();
  
  // Show skeleton loader if card is incomplete
  if (!isCardComplete) {
    return <CardSkeleton />;
  }

  return (
    <Card className="p-3">
      <Image
        src={imageUrl}
        alt={data.title}
        width={100}
        height={140}
        className="h-[140px] w-full rounded-sm object-cover"
      />
      <div className="border-muted-200 mt-0.5 rounded-sm border p-2 h-[86px] flex flex-col">
        <div className="flex items-center justify-between gap-2 mb-1">
          <div className="relative group">
            <Tag className="max-w-[120px] overflow-hidden text-ellipsis whitespace-nowrap inline-flex items-center">
              <span className="block overflow-hidden text-ellipsis whitespace-nowrap">
                {data.title}
              </span>
            </Tag>
            {/* Tooltip for full title */}
            <div className="absolute left-0 bottom-full mb-1 px-2 py-1 bg-gray-800/80 text-white text-xs rounded shadow-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity max-w-[150px] whitespace-normal break-words z-10">
              {data.title}
            </div>
          </div>
          <Paragraph size="lg" className="font-semibold">
            {formattedSubtitle}
          </Paragraph>
        </div>
        <div className="relative flex-1 overflow-hidden">
          <Paragraph 
            size="xs" 
            className="font-medium text-neutral-600"
          >
            {data.description}
          </Paragraph>
          {/* Add fade out effect at the bottom */}
          <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white to-transparent"></div>
        </div>
      </div>
    </Card>
  );
}

// Skeleton loader for cards that are still being streamed
function CardSkeleton() {
  return (
    <Card className="p-3 relative overflow-hidden">
      {/* Shimmer overlay with inline animation */}
      <div className="absolute inset-0 z-10">
        <div 
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent" 
          style={{
            animation: 'shimmer 1.5s infinite',
            transform: 'translateX(-100%)',
          }}
        />
      </div>
      
      <div className="bg-gray-200 h-[140px] w-full rounded-sm" />
      <div className="border-muted-200 mt-0.5 rounded-sm border p-2">
        <div className="mb-2 flex items-center justify-between gap-2">
          <div className="bg-gray-200 h-6 w-16 rounded" />
          <div className="bg-gray-200 h-6 w-10 rounded" />
        </div>
        <div className="bg-gray-200 h-4 w-full rounded mb-1" />
        <div className="bg-gray-200 h-4 w-3/4 rounded" />
      </div>
      
      {/* Add inline styles for the animation */}
      <style jsx>{`
        @keyframes shimmer {
          100% {
            transform: translateX(100%);
          }
        }
      `}</style>
    </Card>
  );
}

// Detect if a card block is complete (contains all required fields)
const isCardBlockComplete = (text: string): boolean => {
  if (!text.includes('card:')) return false;
  
  const cardEntries = text.split(/card:/g).filter(s => s.trim() !== '');
  
  // Check if all card entries have at least 3 pipe separators (4 fields)
  return cardEntries.every(entry => {
    const pipeCount = (entry.match(/\|/g) || []).length;
    return pipeCount === 4; // Need at least 3 pipes for 4 fields
  });
};

// Display a loading skeleton for table rows
function TableSkeleton({ columns = 3, rows = 3 }) {
  return (
    <div className="my-4 overflow-x-auto border border-gray-200 rounded">
      <table className="min-w-full bg-white">
        <thead>
          <tr className="bg-gray-50">
            {Array.from({ length: columns }).map((_, i) => (
              <th key={i} className="p-3">
                <div className="h-5 bg-gray-200 rounded w-24 animate-pulse"></div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <tr key={rowIndex} className="border-t border-gray-200">
              {Array.from({ length: columns }).map((_, colIndex) => (
                <td key={colIndex} className="p-3">
                  <div className="h-4 bg-gray-100 rounded w-full animate-pulse"></div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// Custom components for rendering markdown elements with streaming support
const createComponents = (isStreaming: boolean): Components => ({
  // Headers
  h1: ({...props }) => (
    <Heading level={1}>{props.children}</Heading>
  ),
  h2: ({ ...props }) => (
    <Heading level={2}>{props.children}</Heading>
  ),
  h3: ({ ...props }) => (
    <Heading level={3}>{props.children}</Heading>
  ),
  h4: ({ ...props }) => (
    <Heading level={4}>{props.children}</Heading>
  ),
  
  // Paragraphs
  p: ({ ...props }) => (
    <Paragraph>{props.children}</Paragraph>
  ),
  
  // Links
  a: ({ href, ...props }) => (
    <a 
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="inline-block"
    >
      <Button variant="secondary" size="xs">
        {props.children}
        <IconCircleUpright size={16} className="text-yellow-800" />
      </Button>
    </a>
  ),
  
  // Lists
  ul: ({ ...props }) => (
    <ul className="list-disc pl-5 leading-none">
      {props.children}
    </ul>
  ),
  ol: ({ ...props }) => (
    <ol className="list-decimal pl-5 leading-none">
      {props.children}
    </ol>
  ),
  li: ({ ...props }) => (
    <li className="leading-[1.5]">
      {props.children}
    </li>
  ),
  
  // Images
  img: ({ src, alt }) => (
    <div className="my-4">
      <Image
        src={src as string || "/assets/images/<EMAIL>"}
        alt={alt || "Image"}
        width={500}
        height={300}
        className="rounded-md object-cover"
      />
    </div>
  ),
  
  // Table Components
  table: ({ ...props }) => {
    // Check if we're streaming and the table might be incomplete
    if (isStreaming) {
      // This is a simple incomplete table check, you might want to implement more sophisticated detection
      const tableContent = props.children?.toString() || '';
      const hasData = tableContent.includes('</tr>');
      
      if (!hasData) {
        return <TableSkeleton />;
      }
    }
    
    return (
      <Card className="w-full p-5 bg-white">
        <table className="min-w-full bg-white divide-y divide-gray-200">
          {props.children}
        </table>
      </Card>
    );
  },
  
  thead: ({ ...props }) => (
    <thead>
      {props.children}
    </thead>
  ),
  
  tbody: ({ ...props }) => (
    <tbody className="divide-y divide-gray-200">
      {props.children}
    </tbody>
  ),
  
  tr: ({ ...props }) => (
    <tr className="even:bg-muted-500/10 transition-colors">
      {props.children}
    </tr>
  ),
  
  th: ({ ...props }) => (
    <th className="group">
        <span className="bg-muted-500/10 leading=[1.6] flex h-9 items-center px-2 py-1 text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase group-first:rounded-l-lg group-last:rounded-r-lg">
        {props.children}
        </span>
    </th>
  ),
  
  td: ({ ...props }) => (
    <td className="border-muted-500/30 border-b px-2 py-3 text-sm font-medium text-neutral-900">
      {props.children}
    </td>
  ),
  
  // Blockquotes - handles card: syntax and streaming
  blockquote: ({  children }) => {
    // Step 1: Flatten all children text into one single string
    const getTextFromChildren = (nodes: React.ReactNode): string => {
      if (Array.isArray(nodes)) {
        return nodes.map(getTextFromChildren).join("");
      }
      if (typeof nodes === "string") return nodes;
      if (React.isValidElement(nodes)) {
        const props = nodes.props as { children?: React.ReactNode };
        return getTextFromChildren(props.children);
      }
      return "";
    };

    const text = getTextFromChildren(children);

    // Step 2: Check if it contains card:
    if (text.includes("card:")) {
      // Check if we're streaming and the card block is incomplete
      if (isStreaming && !isCardBlockComplete(text)) {
        // Show skeleton placeholders for incomplete card blocks
        return (
          <div className="grid grid-cols-3 gap-3">
            {[1, 2, 3].map((_, idx) => (
              <CardSkeleton key={idx} />
            ))}
          </div>
        );
      }

      // Parse complete card blocks
      const cardEntries = text.split(/card:/g).filter((s) => s.trim() !== "");

      const cards: CardData[] = cardEntries.map((entry) => {
        const parts = entry.trim().split("|").map((s) => s.trim());
        return {
          title: parts[0] ?? "Not available",
          description: parts[1] ?? "",
          image: parts[2] ?? "",
          subTitle: parts[3] ?? "",
        };
      });

      return (
        <div className="grid grid-cols-3 gap-3">
          {cards.map((card, idx) => (
            <CardItem data={card} key={idx} />
          ))}
        </div>
      );
    }

    // Step 4: Fallback regular blockquote
    return (
      <blockquote className="border-l-4 pl-4 text-gray-600 italic my-4">
        {children}
      </blockquote>
    );
  }
});

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ 
  markdown,
  className,
  isStreaming = false
}) => {
  // Preprocess markdown to fix list formatting issues
  const processedMarkdown = useMemo(() => {
    if (!markdown) return '';
    
    // Helper function to identify list items (both ordered and unordered)
    const isListItem = (line: string): boolean => {
      return /^\s*[-*+]\s+/.test(line) || /^\s*\d+\.\s+/.test(line);
    };
    
    // Split into lines for more precise processing
    const lines = markdown.split('\n');
    const processedLines: string[] = [];
    
    // Process line by line for better control
    for (let i = 0; i < lines.length; i++) {
      const currentLine = lines[i];
      const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
      const prevLine = i > 0 ? lines[i - 1] : '';
      
      // Check if this is a list item
      const currentIsListItem = isListItem(currentLine);
      const nextIsListItem = isListItem(nextLine);
      const prevIsListItem = isListItem(prevLine);
      
      // Check if we have an empty line between list items
      const nextIsEmptyLine = nextLine.trim() === '';
      const nextNextIsListItem = i < lines.length - 2 && isListItem(lines[i + 2]);
      
      // Determine if we're in a nested list context
      const currentIndent = currentLine.match(/^\s*/)?.[0]?.length || 0;
      const nextIndent = nextLine.match(/^\s*/)?.[0]?.length || 0;
      const isNested = currentIsListItem && nextIsListItem && nextIndent > currentIndent;
      
      // Add the current line
      processedLines.push(currentLine);
      
      // Handle specific cases:
      if (currentIsListItem) {
        // Case 1: Current line is a list item and next line is empty, but there's another list item after
        if (nextIsEmptyLine && nextNextIsListItem) {
          // Skip the next empty line by incrementing i
          i++;
          continue;
        }
        
        // Case 2: Current line is a list item and there's intended content (indented non-list text)
        // after an empty line - remove the empty line
        if (nextIsEmptyLine && i < lines.length - 2) {
          const afterEmptyLine = lines[i + 2];
          const afterEmptyLineIndent = afterEmptyLine.match(/^\s*/)?.[0]?.length || 0;
          
          if (afterEmptyLineIndent > currentIndent && !isListItem(afterEmptyLine)) {
            // Skip the empty line
            i++;
            continue;
          }
        }
        
        // Case 3: Handle nested list transitions
        if (isNested) {
          // Ensure no blank line when going deeper in nesting
          if (nextIsEmptyLine) {
            i++; // Skip the empty line
            continue;
          }
        }
      }
      
      // Special case for paragraphs inside list items
      if (!currentIsListItem && !prevIsListItem && nextIsListItem) {
        const currentIndent = currentLine.match(/^\s*/)?.[0]?.length || 0;
        const nextIndent = nextLine.match(/^\s*/)?.[0]?.length || 0;
        
        // If next line is more indented, this might be content belonging to a list item
        if (nextIndent > currentIndent && nextIsEmptyLine) {
          i++; // Skip the empty line
          continue;
        }
      }
    }
    
    // Join back into a string
    return processedLines.join('\n')
      // Perform additional cleanup with regex for cases not caught above
      .replace(/^(\s*[-*+]\s+.+?)\n\n(\s+\S)/gm, '$1\n$2') // Fix unordered list with indented content
      .replace(/^(\s*\d+\.\s+.+?)\n\n(\s+\S)/gm, '$1\n$2'); // Fix ordered list with indented content
  }, [markdown]);
  
  // Get the appropriate components based on streaming state
  const components = createComponents(isStreaming);

  return (
    <div className={`flex w-full flex-col items-start ${className || ''}`}>
      <div className="w-full max-w-none">
        <ReactMarkdown remarkPlugins={[remarkGfm]} components={components}>
          {processedMarkdown}
        </ReactMarkdown>
      </div>
    </div>
  );
};

export default MarkdownRenderer; 