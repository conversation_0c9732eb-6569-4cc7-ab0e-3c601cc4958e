import { useState } from "react";
import Paragraph from "@/components/base/paragraph";
import Card from "@/components/base/card";
import Tag from "@/components/base/tag";
import { IconLocation, IconMenuAdoption } from "@/components/icons";
import { SafeImage } from "@/components/utils/helpers";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import DataSourcesReports from "@/components/shared/dataSourcesReports";
import { useIngredient } from "@/contexts/IngredientContext";
import { useQuery } from "@tanstack/react-query";
import { fetchIngredientMenuItems } from "@/api/ingredients";
import { useSession } from "next-auth/react";
import titleCase from "voca/title_case";
import Pagination from "@/components/table/pagination";
import Link from "next/link";


enum RestaurantType {
  qsr = "QSR",
  fast_casual = "Fast Casual",
  mid_scale = "Mid Scale",
  fine_dining = "Fine Dining",
  other = "Other",
}

function truncateString(str: string, maxLength = 40) {
  if (str.length <= maxLength) return str;
  return str.slice(0, maxLength - 3) + "...";
}

const IngredientsDetailsOnMenu = () => {
  const { data: session } = useSession();

  const { guid } = useIngredient();

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [search, setSearch] = useState<string>("");

  const { data: items } = useQuery({
    queryFn: () => {
      return fetchIngredientMenuItems({
        auth: session?.user.authorization as string,
        guid: guid,
        page,
        pageSize,
        secondary_filter: search,
      });
    },
    queryKey: ["items", guid, page, pageSize, search],
    enabled: !!session?.user?.authorization,
  });

  const itemsMap = items?.data.map((item: MenuItem) => {
    const {
      id,
      name,
      price,
      description,
      business_name,
      restaurant_type,
      restaurant_guid,
      cuisine,
      city,
    } = item;
    let { image_url } = item;
    if (image_url === "[nil]" || image_url === null) {
      image_url = "";
    }

    return (
      <Card key={id} className="p-3">
        <SafeImage
          src={image_url ? image_url.trim() : ""}
          alt={name}
          width={100}
          height={232}
          className="mb-1 h-[140px] w-full rounded-lg object-cover"
        />
        <div className="mb-2 rounded-lg border border-neutral-200 p-2">
          <div className="mb-2 flex items-center justify-between gap-2">
            <Tag>{titleCase(name)}</Tag>
            <Paragraph size="lg" className="font-semibold">
              {price > 0 && `$${price}`}
            </Paragraph>
          </div>
          <Paragraph size="xs" className="font-medium text-neutral-600">
            {titleCase(truncateString(description))}
          </Paragraph>
        </div>
        <Paragraph
          size="sm"
          className="mb-1 flex items-center gap-2 px-2 font-medium"
        >
          <IconLocation size={16} className="text-neutral-500" />
          <Link href={`/menu-items/restaurant/${restaurant_guid}`}>
            {titleCase(business_name)}
          </Link>
        </Paragraph>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
              Type
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {RestaurantType[restaurant_type as keyof typeof RestaurantType]}
            </Paragraph>
          </div>
        </div>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
              Cuisine
            </Paragraph>
            <Tag variant="white">
              <IconMenuAdoption size={16} className="text-neutral-500" />
              {cuisine.length > 0 ? titleCase(cuisine[0]) : ""}
            </Tag>
          </div>
        </div>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
              Location
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {titleCase(city)}
            </Paragraph>
          </div>
        </div>
      </Card>
    );
  });

  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPageSize(pageSize);
  };

  return (
    <section className="mt-[60px] py-4">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-4 gap-5">{itemsMap}</div>
        <Pagination
          currentPage={page}
          perPage={pageSize}
          totalResults={items?.total_records}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default IngredientsDetailsOnMenu;
