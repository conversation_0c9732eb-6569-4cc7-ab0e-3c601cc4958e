import { useState, useEffect } from "react";
import Card, { <PERSON><PERSON>eader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Tag from "@/components/base/tag";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { IconCircleUpright, IconIncrease, IconMore } from "@/components/icons";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";
import { useIngredient } from "@/contexts/IngredientContext";

import { fetchPenRateOverTime } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import titleCase from "voca/title_case";

// const lineChartData: LineSeriesItem[] = [
//   {
//     id: 1,
//     label: "Bok Cho<PERSON>",
//     color: "#FF6D56",
//     data: [0, 3, 2.5, 3.0, 5.0, 6, 8],
//   },
// ];

interface Quarter {
  quarter: number;
  year: number;
  pen_rate: number;
}

const IngredientsStats = () => {
  const { data: session } = useSession();

  const [selected, setSelected] = useState<string>("");

  const { guid, name, pen_rate, google_trend, change_1y, growth_prediction } =
    useIngredient();
  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const isPenPositive = pen_rate > 0;

  const { data: pen_rate_over_time } = useQuery({
    queryFn: () => {
      return fetchPenRateOverTime({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["pen_rate_over_time", guid],
    enabled: !!session?.user?.authorization,
  });

  const [chartData, setChartData] = useState<LineSeriesItem[]>([]);
  const [chartLabels, setChartLabels] = useState([]);
  useEffect(() => {
    const generatePenRateChartData = () => {
      return [
        {
          id: 1,
          label: titleCase(name),
          color: "#FF6D56",
          data: pen_rate_over_time?.map((q: Quarter) => q.pen_rate),
        },
      ];
    };
    const generatePenRateLabels = () => {
      return pen_rate_over_time?.map((q: Quarter) => `Q${q.quarter} ${q.year}`);
    };
    setChartData(generatePenRateChartData());
    setChartLabels(generatePenRateLabels());
  }, [name, pen_rate_over_time, setChartData, setChartLabels]);

  return (
    <>
      <div className="grid grid-cols-2 gap-5">
        <Card className="p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <Heading level={5}>Menu Penetration</Heading>
          </div>
          <div className="flex items-end justify-between">
            <Heading level={2} className="flex items-end gap-1.5">
              {isPenPositive && <span className="text-4">+</span>}
              {pen_rate}%
            </Heading>
            <Tag variant="greenOutline" className="px-1">
              <IconIncrease size={16} />
              {/*175,56 %*/}
            </Tag>
          </div>
        </Card>
        <Card className="p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <Heading level={5}>Google Trends</Heading>
            <IconCircleUpright size={24} className="text-yellow-800" />
          </div>
          <div className="flex items-end justify-between">
            <Heading level={2} className="flex items-end gap-1.5">
              {google_trend}
              <span className="text-sm font-medium text-neutral-600">
                search of term popularity
              </span>
            </Heading>
          </div>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <Heading level={4}>Foodservice Growth</Heading>
            <Info
              tooltipId="foodservice-growth-tooltip"
              content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Adipisci rem sit molestias officia mollitia"
            />
          </div>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </CardHeader>
        <div className="p-4">
          <div className="mb-5 grid grid-cols-2 gap-5">
            <Card className="p-4">
              <div className="mb-2">
                <Heading level={5}>Compared</Heading>
                <Paragraph size="xs" className="font-medium text-neutral-600">
                  to Previous Period
                </Paragraph>
              </div>
              <div className="flex items-end justify-between">
                <Heading level={2} className="flex items-end gap-1.5">
                  {change_1y > 0 && <span className="text-4">+</span>}
                  {change_1y?.toFixed(2)}%
                </Heading>
                <Tag variant="greenOutline" className="px-1">
                  <IconIncrease size={16} />
                </Tag>
              </div>
            </Card>
            <Card className="p-4">
              <div className="mb-2">
                <Heading level={5}>Prediction</Heading>
                <Paragraph size="xs" className="font-medium text-neutral-600">
                  For Upcoming Period
                </Paragraph>
              </div>
              <div className="flex items-end justify-between">
                <Heading level={2} className="flex items-end gap-1.5">
                  {growth_prediction > 0 && <span className="text-4">+</span>}
                  {growth_prediction}%
                </Heading>
                <Tag variant="greenOutline" className="px-1">
                  <IconIncrease size={16} />
                </Tag>
              </div>
            </Card>
          </div>
          <div className="border-muted-100 rounded-lg border p-3">
            {pen_rate_over_time && (
              <LineChart
                dataItems={chartData}
                labels={chartLabels}
                showLegend={false}
                symbol=""
                height={255}
                yTickCallback={(val) => {
                  if (typeof val === "number") {
                    val = val.toFixed(2);
                  }
                  return `${val}%`;
                }}
                //   yTitle = {{
                //     display: true,
                //     text: "Pen Rate"
                // }}
              />
            )}
          </div>
        </div>
      </Card>
    </>
  );
};

export default IngredientsStats;
