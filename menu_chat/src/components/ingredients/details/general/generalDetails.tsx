import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import ImageSlider from "@/components/base/imageSlider";
import {
  IconCircleUpright,
  IconCircleFilled,
  IconAroma,
  IconTaste2,
  IconAppearance,
} from "@/components/icons";
import { useIngredient } from "@/contexts/IngredientContext";
import titleCase from "voca/title_case";
import { getVariantForMenuAdoption } from "@/components/utils/helpers";
import CollapseCard from "@/components/base/collapseCard";

const GeneralDetails = () => {
  const {
    name,
    category,
    menu_adoption,
    appearance,
    texture,
    aroma,
    taste,
    description,
    images,
  } = useIngredient();

  const adoptionColor = getVariantForMenuAdoption(titleCase(menu_adoption));

  const imagesMap = images?.map((i) => i.url) || [];
  return (
    <Card className="p-4">
      <div className="flex items-start justify-between gap-2">
        <div className="flex w-1/2 gap-2">
          <div className="w-1/2">
            <Heading level={3} className="mb-3">
              {titleCase(name)}
            </Heading>
            <div className="flex flex-wrap gap-1">
              <Tag variant="white">{titleCase(category)}</Tag>
              <Tag variant={adoptionColor}>
                <IconCircleUpright size={16} />
                {titleCase(menu_adoption)}
              </Tag>
            </div>
          </div>
          <div className="flex w-1/2 flex-col gap-2">
            <div className="flex flex-col gap-2 rounded-lg border border-neutral-100 p-3">
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Appearance
                </Paragraph>
                <Tag variant="white">
                  <IconAppearance size={16} className="text-muted-500" />
                  {titleCase(appearance)}
                </Tag>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Texture
                </Paragraph>
                <Tag variant="white">
                  <IconCircleFilled size={16} className="text-muted-500" />
                  {titleCase(texture)}
                </Tag>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Aroma
                </Paragraph>
                <Tag variant="white">
                  <IconAroma size={16} className="text-muted-500" />
                  {titleCase(aroma)}
                </Tag>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Taste
                </Paragraph>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-muted-500" />
                  {titleCase(taste)}
                </Tag>
              </div>
            </div>
            <CollapseCard text={description} />
          </div>
        </div>
        <ImageSlider
          thumbnailPosition="right"
          className="w-1/2"
          images={imagesMap}
        />
      </div>
    </Card>
  );
};

export default GeneralDetails;
