import { useEffect, useState } from 'react';
import Card, { <PERSON>Header } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Link from "next/link";
import Bar<PERSON>hart from "@/components/charts/barChart";

import { fetchMenuTypes } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";

import titleCase from "voca/title_case";
import ColorGenerator from "@/components/utils/color-generator";

// TODO: get actual data from API
// const chartData: BarChartItem[] = [
//   {
//     id: 1,
//     label: "Chinese",
//     color: "#78C5E3",
//     value: 35.36,
//     symbol: "%",
//   },
//   { id: 2, label: "Japenese", color: "#FAAB61", value: 75, symbol: "%" },
//   { id: 3, label: "Thai", color: "#DF9DE4", value: 40, symbol: "%" },
//   { id: 4, label: "Asian", color: "#FF9985", value: 10, symbol: "%" },
//   { id: 5, label: "Mexican", color: "#BDA4CB", value: 25, symbol: "%" },
// ];


interface MenuTypeResponse {
  name: string
  items_count: number
  total_items_count: number
  percentage: number
}

const MenuTypes = () => {
  const { data: session } = useSession();

  const {guid} = useIngredient();

  const { data: menu_types }  = useQuery({
    queryFn: () => {
      return fetchMenuTypes({auth: session?.user.authorization as string, guid: guid})
    },
    queryKey: ["menu_types", guid],
    enabled: !!(session?.user?.authorization),
  });

  const [menuTypesMap, setMenuTypesMap] = useState([])
  const [maxScale, setMaxScale] = useState(0)

  useEffect(()=>{
    const colorGen = new ColorGenerator(guid);

    const getMaxScale = () => {
      const percents = menu_types?.data?.map((t: MenuTypeResponse)  => t.percentage) || []
      const maxPercent = Math.max(...percents)
      return Math.round(maxPercent * 1.25)
    }
    const result = menu_types?.data?.map((menu_type: MenuTypeResponse, idx: number)=>{
      const {name, percentage} = menu_type
      return ({ id: idx, label: titleCase(name), color: colorGen.getColor(name), value: percentage, symbol: "%" })
    })
    setMenuTypesMap(result || [])
    setMaxScale(getMaxScale())

  }, [guid, menu_types])


  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Menu Types</Heading>
          <Info
            tooltipId="menu-types-tooltip"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Link href="/ingredients/">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="p-4">
        <div className="border-muted-100 rounded-lg border">
          <BarChart
            dataItems={menuTypesMap}
            symbol="%"
            maxValue={maxScale}
            height={250}
          />
        </div>
      </div>
    </Card>
  );
};

export default MenuTypes;
