import { useState } from "react";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import { fetchPairingTrendsForIng } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";
import titleCase from "voca/title_case";
import { getVariantForChange } from "@/components/utils/helpers";



const PairingTrends = () => {
  const { data: session } = useSession();

  const {guid} = useIngredient();

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const { data: pairing_trends }  = useQuery({
    queryFn: () => {
      return fetchPairingTrendsForIng({auth: session?.user.authorization as string, guid: guid, limit: 3})
    },
    queryKey: ["ing_pairing_trends", guid],
    enabled: !!(session?.user?.authorization),
  });


  const pairingTrendsMap = pairing_trends?.data?.map((pair: Pairing)=>{
    const {ingredient_a_name, ingredient_a_guid, ingredient_b_name, ingredient_b_guid, pairing_percent, change} = pair
    const variant = getVariantForChange(change)

    return (
      <Tr key={ingredient_b_name}>
        <Td>
          <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
            <a href={`/ingredients/details/${ingredient_a_guid}`}>
              <Tag>{titleCase(ingredient_a_name)}</Tag>
            </a>
            <a href={`/ingredients/details/${ingredient_b_guid}`}>
              <Tag>{titleCase(ingredient_b_name)}</Tag>
            </a>
          </div>
        </Td>
        <Td>{pairing_percent?.toFixed(2)}%</Td>
        <Td>
          <Tag variant={variant.type}>
            {variant.icon}
            {change?.toFixed(2)}%
          </Tag>
        </Td>
      </Tr>
    )
  })


  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Pairing Trends</Heading>
          <Info
            tooltipId="pairing-trends-tooltip"
            content="Most popular (used) pairing ingredients trends in last quarter."
          />
        </div>
        <Dropdown
          options={MENU_ADOPTION_CURVE_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Pairing %</Th>
            <Th>Change</Th>
          </Tr>
        </Thead>
        <Tbody>
          {
            pairingTrendsMap
          }
          {/*<Tr>
            <Td>
              <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                <Tag>Bok Choy</Tag>
                <Tag>Cranberries</Tag>
              </div>
            </Td>
            <Td>5,44%</Td>
            <Td>
              <Tag variant="greenOutline">
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                <Tag>Bok Choy</Tag>
                <Tag>Pistachio</Tag>
              </div>
            </Td>
            <Td>5,44%</Td>
            <Td>
              <Tag variant="blueOutline">
                <IconGraph size={16} />
                3,25%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                <Tag>Bok Choy</Tag>
                <Tag>Grape</Tag>
              </div>
            </Td>
            <Td>5,44%</Td>
            <Td>
              <Tag variant="redOutline">
                <IconDecrease size={16} />
                3,25%
              </Tag>
            </Td>
          </Tr>*/}
        </Tbody>
      </Table>
    </Card>
  );
};

export default PairingTrends;
