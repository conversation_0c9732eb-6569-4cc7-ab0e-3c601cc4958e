import { useEffect, useState } from "react";
import Card from "@/components/base/card";
import Button from "@/components/base/button";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Tabs, { TabContent } from "@/components/base/tabs";
import BarChart from "@/components/charts/barChart";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";
import { useQuery } from "@tanstack/react-query";
import {
  fetchIngredientCategories,
  fetchIngredientSubcategories,
} from "@/api/ingredients";

import titleCase from "voca/title_case";


// // TODO: get actual data from API
// const chartData1: BarChartItem[] = [
//   {
//     id: 1,
//     label: "Fruit",
//     color: "#A2D161",
//     value: 45,
//     symbol: "%",
//   },
//   { id: 2, label: "Dairy", color: "#D2D0BC", value: 42, symbol: "%" },
//   { id: 3, label: "Fermented", color: "#D2D0BC", value: 33, symbol: "%" },
//   {
//     id: 4,
//     label: "Vegetables",
//     color: "#D2D0BC",
//     value: 31,
//     symbol: "%",
//   },
//   { id: 5, label: "Beverages", color: "#D2D0BC", value: 28, symbol: "%" },
// ];

//
// const chartData2: BarChartItem[] = [
//   {
//     id: 1,
//     label: "Breakfast entrees",
//     color: "#A2D161",
//     value: 42,
//     symbol: "%",
//   },
//   { id: 2, label: "Burgers", color: "#D2D0BC", value: 38, symbol: "%" },
//   { id: 3, label: "Tacos", color: "#D2D0BC", value: 30, symbol: "%" },
//   {
//     id: 4,
//     label: "Burritos",
//     color: "#D2D0BC",
//     value: 28,
//     symbol: "%",
//   },
//   { id: 5, label: "Tortillas", color: "#D2D0BC", value: 25, symbol: "%" },
// ];

const TopCategories = () => {
  const { data: session } = useSession();

  const {  guid } = useIngredient();

  const { data: ingredient_categories } = useQuery({
    queryFn: () => {
      return fetchIngredientCategories({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["ingredient_categories", guid],
    enabled: !!session?.user?.authorization,
  });

  const [chartIngredientCategories, setIngredientCategories] = useState([]);
  const [maxIc, setMaxIc] = useState<number>(100);

  const { data: ingredient_subcategories } = useQuery({
    queryFn: () => {
      return fetchIngredientSubcategories({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["ingredient_subcategories", guid],
    enabled: !!session?.user?.authorization,
  });

  const [chartIngredientSubcategories, setIngredientSubcategories] = useState([]);
  const [maxIsc, setMaxIsc] = useState<number>(100);


  useEffect(() => {
    const chart1 = ingredient_categories?.map((item: {id: string, name: string, distribution: number, region: string}, idx: number) => {
      return {
        id: item.id,
        label: titleCase(item.name),
        color: idx == 0 ? "#A2D161" : "#D2D0BC",
        value: item.distribution * 100,
        symbol: "%",
      }
    }) || []
    setIngredientCategories(chart1)
    setMaxIc(chart1[0]?.value ? Math.round(chart1[0].value * 1.15) : 100)

    const chart2 = ingredient_subcategories?.map((item: {id: string, name: string, distribution: number, region: string}, idx: number) => {
      return {
        id: item.id,
        label: titleCase(item.name),
        color: idx == 0 ? "#A2D161" : "#D2D0BC",
        value: item.distribution * 100,
        symbol: "%",
      }
    }) || []
    setIngredientSubcategories(chart2)
    setMaxIsc(chart2[0]?.value ? Math.round(chart2[0].value * 1.15) : 100)
  }, [ingredient_categories, setMaxIc, ingredient_subcategories, setIngredientSubcategories, setMaxIsc])


  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className="p-4">
      <div className="relative flex flex-wrap items-center justify-between gap-2">
        <Tabs navigation={["Top Categories", "Top Subcategories"]}>
          <TabContent>
            <div className="border-muted-100 rounded-lg border p-4 pt-0">
              <BarChart
                dataItems={chartIngredientCategories}
                height={260}
                symbol=""
                maxValue={maxIc}
              />
            </div>
          </TabContent>
          <TabContent>
            <BarChart
              dataItems={chartIngredientSubcategories}
              height={260}
              symbol=""
              maxValue={maxIsc}
            />
          </TabContent>
        </Tabs>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
          className="absolute top-0 right-0"
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
    </Card>
  );
};

export default TopCategories;
