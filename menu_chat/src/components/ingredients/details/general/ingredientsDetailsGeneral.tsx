import DataSourcesReports from "@/components/shared/dataSourcesReports";
import GeneralDetails from "./generalDetails";
import IngredientsStats from "./ingredientsStats";
//import MenuTypes from "./menuTypes";
import PairingTrends from "./pairingTrends";
import TopCategories from "./topCategories";

const IngredientsDetailsGeneral = () => {
  return (
    <section className="mt-[60px] py-4">
      <div className="flex flex-col gap-5">
        <GeneralDetails />
        <div className="grid grid-cols-2 gap-5">
          <div className="flex flex-col gap-5">
            <IngredientsStats />
          </div>
          <div className="flex flex-col gap-5">
            {/* <MenuTypes /> */}
            <TopCategories />
            <PairingTrends />
          </div>
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default IngredientsDetailsGeneral;
