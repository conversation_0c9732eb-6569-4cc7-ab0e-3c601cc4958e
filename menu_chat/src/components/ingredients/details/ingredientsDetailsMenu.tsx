import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconCalendar } from "@/components/icons";
import { getIngredientDetailsMenu } from "@/data/ingredients";

type Props = {
  ingredientSlug: string;
};

const IngredientsDetailsMenu = ({ ingredientSlug }: Props) => {
  const [selectedPeriod, setSelectedPeriod] = useState<string>("Last quarter");
  const pathname = usePathname();

  const handleSelectPeriod = (value: string) => {
    setSelectedPeriod(value);
  };

  const menu = ingredientSlug && getIngredientDetailsMenu(ingredientSlug) || [];

  return (
    <div className="bg-custom-blur absolute top-0 right-0 left-0 z-20 m-auto flex w-full items-center justify-between gap-2 px-8 py-3">
      <div className="flex items-center gap-1">
        {menu.map((item) => (
          <Link key={item.slug} href={item.slug}>
            <Button
              variant="tertiary"
              size="sm"
              className={twMerge(
                "hover:bg-muted-100 active:bg-muted-100 font-archivo rounded-lg border-0 px-4",
                pathname === item.slug
                  ? "bg-muted-100 hover:bg-muted-100 active:bg-muted-200"
                  : "bg-transparent",
              )}
            >
              {item.title}
            </Button>
          </Link>
        ))}
      </div>
      <div className="flex items-center gap-1">
        <Dropdown
          options={[
            { label: "Last quarter", value: "Last quarter" },
            { label: "This quarter", value: "This quarter" },
            { label: "This month", value: "This month" },
          ]}
          selectedValue={selectedPeriod}
          onSelect={(e) => handleSelectPeriod(e)}
          icon={<IconCalendar size={16} />}
          withCaret
        />
      </div>
    </div>
  );
};

export default IngredientsDetailsMenu;
