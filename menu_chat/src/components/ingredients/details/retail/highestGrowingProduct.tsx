import { useState } from "react";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import LineChart from "@/components/charts/lineChart";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { highestGrowingIngredientByCategory } from "@/api/ingredients";
import { useIngredient } from "@/contexts/IngredientContext";

const HighestGrowingProduct = () => {
  const { data: session } = useSession();
  const ingredientId = useIngredient().guid;
  const { data, isLoading, error } = useQuery({
    queryFn: () => highestGrowingIngredientByCategory({ auth: session?.user.authorization as string, ingredientId }),
    queryKey: ["highest_growing_ingredient_by_category", ingredientId],
    enabled: !!session?.user?.authorization && !!ingredientId,
  });

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Highest Growing Product Category</Heading>
          <Info
            tooltipId="menu-types-tooltip"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit. Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Dropdown
          options={MENU_ADOPTION_CURVE_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="border-muted-100 rounded-lg border p-4">
          {isLoading ? (
            <div className="text-center text-neutral-600 py-4">Loading...</div>
          ) : error ? (
            <div className="text-center text-red-500 py-4">Error loading data</div>
          ) : (
            <LineChart
              dataItems={data?.data || []}
              labels={data?.labels || []}
              symbol=""
              height={300}
              yTitle={{
                display: true,
                text: 'Product Count',
              }}
            />
          )}
        </div>
      </div>
    </Card>
  );
};

export default HighestGrowingProduct;
