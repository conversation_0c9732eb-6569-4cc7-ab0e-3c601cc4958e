import { useState } from "react";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import DonutChart from "@/components/charts/donutChart";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchProductCategoryChartData } from "@/api/ingredients";
import { useIngredient } from "@/contexts/IngredientContext";

const MostCommonProduct = () => {
  const { data: session } = useSession();
  const ingredientId = useIngredient().guid;

  const { data, isLoading, error } = useQuery({
    queryFn: () =>
      fetchProductCategoryChartData({
        auth: session?.user.authorization as string,
        ingredientId,
      }),
    queryKey: ["most_common_product_category", ingredientId],
    enabled: !!session?.user?.authorization && !!ingredientId,
  });

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const chartData = Array.isArray(data) ? data : [];
  const totalValue = chartData.reduce((sum, item) => sum + item.value, 0);

  const dataForChart = chartData.map((item) => ({
    ...item,
    value:
      totalValue > 0 ? Number(((item.value / totalValue) * 100).toFixed(1)) : 0,
  }));

  const dataWithOriginalValues = chartData.map((item) => ({
    ...item,
    originalValue: item.value,
    percentage:
      totalValue > 0 ? ((item.value / totalValue) * 100).toFixed(1) : "0.0",
  }));

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Common Product Category</Heading>
          <Info
            tooltipId="menu-types-tooltip"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit. Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <div className="border-muted-100 flex items-center justify-between gap-5 rounded-lg border p-3">
          {isLoading ? (
            <div className="py-4 text-center text-neutral-600">Loading...</div>
          ) : error ? (
            <div className="py-4 text-center text-red-500">
              Error: Server issue (500). Please try again later.
            </div>
          ) : chartData.length === 0 ? (
            <div className="py-4 text-center text-neutral-600">
              No data available
            </div>
          ) : (
            <>
              <DonutChart
                data={dataForChart}
                size={235}
                showLabel={false}
                pattern={true}
              />
              <div className="flex w-full flex-1 flex-col gap-1 text-xs font-medium">
                {dataWithOriginalValues.map((item, index) => (
                  <div
                    key={index}
                    className="border-muted-500/30 flex items-center justify-between border-b px-1 py-1.5"
                  >
                    <span className="flex items-center gap-1">
                      <span
                        className="h-3 w-1 rounded-full"
                        style={{ backgroundColor: item.color }}
                      ></span>
                      {item.label}
                    </span>
                    <span className="text-neutral-600">
                      {item.originalValue}
                    </span>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </Card>
  );
};

export default MostCommonProduct;
