import { useState, useMemo, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import {
  ColumnDef,
  ColumnResizeMode,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import NextImage from "next/image";
import { fetchProductsTable } from "@/api/ingredients";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import RetailProductsFilters from "@/components/table/RetailProductsFilters";
import { useIngredient } from "@/contexts/IngredientContext";

interface ProductTableData {
  id: number;
  name: string;
  category: string;
  product: { name: string; image_urls: string[] };
  price: string;
  created_at: string;
  upc?: string;
  listing_url?: string;
}

interface ProductWithValidImage extends ProductTableData {
  validImageUrl: string;
}

const RetailTable = () => {
  const { data: session } = useSession();
  const ingredientId = useIngredient().guid;

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [pagination, setPagination] = useState({
    pageIndex: page - 1,
    pageSize: pageSize,
  });

  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([]);

  const { data: productsTable, isLoading } = useQuery({
    queryFn: () =>
      fetchProductsTable({
        auth: session?.user.authorization as string,
        ingredientId,
        pagination,
        search,
        sorting,
      }),
    queryKey: ["productsTable", ingredientId, pagination, search, sorting],
    enabled: !!session?.user?.authorization,
  });

  // State to store products with validated image URLs
  const [dataWithValidImages, setDataWithValidImages] = useState<ProductWithValidImage[]>([]);

  // Validate images when productsTable data changes
  useEffect(() => {
    if (!productsTable?.data) {
      setDataWithValidImages([]);
      return;
    }

    const checkImage = (url: string): Promise<boolean> => {
      return new Promise((resolve) => {
        const img = new window.Image();
        img.src = url;
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        setTimeout(() => resolve(false), 5000); // Timeout after 5 seconds
      });
    };

    const validateImages = async () => {
      const validatedData = await Promise.all(
        productsTable.data.map(async (product) => {
          const imageUrl = product.product.image_urls[0];
          let validImageUrl = "/assets/images/<EMAIL>";

          if (imageUrl) {
            const isValid = await checkImage(imageUrl);
            if (isValid) {
              validImageUrl = imageUrl;
            }
          }

          return {
            ...product,
            validImageUrl,
          };
        })
      );

      setDataWithValidImages(validatedData);
    };

    validateImages();
  }, [productsTable]);

  const [columnResizeMode] = useState<ColumnResizeMode>("onChange");

  const columns = useMemo<ColumnDef<ProductWithValidImage>[]>(
    () => [
      {
        accessorKey: "retailer_name",
        header: "Name",
        cell: (info) => <span>{info.row.original.name}</span>,
        size: 150,
      },
      {
        accessorKey: "category",
        header: "Category",
        cell: (info) => <Tag variant="white">{info.getValue<string>()}</Tag>,
        size: 150,
      },
      {
        accessorKey: "product",
        header: "Product",
        cell: (info) => {
          const product = info.row.original;
          const imageUrl = product.validImageUrl;

          return (
            <div className="flex items-center gap-2">
              <NextImage
                src={imageUrl}
                alt={product.product.name}
                width={40}
                height={40}
                className="size-10 rounded-sm object-cover object-center"
              />
              <span>{product.product.name}</span>
            </div>
          );
        },
        size: 300,
        enableSorting: false,
      },
      {
        accessorKey: "price",
        header: "Price",
        cell: (info) => `$${parseFloat(info.getValue<string>()).toFixed(2)}`,
        size: 100,
      },
      {
        accessorKey: "created_at",
        header: "Date Created",
        cell: (info) => new Date(info.getValue<string>()).toLocaleDateString(),
        size: 150,
      },
    ],
    []
  );

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    setPage(1);
    setPagination({ pageIndex: 0, pageSize: pageSize });
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
    const [column, direction] = newSort.split("-");
    setSorting(
      newSort === "heading" ? [] : [{ id: column, desc: direction === "lowest" }]
    );
    setPage(1);
    setPagination({ pageIndex: 0, pageSize: pageSize });
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setPagination({ pageIndex: newPage - 1, pageSize: pageSize });
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
    setPagination({ pageIndex: 0, pageSize: newPageSize });
  };

  const emptyState = (
    <div className="text-center py-6">
      <p className="text-gray-500">No products found</p>
      <p className="text-sm text-gray-400">Try adjusting your search or filters</p>
    </div>
  );

  const table = useReactTable({
    data: dataWithValidImages,
    columns,
    state: { pagination, sorting },
    enableColumnResizing: true,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    manualSorting: true,
    manualPagination: true,
    rowCount: productsTable?.recordsTotal,
    columnResizeMode,
    defaultColumn: { size: 150, minSize: 50, maxSize: 500 },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <div className="mt-[60px]">
      <RetailProductsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
        table={table}
        tableName="products"
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <EnhancedTable
          table={table}
          withBorder
          enableResizing
          emptyState={emptyState}
          isLoading={isLoading}
        />
        <Pagination
          currentPage={page}
          perPage={pageSize}
          totalResults={productsTable?.recordsTotal || 0}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
};

export default RetailTable;