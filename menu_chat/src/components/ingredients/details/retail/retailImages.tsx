import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import ImageSlider from "@/components/base/imageSlider";
import { fetchRetailImages } from "@/api/ingredients";
import { useState, useEffect } from "react";
import { useIngredient } from "@/contexts/IngredientContext";

interface RetailImage {
  img_url: string;
  name: string | null;
  offer_url: string | null;
}

const RetailImages = () => {
  const { data: session } = useSession();
  const ingredientId = useIngredient().guid;

  const { data: images, isLoading, error } = useQuery<RetailImage[], Error>({
    queryFn: () =>
      fetchRetailImages({
        auth: session?.user.authorization as string,
        ingredientId,
      }),
    queryKey: ["retail_images", ingredientId],
    enabled: !!(session?.user?.authorization && ingredientId),
  });

  const [validImageUrls, setValidImageUrls] = useState<string[]>([]);

  useEffect(() => {
    if (!images) return;

    const checkImage = (url: string): Promise<boolean> => {
      return new Promise((resolve) => {
        const img = new Image();
        img.src = url;
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        setTimeout(() => resolve(false), 5000);
      });
    };

    const filterValidImages = async () => {
      const uniqueImageUrls = Array.from(new Set(images.map((img) => img.img_url)));
      const validityPromises = uniqueImageUrls.map((url) => checkImage(url));
      const validityResults = await Promise.all(validityPromises);
      const validUrls = uniqueImageUrls.filter((_, index) => validityResults[index]);
      setValidImageUrls(validUrls);
    };

    filterValidImages();
  }, [images]);

  if (isLoading) {
    return <div className="text-center text-neutral-600 py-4">Loading images...</div>;
  }

  if (error) {
    return <div className="text-center text-red-500 py-4">Error loading images</div>;
  }

  if (!images || images.length === 0) {
    return <div className="text-center text-neutral-600 py-4">No images available</div>;
  }

  if (validImageUrls.length === 0) {
    return <div className="text-center text-neutral-600 py-4">No valid images available</div>;
  }

  return (
    <ImageSlider
      height={366}
      thumbnailPosition="bottom"
      images={validImageUrls}
    />
  );
};

export default RetailImages;