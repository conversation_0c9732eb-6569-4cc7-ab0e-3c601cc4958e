import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Bar<PERSON>hart from "@/components/charts/barChart";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { topRetailers } from "@/api/ingredients";
import { useIngredient } from "@/contexts/IngredientContext";

interface TopRetailersProps {
  className?: string;
}

const TopRetailers = ({ className }: TopRetailersProps) => {
  const { data: session } = useSession();
  const ingredientId = useIngredient().guid;
  const { data, isLoading, error } = useQuery({
    queryFn: () =>
      topRetailers({
        auth: session?.user.authorization as string,
        ingredientId,
      }),
    queryKey: ["top_retailers", ingredientId],
    enabled: !!session?.user?.authorization && !!ingredientId,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Top Retailers</Heading>
          <Info
            tooltipId="top-retailers"
            content="Retailers with the highest product counts"
          />
        </div>
        <Link href="/ingredients/top-retailers">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="font-archivo font-semibold bg-muted-500/10 mb-4 flex items-center justify-between rounded-md px-2 py-1 text-[11px] text-neutral-600 uppercase">
          <span>Name</span>
          <span>Product Count</span>
          <span>Avg. Price</span>
        </div>
        <div className="border-muted-100 rounded-lg border px-4 py-1">
          {isLoading ? (
            <div>Loading...</div>
          ) : error ? (
            <div>Error loading data</div>
          ) : (
            <BarChart
              dataItems={data?.data || []}
              symbol=""
              maxValue={data?.maxValue || 100}
              height={370}
              withSecondaryValue
            />
          )}
        </div>
      </div>
    </Card>
  );
};

export default TopRetailers;
