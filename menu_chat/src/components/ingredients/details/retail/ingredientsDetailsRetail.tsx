import RetailImages from "./retailImages";
import RetailTable from "./retailTable";
import RetailGrowth from "./retailGrowth";
import TopRetailers from "./topRetailers";
import TopManufacturers from "./topManufacturers";
import MostCommonProduct from "./mostCommonProduct";
import HighestGrowingProduct from "./highestGrowingProduct";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const IngredientsDetailsRetail = () => {
  return (
    <section className="mt-[60px] py-4">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-2 items-start gap-5">
          <div className="flex flex-col gap-5">
            <RetailImages />
            <RetailGrowth />
            <TopRetailers />
          </div>
          <div className="flex flex-col gap-5">
            <TopManufacturers />
            <MostCommonProduct />
            <HighestGrowingProduct />
          </div>
        </div>
        <RetailTable />
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default IngredientsDetailsRetail;
