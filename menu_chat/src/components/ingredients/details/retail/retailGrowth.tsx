import { useState } from "react";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Tag from "@/components/base/tag";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { IconIncrease, IconMore } from "@/components/icons";
import LineChart from "@/components/charts/lineChart";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchRetailGrowth } from "@/api/ingredients";
import { useIngredient } from "@/contexts/IngredientContext";

const RetailGrowth = () => {
  const { data: session } = useSession();
  const ingredientId = useIngredient().guid;
  const { data, isLoading, error } = useQuery({
    queryFn: () =>
      fetchRetailGrowth({
        auth: session?.user.authorization as string,
        ingredientId,
      }),
    queryKey: ["retail_growth", ingredientId],
    enabled: !!session?.user?.authorization && !!ingredientId,
  });

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const formatGrowth = (value: number | undefined) => {
    if (value === undefined) return "0.00";
    return value >= 0 ? `+${value.toFixed(2)}` : `${value.toFixed(2)}`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Retail Growth</Heading>
          <Info
            tooltipId="menu-types-tooltip"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit. Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <div className="mb-5 grid grid-cols-2 gap-5">
          <Card className="p-4">
            <div className="mb-2">
              <Heading level={5}>Compared</Heading>
              <Paragraph size="xs" className="font-medium text-neutral-600">
                to Previous Period
              </Paragraph>
            </div>
            <div className="flex items-end justify-between">
              {isLoading ? (
                <Paragraph>Loading...</Paragraph>
              ) : error ? (
                <Paragraph className="text-red-500">
                  Error loading data
                </Paragraph>
              ) : (
                <>
                  <Heading level={2} className="flex items-end gap-1.5">
                    <span className="text-4">
                      {formatGrowth(data?.growthPercent)}
                    </span>
                    %
                  </Heading>
                  <Tag variant="greenOutline" className="px-1">
                    <IconIncrease size={16} />
                  </Tag>
                </>
              )}
            </div>
          </Card>
          <Card className="p-4">
            <div className="mb-2">
              <Heading level={5}>Prediction</Heading>
              <Paragraph size="xs" className="font-medium text-neutral-600">
                For Upcoming Period
              </Paragraph>
            </div>
            <div className="flex items-end justify-between">
              {isLoading ? (
                <Paragraph>Loading...</Paragraph>
              ) : error ? (
                <Paragraph className="text-red-500">
                  Error loading data
                </Paragraph>
              ) : (
                <>
                  <Heading level={2} className="flex items-end gap-1.5">
                    <span className="text-4">
                      {formatGrowth(data?.growthPrediction)}
                    </span>
                    %
                  </Heading>
                  <Tag variant="greenOutline" className="px-1">
                    <IconIncrease size={16} />
                  </Tag>
                </>
              )}
            </div>
          </Card>
        </div>
        <div className="border-muted-100 rounded-lg border p-3">
          {isLoading ? (
            <div className="py-4 text-center text-neutral-600">Loading...</div>
          ) : error ? (
            <div className="py-4 text-center text-red-500">
              Error loading data
            </div>
          ) : (
            <LineChart
              dataItems={data?.lineChartData || []}
              labels={data?.labels || []}
              showLegend={false}
              symbol=""
              height={240}
            />
          )}
        </div>
      </div>
    </Card>
  );
};

export default RetailGrowth;
