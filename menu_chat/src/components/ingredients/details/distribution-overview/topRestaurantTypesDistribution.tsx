import { useEffect, useState } from "react";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Button from "@/components/base/button";
import BarChart, { BarChartItem } from "@/components/charts/barChart";
import { useQuery } from "@tanstack/react-query";
import { fetchRestaurantTypes } from "@/api/ingredients";
import { useIngredient } from "@/contexts/IngredientContext";
import { useSession } from "next-auth/react";

enum RestaurantTypes {
  qsr = 'QSR',
  fast_casual = 'Fast Casual',
  fine_dining = 'Fine Dining',
  mid_scale = 'Mid Scale',
  casual_dining = 'Casual Dining',
  other = 'Other'
}

type RestaurantTypeKeys = keyof typeof RestaurantTypes;

interface IngredientRestaurantTypeResponse {
  id: number,
  restaurant_type: string,
  pen_rate: number,
  quarter: number,
  year: number
}

// TODO: get actual data from API
// const chartData1: BarChartItem[] = [
//   {
//     id: 1,
//     label: "QSR",
//     color: "#A2D161",
//     value: 45,
//     symbol: "%",
//   },
//   { id: 2, label: "Fast casual", color: "#D2D0BC", value: 28, symbol: "%" },
//   { id: 3, label: "Fine dining", color: "#D2D0BC", value: 13, symbol: "%" },
//   {
//     id: 4,
//     label: "Casual dining",
//     color: "#D2D0BC",
//     value: 11,
//     symbol: "%",
//   },
//   { id: 5, label: "Family style", color: "#D2D0BC", value: 9, symbol: "%" },
// ];

const TopRestaurantTypesDistribution = () => {
  const { data: session } = useSession();


  const {  guid } = useIngredient();

  const { data: ingredient_restaurant_types } = useQuery({
    queryFn: () => {
      return fetchRestaurantTypes({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["ingredient_restaurant_types", guid],
    enabled: !!session?.user?.authorization,
  });

  const [chartData, setChartData] = useState([]);
  const [maxValue, setMaxValue] = useState<number>(100);

  useEffect(()=>{
    const chartData1 = ingredient_restaurant_types?.map((item: IngredientRestaurantTypeResponse, idx: number) : BarChartItem => {
      return   {
        id: item.id,
        label: RestaurantTypes[item.restaurant_type as RestaurantTypeKeys],
        color: idx == 0 ? "#A2D161" : "#D2D0BC",
        value: item.pen_rate * 100,
        symbol: "%",
      }
    }) || []
    setChartData(chartData1)
    setMaxValue(chartData1[0]?.value ? Math.ceil(chartData1[0].value * 1.15) : 100)
  }, [ingredient_restaurant_types, guid])



  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={5}>Top Restaurant Types Distribution</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <div className="border-muted-100 rounded-lg border p-4 pt-0">
          <BarChart
            dataItems={chartData}
            height={260}
            symbol=""
            maxValue={maxValue}
          />
        </div>
      </div>
    </Card>
  );
};

export default TopRestaurantTypesDistribution;
