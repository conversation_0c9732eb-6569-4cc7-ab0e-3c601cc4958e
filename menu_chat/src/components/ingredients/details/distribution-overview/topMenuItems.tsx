import { useEffect, useState } from "react";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconGraph, IconIncrease, IconDecrease } from "@/components/icons";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import titleCase from "voca/title_case";
import { useIngredient } from "@/contexts/IngredientContext";
import { fetchMenuItems } from "@/api/ingredients";

interface MenuItemsResponse {
  id: number,
  name: string,
  distribution: number,
  growth_rate: number,
}



const generateRateTagWithIcon = (rate: number) => {
  if(rate > 0){
    return <Tag variant="greenOutline">
      <IconIncrease size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  }
  else if (rate == 0){
    return <Tag variant="blueOutline">
      <IconGraph size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  } else {
    return <Tag variant="redOutline">
      <IconDecrease size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  }
}

const TopMenuItems = () => {
  const { data: session } = useSession();
  const {  guid } = useIngredient();

  const { data: top_menu_items } = useQuery({
    queryFn: () => {
      return fetchMenuItems({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["top_menu_items", guid],
    enabled: !!session?.user?.authorization,
  });

  const [menuItemsMap, setMenuItems] = useState([]);

  useEffect(()=>{
    const rowsMap = top_menu_items?.map((item: MenuItemsResponse)=>{
      return (<Tr key={item.id}>
        <Td>
          <Tag>{titleCase(item.name)}</Tag>
        </Td>

        <Td>
          {(item.distribution).toFixed(2)}%
        </Td>
        <Td>{""}</Td>
        <Td>
          {generateRateTagWithIcon(item.growth_rate)}
        </Td>
      </Tr>)
    }) || []
    setMenuItems(rowsMap)
  }, [top_menu_items, guid])

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={5}>Top Menu Items</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Penetration</Th>
            <Th>Median Price</Th>
            <Th>Change</Th>
          </Tr>
        </Thead>
        <Tbody>
          {menuItemsMap}
          {/*<Tr>
            <Td>
              <Tag>Spicy Garlic Noodles</Tag>
            </Td>
            <Td>56.35%</Td>
            <Td>$10,29</Td>
            <Td>
              <Tag variant="greenOutline">
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Beef Pho</Tag>
            </Td>
            <Td>2.52%</Td>
            <Td>$28,49</Td>
            <Td>
              <Tag variant="blueOutline">
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Teriyaki Chicken Ric...</Tag>
            </Td>
            <Td>2.52%</Td>
            <Td>$19,69</Td>
            <Td>
              <Tag variant="redOutline">
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Vegetable Ramen</Tag>
            </Td>
            <Td>56.35%</Td>
            <Td>$12,45</Td>
            <Td>
              <Tag variant="blueOutline">
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Szechuan Stir-Fry</Tag>
            </Td>
            <Td>2.52%</Td>
            <Td>$17,28</Td>
            <Td>
              <Tag variant="redOutline">
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>*/}
        </Tbody>
      </Table>
    </Card>
  );
};

export default TopMenuItems;
