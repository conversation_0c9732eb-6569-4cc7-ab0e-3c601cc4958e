import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Donut<PERSON>hart from "@/components/charts/donutChart";

const chartData = [
  {
    label: "Chinese",
    value: 23,
    color: "#FF6D56",
  },
  { label: "Japanese", value: 28, color: "#FFBE05" },
  { label: "Thai", value: 22, color: "#8BC539" },
  { label: "French", value: 15, color: "#D273D8" },
  { label: "Mexican", value: 12, color: "#2A9DCB" },
];

interface PieChartData {
  label: string;
  value: number;
  color: string;
}

const MostPopularMealType = () => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={5}>Most Popular Meal Types</Heading>
          <Info
            tooltipId="most-popular-meal-types"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
              Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Link href="/ingredients/most-popular-meal-types">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="p-4">
        <div className="border-muted-100 flex items-center justify-between gap-5 rounded-lg border px-3 py-6">
          <DonutChart data={chartData} size={170} showLabel={false} />
          <div className="flex w-full flex-1 flex-col gap-1 text-xs font-medium">
            {chartData.map((item: PieChartData, index) => (
              <div
                key={index}
                className="border-muted-500/30 flex items-center justify-between border-b px-1 py-1.5"
              >
                <span className="flex items-center gap-1">
                  <span
                    className="h-3 w-1 rounded-full"
                    style={{ backgroundColor: item.color }}
                  ></span>
                  {item.label}
                </span>
                <span className="text-neutral-600">{item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MostPopularMealType;
