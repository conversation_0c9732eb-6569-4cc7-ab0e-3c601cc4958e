import { useState, useEffect } from "react";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Donut<PERSON>hart from "@/components/charts/donutChart";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";
import { useQuery } from "@tanstack/react-query";
import { fetchCuisineTypes } from "@/api/ingredients";
import titleCase from "voca/title_case";
import ColorGenerator from "@/components/utils/color-generator";

/*const chartData = [
  {
    label: "Chinese",
    value: 23,
    color: "#FF6D56",
  },
  { label: "Japanese", value: 28, color: "#FFBE05" },
  { label: "Thai", value: 22, color: "#8BC539" },
  { label: "French", value: 15, color: "#D273D8" },
  { label: "Mexican", value: 12, color: "#2A9DCB" },
];*/

interface PieChartData {
  label: string;
  value: number;
  color: string;
}

interface CuisineTypeResponse {
  "id": number,
  "ingredient_id": number,
  "cuisine_type": string,
  "items_count": number,
  "total_items_count": number,
  "percent": number,
  "quarter": number,
  "year": number
}

const MostPopularMealType = () => {
  const { data: session } = useSession();

  const {  guid } = useIngredient();

  const { data: cuisine_types } = useQuery({
    queryFn: () => {
      return fetchCuisineTypes({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["cuisine_types", guid],
    enabled: !!session?.user?.authorization,
  });

  const [chartData, setChartData] = useState<PieChartData[]>([]);

  useEffect(()=>{
    const colorGen = new ColorGenerator(guid)
    setChartData(cuisine_types?.map((item: CuisineTypeResponse)=>{
      return { label: titleCase(item.cuisine_type), value: item.percent, color: colorGen.getColor(item.cuisine_type) }
    }) || []);
  }, [cuisine_types, setChartData, guid])


  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={5}>Most Popular Meal Types</Heading>
          <Info
            tooltipId="most-popular-meal-types"
            content="Most Popular Meal Types for an Ingredient"
          />
        </div>
        <Link href="/ingredients/most-popular-meal-types">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="p-4">
        <div className="border-muted-100 flex items-center justify-between gap-5 rounded-lg border px-3 py-6">
          <DonutChart data={chartData} size={170} showLabel={false} />
          <div className="flex w-full flex-1 flex-col gap-1 text-xs font-medium">
            {chartData.map((item: PieChartData, index) => (
              <div
                key={index}
                className="border-muted-500/30 flex items-center justify-between border-b px-1 py-1.5"
              >
                <span className="flex items-center gap-1">
                  <span
                    className="h-3 w-1 rounded-full"
                    style={{ backgroundColor: item.color }}
                  ></span>
                  {item.label}
                </span>
                <span className="text-neutral-600">{item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MostPopularMealType;
