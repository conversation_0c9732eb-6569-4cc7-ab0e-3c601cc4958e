import TopMenuItems from "./topMenuItems";
import TopRestaurantTypesDistribution from "./topRestaurantTypesDistribution";
import GeographicPopularity from "./geographicPopularity";
import MostPopularMealType from "./mostPopularMealType";
import ChainTypesDistribution from "./chainTypesDistribution";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const IngredientsDistributionOverview = () => {
  return (
    <section className="mt-[60px] py-4">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-2 items-start gap-5">
          <TopMenuItems />
          <TopRestaurantTypesDistribution />
        </div>
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-7">
            <GeographicPopularity />
          </div>
          <div className="col-span-5">
            <div className="flex flex-col gap-5">
              <MostPopularMealType />
              <ChainTypesDistribution />
            </div>
          </div>
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default IngredientsDistributionOverview;
