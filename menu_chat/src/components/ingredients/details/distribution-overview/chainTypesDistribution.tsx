import { useEffect, useState } from "react";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconGraph, IconIncrease, IconDecrease } from "@/components/icons";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchChainTypes } from "@/api/ingredients";
import { useIngredient } from "@/contexts/IngredientContext";

interface ChainTypesResponse {
  id: number,
  chain_type: string,
  menu_item_id: number,
  pen_rate: number,
  restaurant_count: number,
  growth_rate: number,
  quarter: number,
  year: number
}

enum ChainTypes {
  independents = 'Independents',
  small_chains = 'Small Chains',
  large_chains = 'Large Chains',
}

type ChainTypesKeys = keyof typeof ChainTypes;

const ChainTypesDistribution = () => {
  const { data: session } = useSession();

  const {  guid } = useIngredient();

  const { data: ingredient_chain_types } = useQuery({
    queryFn: () => {
      return fetchChainTypes({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["ingredient_chain_types", guid],
    enabled: !!session?.user?.authorization,
  });


  const [chainTypesMap, setChainTypesMap] = useState([]);

  useEffect(()=>{
    const rowsMap = ingredient_chain_types?.map((item: ChainTypesResponse)=>{
      return (<Tr key={item.id}>
        <Td>{ChainTypes[item.chain_type as ChainTypesKeys]}</Td>
        <Td>{item.pen_rate}%</Td>
        <Td>
          {(()=>{
            if(item.growth_rate > 0){
              return <Tag variant="greenOutline">
                <IconIncrease size={16} />
                {item.growth_rate}%
              </Tag>
            }
            else if (item.growth_rate == 0){
              return <Tag variant="blueOutline">
                <IconGraph size={16} />
                {item.growth_rate}%
              </Tag>
            } else {
              return <Tag variant="redOutline">
                <IconDecrease size={16} />
                {item.growth_rate}%
              </Tag>
            }
          })()}
        </Td>
      </Tr>)
    }) || []
    setChainTypesMap(rowsMap)


  }, [ingredient_chain_types, guid])

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={5}>Chain Types Distribution</Heading>
          <Info
            tooltipId="chain-types-distribution"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
              Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Penetration</Th>
            <Th>Change</Th>
          </Tr>
        </Thead>
        <Tbody>
          {chainTypesMap}

          {/*<Tr>
            <Td>Independent stores</Td>
            <Td>56.35%</Td>
            <Td>
              <Tag variant="greenOutline">
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>Regional chains</Td>
            <Td>17.35%</Td>
            <Td>
              <Tag variant="blueOutline">
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>Large chains</Td>
            <Td>27.35%</Td>
            <Td>
              <Tag variant="redOutline">
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>*/}
        </Tbody>
      </Table>
    </Card>
  );
};

export default ChainTypesDistribution;
