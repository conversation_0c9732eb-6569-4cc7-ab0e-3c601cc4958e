import { useState, useRef } from "react";
import Card from "@/components/base/card";
import Button from "@/components/base/button";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import Bar<PERSON><PERSON>, {
  Bar<PERSON>hartItem,
  BarChartHandle,
} from "@/components/charts/barChart";
import Tabs, { TabContent } from "@/components/base/tabs";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { fetchConsumedByGeneration } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";
import * as XLSX from "xlsx";

const ConsumedBy = () => {
  const { data: session } = useSession();

  const { guid } = useIngredient();
  const [selected] = useState<string>("");

  const chartRefs: Record<
    "generation" | "ethnicity",
    React.RefObject<BarChartHandle | null>
  > = {
    generation: useRef<BarChartHandle | null>(null),
    ethnicity: useRef<BarChartHandle | null>(null),
  };

  const [tabIndex, setTabIndex] = useState(0);

  const handleSelect = (format: string) => {
    const chartRef =
      tabIndex === 0
        ? chartRefs.generation.current
        : chartRefs.ethnicity.current;

    if (!chartRef) return;

    const chart = chartRef.getChart?.();
    const data = chartRef.getChartData?.();
    if (!chart) return;

    if (format === "png" || format === "jpg") {
      const mime = format === "jpg" ? "image/jpeg" : "image/png";
      const url = chart.toBase64Image(mime);
      const a = document.createElement("a");
      a.href = url;
      a.download = `chart.${format}`;
      a.click();
    }

    if (format === "csv" || format === "xls") {
      const userDataset = data.datasets[1]; // головні дані
      const rows = data.labels.map((label: string, i: number) => [
        label,
        userDataset.data[i],
      ]);
      rows.unshift(["Label", "Value"]);

      if (format === "csv") {
        const csv = rows.map((r) => r.join(",")).join("\n");
        const blob = new Blob([csv], { type: "text/csv" });
        const a = document.createElement("a");
        a.href = URL.createObjectURL(blob);
        a.download = "chart.csv";
        a.click();
      }

      if (format === "xls") {
        const ws = XLSX.utils.aoa_to_sheet(rows);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Chart");
        XLSX.writeFile(wb, "chart.xlsx");
      }
    }
  };

  const { data: consumed_by_generation } = useQuery({
    queryFn: () => {
      return fetchConsumedByGeneration({
        auth: session?.user.authorization as string,
        guid,
      });
    },
    queryKey: ["consumed_by_generation", guid],
    enabled: !!session?.user?.authorization,
  });

  const ageChartData: BarChartItem[] = Object.keys(
    consumed_by_generation?.age_percentages || {},
  ).map((key, index) => ({
    id: index + 1,
    label: key,
    color:
      consumed_by_generation.age_percentages[key] ===
      Math.max(
        ...(Object.values(
          consumed_by_generation?.age_percentages || {},
        ) as number[]),
      )
        ? "#A2D161"
        : "#D2D0BC",
    value: consumed_by_generation.age_percentages[key],
    symbol: "%",
  }));

  const ethnicityChartData: BarChartItem[] = Object.keys(
    consumed_by_generation?.ethnicity_percentages || {},
  ).map((key, index) => ({
    id: index + 1,
    label: key,
    color:
      consumed_by_generation.ethnicity_percentages[key] ===
      Math.max(
        ...(Object.values(
          consumed_by_generation?.ethnicity_percentages || {},
        ) as number[]),
      )
        ? "#A2D161"
        : "#D2D0BC",
    value: consumed_by_generation.ethnicity_percentages[key],
    symbol: "%",
  }));

  return (
    <Card className="p-4">
      <div className="relative flex flex-wrap items-center justify-between gap-2">
        <Tabs
          navigation={["Consumed by Generation", "Consumed by Ethnicity"]}
          onChange={setTabIndex}
        >
          <TabContent>
            <div className="border-muted-100 rounded-lg border px-4 py-2">
              <BarChart
                ref={chartRefs.generation}
                dataItems={ageChartData}
                symbol="%"
                maxValue={100}
                height={320}
              />
            </div>
          </TabContent>
          <TabContent>
            <div className="border-muted-100 rounded-lg border px-4 py-2">
              <BarChart
                ref={chartRefs.ethnicity}
                dataItems={ethnicityChartData}
                symbol="%"
                maxValue={100}
                height={320}
              />
            </div>
          </TabContent>
        </Tabs>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
          className="absolute top-0 right-0"
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
    </Card>
  );
};

export default ConsumedBy;
