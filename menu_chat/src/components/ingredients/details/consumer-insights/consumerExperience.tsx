import { useState } from "react";
import titleCase from "voca/title_case";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import Info from "@/components/base/info";
import { IconMore } from "@/components/icons";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import Paragraph from "@/components/base/paragraph";
import Donut<PERSON>hart from "@/components/charts/donutChart";
import { fetchConsumerExperience } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";

const ConsumerExperience = () => {
  const { data: session } = useSession();

  const { guid, name } = useIngredient();
  const [selected, setSelected] = useState<string>("");

  const { data } = useQuery({
      queryFn: () => {
        return fetchConsumerExperience({ auth: session?.user.authorization as string, guid });
      },
      queryKey: ["consumer_experience", guid],
      enabled: !!(session?.user?.authorization),
    });

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const consumer_experience = data || {
    consumer_who_likes: {
      count: 0,
      male: 0,
      female: 0
    },
    consumer_who_neutral: {
      count: 0,
      male: 0,
      female: 0
    },
    consumer_who_dislikes: {
      count: 0,
      male: 0,
      female: 0
    },
    total: 0
  };

  return (
    <Card className="p-1">
      <div className="mb-4 flex flex-wrap items-center justify-between gap-2 px-4 pt-3">
        <div className="flex items-center">
          <Heading level={5}>Consumer Experience</Heading>
          <Info
            tooltipId="geographic-popularity"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Dropdown
          options={MENU_ADOPTION_CURVE_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
      <div className="border-muted-100 flex flex-col gap-4 border-t p-4">
        <div className="border-muted-100 flex gap-4 rounded-lg border p-4">
          <DonutChart data={[{ value: parseFloat((consumer_experience.consumer_who_likes.count / consumer_experience.total * 100).toFixed(2)), color: "#648E29" }]} size={100} />
          <div className="flex-1">
            <Heading level={3}>{consumer_experience.consumer_who_likes?.count.toFixed(0)}</Heading>
            <Paragraph className="mb-8 text-xs font-medium text-neutral-600">
              /{consumer_experience.total}
            </Paragraph>
            <Paragraph className="text-xs font-medium text-neutral-600">
              Consumers who like {titleCase(name)}
            </Paragraph>
          </div>
          <div className="flex flex-1 flex-col items-start gap-1">
            <div className="text-green-1000 mb-5 flex items-center gap-1 self-end rounded-sm bg-green-100 px-1 py-0.5 text-xs font-medium">
              <span className="block size-3 rounded-xs bg-green-700"></span>
              Strongly like
            </div>
            <div className="flex w-full items-center justify-between px-1 py-0.5">
              <Heading
                level={6}
                className="text-[11px] font-semibold text-neutral-600 uppercase"
              >
                Male
              </Heading>
              <Paragraph className="text-sm font-medium text-neutral-700">
                {(consumer_experience.consumer_who_likes.male / (consumer_experience.consumer_who_likes.male + consumer_experience.consumer_who_likes.female) * 100).toFixed(2)}%
              </Paragraph>
            </div>
            <div className="flex w-full items-center justify-between px-1 py-0.5">
              <Heading
                level={6}
                className="text-[11px] font-semibold text-neutral-600 uppercase"
              >
                Female
              </Heading>
              <Paragraph className="text-sm font-medium text-neutral-700">
                {(consumer_experience.consumer_who_likes.female / (consumer_experience.consumer_who_likes.male + consumer_experience.consumer_who_likes.female) * 100).toFixed(2)}%
              </Paragraph>
            </div>
          </div>
        </div>
        <div className="border-muted-100 flex gap-4 rounded-lg border p-4">
          <DonutChart
            data={[{ value: parseFloat((consumer_experience.consumer_who_neutral.count / consumer_experience.total * 100).toFixed(2)), color: "#ffc857" }]}
            size={100}
            pattern
          />
          <div className="flex-1">
            <Heading level={3}>{consumer_experience.consumer_who_neutral?.count.toFixed(0)}</Heading>
            <Paragraph className="mb-8 text-xs font-medium text-neutral-600">
              /{consumer_experience.total}
            </Paragraph>
            <Paragraph className="text-xs font-medium text-neutral-600">
            Consumers who like familiar with {titleCase(name)}
            </Paragraph>
          </div>
          <div className="flex flex-1 flex-col items-start gap-1">
            <div className="text-yellow-1000 mb-5 flex items-center gap-1 self-end rounded-sm bg-yellow-100 px-1 py-0.5 text-xs font-medium">
              <span className="block size-3 rounded-xs bg-yellow-500"></span>
              Tried once or twice
            </div>
            <div className="flex w-full items-center justify-between px-1 py-0.5">
              <Heading
                level={6}
                className="text-[11px] font-semibold text-neutral-600 uppercase"
              >
                Male
              </Heading>
              <Paragraph className="text-sm font-medium text-neutral-700">
                {(consumer_experience.consumer_who_neutral.male / (consumer_experience.consumer_who_neutral.male + consumer_experience.consumer_who_neutral.female) * 100).toFixed(2)}%
              </Paragraph>
            </div>
            <div className="flex w-full items-center justify-between px-1 py-0.5">
              <Heading
                level={6}
                className="text-[11px] font-semibold text-neutral-600 uppercase"
              >
                Female
              </Heading>
              <Paragraph className="text-sm font-medium text-neutral-700">
                {(consumer_experience.consumer_who_neutral.female / (consumer_experience.consumer_who_neutral.male + consumer_experience.consumer_who_neutral.female) * 100).toFixed(2)}%
              </Paragraph>
            </div>
          </div>
        </div>
        <div className="border-muted-100 flex gap-4 rounded-lg border p-4">
          <DonutChart
            data={[{ value: parseFloat((consumer_experience.consumer_who_dislikes.count / consumer_experience.total * 100).toFixed(2)), color: "#ff6d56" }]}
            size={100}
            pattern
          />
          <div className="flex-1">
            <Heading level={3}>{consumer_experience.consumer_who_dislikes?.count.toFixed(0)}</Heading>
            <Paragraph className="mb-8 text-xs font-medium text-neutral-600">
              /{consumer_experience.total}
            </Paragraph>
            <Paragraph className="text-xs font-medium text-neutral-600">
              Consumers who dislike {titleCase(name)}
            </Paragraph>
          </div>
          <div className="flex flex-1 flex-col items-start gap-1">
            <div className="text-green-1000 mb-5 flex items-center gap-1 self-end rounded-sm bg-green-100 px-1 py-0.5 text-xs font-medium">
              <span className="block size-3 rounded-xs bg-red-500"></span>
              Strongly Dislike
            </div>
            <div className="flex w-full items-center justify-between px-1 py-0.5">
              <Heading
                level={6}
                className="text-[11px] font-semibold text-neutral-600 uppercase"
              >
                Male
              </Heading>
              <Paragraph className="text-sm font-medium text-neutral-700">
                {(consumer_experience.consumer_who_dislikes.male / (consumer_experience.consumer_who_dislikes.male + consumer_experience.consumer_who_dislikes.female) * 100).toFixed(2)}%
              </Paragraph>
            </div>
            <div className="flex w-full items-center justify-between px-1 py-0.5">
              <Heading
                level={6}
                className="text-[11px] font-semibold text-neutral-600 uppercase"
              >
                Female
              </Heading>
              <Paragraph className="text-sm font-medium text-neutral-700">
                {(consumer_experience.consumer_who_dislikes.female / (consumer_experience.consumer_who_dislikes.male + consumer_experience.consumer_who_dislikes.female) * 100).toFixed(2)}%
              </Paragraph>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ConsumerExperience;
