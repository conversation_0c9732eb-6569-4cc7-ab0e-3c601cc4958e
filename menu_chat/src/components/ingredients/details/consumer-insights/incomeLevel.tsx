import { useState } from "react";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import SingleBar from "@/components/charts/singleBar";

const IncomeLevel = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className="p-4">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center">
          <Heading level={5}>
            Income Level <span className="text-neutral-600">(yearly)</span>
          </Heading>
          <Info
            tooltipId="geographic-popularity"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Dropdown
          options={MENU_ADOPTION_CURVE_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
      <div className="grid grid-cols-4 gap-4 text-center text-neutral-700">
        <div>
          <Heading level={3}>23,12%</Heading>
          <SingleBar
            value={30}
            maxValue={100}
            pattern="diagonal"
            className="my-2"
          />
          <Paragraph size="xs" className="font-medium text-neutral-600">
            &lt; $40,000
          </Paragraph>
        </div>
        <div>
          <Heading level={3}>87,12%</Heading>
          <SingleBar
            value={50}
            maxValue={100}
            color="#A2D161"
            pattern="diagonal"
            className="my-2"
          />
          <Paragraph size="xs" className="font-medium text-neutral-600">
            $40,000 - $80,000
          </Paragraph>
        </div>
        <div>
          <Heading level={3}>44,7%</Heading>
          <SingleBar
            value={20}
            maxValue={100}
            pattern="diagonal"
            className="my-2"
          />
          <Paragraph size="xs" className="font-medium text-neutral-600">
            $80,000 - $100,000
          </Paragraph>
        </div>
        <div>
          <Heading level={3}>52,8%</Heading>
          <SingleBar
            value={40}
            maxValue={100}
            pattern="diagonal"
            className="my-2"
          />
          <Paragraph size="xs" className="font-medium text-neutral-600">
            &gt; $100,000
          </Paragraph>
        </div>
      </div>
    </Card>
  );
};

export default IncomeLevel;
