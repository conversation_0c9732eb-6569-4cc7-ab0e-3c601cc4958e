import { useState } from "react";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import SentimentBar from "@/components/charts/sentimentBar";
import { fetchSocialMediaSentiments } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";

const SocialSentiments = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const { data: session } = useSession();

  const { guid } = useIngredient();

  const { data: social_sentiments } = useQuery({
    queryFn: () => {
      return fetchSocialMediaSentiments({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["social_sentiments", guid],
    enabled: !!session?.user?.authorization,
  });

  const segments: Record<string, number> = {};

  [
    "strongly-dislike",
    "somewhat-dislike",
    "neutral",
    "somewhat-like",
    "strongly-like",
  ].map((sentiment, idx) => {
    segments[sentiment] = parseFloat(
      (social_sentiments?.percentages?.[idx] || 0).toFixed(2),
    );
  });

  return (
    <Card className="p-4">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center">
          <Heading level={5}>Social Sentiments</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
      <SentimentBar height={36} showLabel={true} segments={segments} />
      <div className="mt-1 flex items-center justify-between">
        <Paragraph size="xs" className="font-medium text-neutral-600">
          Negative
        </Paragraph>
        <Paragraph size="xs" className="font-medium text-neutral-600">
          in %
        </Paragraph>
        <Paragraph size="xs" className="font-medium text-neutral-600">
          Positive
        </Paragraph>
      </div>
    </Card>
  );
};

export default SocialSentiments;
