import { useState } from "react";
import titleCase from 'voca/title_case';
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import dynamic from "next/dynamic";
import { fetchMentionsByState } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";
const MapChart = dynamic(() => import("@/components/charts/mapChart"), {
  ssr: false,
});

const GeographicPopularity = () => {
  const { data: session } = useSession();

  const { guid, name } = useIngredient();
  const [selected, setSelected] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  interface StateMentions {
    id: string;
    mentions: number;
  }

  const { data: mentions_by_state } = useQuery<StateMentions[]>({
    queryFn: async () => {
      setLoading(true);
      const data = await fetchMentionsByState({ auth: session?.user.authorization as string, guid });
      setLoading(false);
      return data;
    },
    queryKey: ["mentions_by_state", guid],
    enabled: !!(session?.user?.authorization),
  });

  return (
    <Card className="p-4">
      <div className="flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center">
          <Heading level={5}>Geographic Popularity</Heading>
          <Info
            tooltipId="geographic-popularity"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Dropdown
          options={MENU_ADOPTION_CURVE_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
      <div className="flex w-full items-center justify-center overflow-x-auto h-75">
        <MapChart isExternalLoading={loading} title={titleCase(name)} data={(mentions_by_state || []).map((state) => ({ id: state.id.toString(), value: state.mentions || 0 }))} />
      </div>
    </Card>
  );
};

export default GeographicPopularity;
