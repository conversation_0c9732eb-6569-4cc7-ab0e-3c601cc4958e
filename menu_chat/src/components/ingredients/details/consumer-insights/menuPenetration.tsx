import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Tag from "@/components/base/tag";
import { IconIncrease } from "@/components/icons";
import { useIngredient } from "@/contexts/IngredientContext";

const MenuPenetration = () => {
  const { pen_rate, delta_last_period } = useIngredient();

  return (
    <Card className="p-4">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <Heading level={5}>Menu Penetration</Heading>
      </div>
      <div className="flex items-end justify-between">
        <Heading level={2} className="flex items-end gap-1.5">
          <span className="text-3xl">+</span>{(pen_rate ?? 0).toFixed(2)}%
        </Heading>
        <Tag variant="greenOutline" className="px-1">
          <IconIncrease size={16} /> {(delta_last_period ?? 0).toFixed(2)} %
        </Tag>
      </div>
    </Card>
  );
};

export default MenuPenetration;
