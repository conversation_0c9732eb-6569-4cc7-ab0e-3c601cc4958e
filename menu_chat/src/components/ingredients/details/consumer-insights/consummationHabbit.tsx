import { useState } from "react";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import SentimentBar from "@/components/charts/sentimentBar";
import { fetchConsummationHabbits } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useIngredient } from "@/contexts/IngredientContext";

const ConsummationHabbit = () => {
  const { data: session } = useSession();

  const { guid } = useIngredient();

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const { data: consummation_habbits }  = useQuery({
    queryFn: () => {
      return fetchConsummationHabbits({auth: session?.user.authorization as string, guid: guid})
    },
    queryKey: ["consummation_habbits", guid],
    enabled: !!(session?.user?.authorization),
  });

  return (
    <Card className="p-4">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center">
          <Heading level={5}>Consummation Habbit</Heading>
        </div>
        <Dropdown
          options={MENU_ADOPTION_CURVE_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
      <SentimentBar
        height={36}
        showLabel={true}
        pattern="vertical"
        segments={
          [
            {
              label: "Never heard of it",
              color: "#FF9985",
            },
            {
              label: "Heard of it but never tried",
              color: "#FBC088"
            },
            {
              label: "Tried once or twice",
              color: "#FFDE85"
            },
            {
              label: "Eat occasionally",
              color: "#C7E3A0"
            },
            {
              label: "Eat regularly",
              color: "#8BC539"
            }
          ].map((segment, idx) => {
            return {
              ...segment,
              value: consummation_habbits?.percentages[idx] || 0,
            };
          })
        }
      />
      <div className="mt-1 flex items-center justify-between">
        <Paragraph size="xs" className="font-medium text-neutral-600">
          Never heard of it
        </Paragraph>
        <Paragraph size="xs" className="font-medium text-neutral-600">
          in %
        </Paragraph>
        <Paragraph size="xs" className="font-medium text-neutral-600">
          Eat Regularly
        </Paragraph>
      </div>
    </Card>
  );
};

export default ConsummationHabbit;
