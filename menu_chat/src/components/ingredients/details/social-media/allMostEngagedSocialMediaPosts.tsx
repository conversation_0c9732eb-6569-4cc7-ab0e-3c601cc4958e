import DataSourcesReports from "@/components/shared/dataSourcesReports";
import SocialMediaCard from "./SocialMediaCard";
import Link from "next/link";
import { fetchAllSocialMediaPosts } from "@/api/ingredients";
import { useIngredient } from "@/contexts/IngredientContext";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

interface SocialMediaPost {
  category: string;
  site: string;
  username: string;
  total_engagements: number;
  url: string;
  image?: string;
}

interface AllMostEngagedSocialMediaPostsProps {
  filters: string[];
}

const AllMostEngagedSocialMediaPosts = ({ filters }: AllMostEngagedSocialMediaPostsProps) => {
  const ingredient = useIngredient();
  const guid = ingredient.guid;
  const { data: session } = useSession();

  const { data } = useQuery<SocialMediaPost[]>({
    queryFn: () =>
      fetchAllSocialMediaPosts({
        auth: session?.user.authorization as string,
        guid,
      }),
    queryKey: ["social_media_posts", guid],
    enabled: !!session?.user?.authorization,
  });

  return (
    <section className="pt-4 pb-24">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-4 gap-5 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {data?.filter((row) => filters.length === 0 || filters.includes(row.category)).map((post, index) => (
            <Link href={post.url} target="_blank" key={index}>
              <SocialMediaCard
                imageAlt={post.username}
                imageSrc={post.image || "/assets/images/<EMAIL>"}
                platform="instagram"
                username={post.username}
                engagementValue={post.total_engagements?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0}
              />
            </Link>
          ))}
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default AllMostEngagedSocialMediaPosts;
