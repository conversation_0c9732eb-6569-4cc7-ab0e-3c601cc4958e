import { useState } from "react";
import Card, { CardHeader } from "@/components/base/card";
import titleCase from 'voca/title_case';
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore, IconGraph, IconIncrease, IconDecrease, } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";
import { fetchSocialMediaConversations } from "@/api/ingredients";
import { useIngredient } from "@/contexts/IngredientContext";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

const SocialConversations = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const { data: session } = useSession();
  const { guid, name } = useIngredient();

  const { data } = useQuery({
    queryFn: () => {
      return fetchSocialMediaConversations({
        auth: session?.user.authorization as string,
        guid
      });
    },
    queryKey: ["social_media_conversations", guid],
    enabled: !!session?.user?.authorization,
  });

  const lineChartData: LineSeriesItem[] = [
  {
    id: 1,
    label: titleCase(name),
    color: "#FF6D56",
    data: Object.values(data?.timeline || {}),
  },
];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Social Conversations</Heading>
          <Info
            tooltipId="social-conversations-tooltip"
            content="Social media mentions in comments or posts during the selected period"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="flex flex-col gap-4 p-4">
        <div className="border-muted-100 flex items-center justify-between gap-2 rounded-sm border p-3">
          <div>
            <Heading level={5}>Compared</Heading>
            <Paragraph size="xs" className="font-medium text-neutral-600">
              to Previous Period
            </Paragraph>
          </div>
          <div className="flex items-end gap-2">
            <Heading level={2} className="flex items-end gap-1.5">
              {(data?.growth || 0.0) > 0.0 && (
                <span className="text-4">+</span>
              )}
              {(data?.growth || 0.0).toFixed(2)}%
            </Heading>
            <Tag variant="greenOutline" className="px-1">
              {
                (data?.growth || 0.0) > 0.0 ? <IconIncrease size={16} /> : (
                  (data?.growth || 0.0) < 0.0 ? <IconDecrease size={16} /> : <IconGraph size={16} />
                )
              }
            </Tag>
          </div>
        </div>
        <div className="border-muted-100 rounded-lg border p-3">
          <LineChart
            dataItems={lineChartData}
            labels={Object.keys(data?.timeline || {}).map((date) => new Date(date).toLocaleDateString('en-GB', { day: 'numeric', month: 'long' }))}
            showLegend={false}
            symbol=""
            height={180}
          />
        </div>
      </div>
    </Card>
  );
};

export default SocialConversations;
