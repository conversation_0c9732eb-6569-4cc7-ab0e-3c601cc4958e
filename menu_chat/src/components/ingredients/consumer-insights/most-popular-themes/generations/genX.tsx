import BarChart from "@/components/charts/barChart";

const COLORS = ['#78C5E3', '#FAAB61', '#DF9DE4', '#FF9985', '#BDA4CB'];

type DataProps = {
  data: Record<string, unknown>;
};

const GenX = ({ data = {} }: DataProps) => {
  const chartData = Object.keys(data).map((key, index) => ({
    id: index + 1,
    label: key,
    color: COLORS[index % COLORS.length],
    value: data[key] as number,
    symbol: "%",
  }));

  return (
    <div className="border-muted-200 rounded-lg border p-4">
      <BarChart dataItems={chartData} symbol="%" maxValue={100} />
    </div>
  );
};

export default GenX;
