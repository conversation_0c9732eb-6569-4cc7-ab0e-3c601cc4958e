import BarChart, { BarChartItem } from "@/components/charts/barChart";

// TODO: get actual data from API
const chartData: BarChartItem[] = [
  {
    id: 1,
    label: "Health & Wellness",
    color: "#78C5E3",
    value: 35.36,
    symbol: "%",
  },
  { id: 2, label: "Global Flavors", color: "#FAAB61", value: 75, symbol: "%" },
  { id: 3, label: "Thai", color: "#DF9DE4", value: 40, symbol: "%" },
  { id: 4, label: "Mexican", color: "#FF9985", value: 10, symbol: "%" },
  { id: 5, label: "Asian", color: "#BDA4CB", value: 85, symbol: "%" },
];

const GenAlpha = () => {
  return (
    <div className="border-muted-200 rounded-lg border p-4">
      <BarChart dataItems={chartData} symbol="%" maxValue={100} />
    </div>
  );
};

export default GenAlpha;
