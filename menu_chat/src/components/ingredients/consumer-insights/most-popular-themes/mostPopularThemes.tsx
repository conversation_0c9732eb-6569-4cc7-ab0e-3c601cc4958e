import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import Tabs, { TabContent } from "@/components/base/tabs";
import { CHART_DROPDOWN } from "@/data/ingredients";
import GenAlpha from "./generations/genAlpha";
import GenZ from "./generations/genZ";
import Millennials from "./generations/millennials";
import GenX from "./generations/genX";
import BabyBoomers from "./generations/babyBoomers";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchPopularThemesByGeneration } from "@/api/ingredients";

interface MostPopularThemesProps {
  className?: string;
}

const MostPopularThemes = ({ className }: MostPopularThemesProps) => {
  const [selected, setSelected] = useState<string>("");
  const { data: session } = useSession();
  const { data: popularThemes = {} } = useQuery({
    queryFn: () => {
      return fetchPopularThemesByGeneration({
        auth: session?.user.authorization as string,
      });
    },
    queryKey: ["popularThemes"],
    enabled: !!session?.user?.authorization,
  });

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular Themes Among Generations</Heading>
          <Info
            tooltipId="most-popular-themes"
            content="Top 5 most talked about ingredients and their sentiments. Data gathered from social media on February 2025."
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <Tabs
          navigation={[
            "Gen Alpha",
            "Gen Z",
            "Millennials",
            "Gen X",
            "Baby Boomers",
          ]}
        >
          <TabContent>
            <GenAlpha data={popularThemes["Gen Alpha"]} />
          </TabContent>
          <TabContent>
            <GenZ data={popularThemes["Gen Z"]} />
          </TabContent>
          <TabContent>
            <Millennials data={popularThemes["Millennials"]} />
          </TabContent>
          <TabContent>
            <GenX data={popularThemes["Gen X"]} />
          </TabContent>
          <TabContent>
            <BabyBoomers data={popularThemes["Baby Boomers"]} />
          </TabContent>
        </Tabs>
      </div>
    </Card>
  );
};

export default MostPopularThemes;
