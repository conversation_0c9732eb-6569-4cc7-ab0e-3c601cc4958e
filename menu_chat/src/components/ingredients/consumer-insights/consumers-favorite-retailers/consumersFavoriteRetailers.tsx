import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMostLikedRetailers } from "@/api/ingredients";

interface ConsumersFavoriteRetailersProps {
  className?: string;
}

const ConsumersFavoriteRetailers = ({
  className,
}: ConsumersFavoriteRetailersProps) => {
  const { data: session } = useSession();
    const { data: mostLikedRetailers = [] }  = useQuery({
        queryFn: () => {
          return fetchMostLikedRetailers({auth: session?.user.authorization as string})
        },
        queryKey: ["most_liked_retailers"],
        enabled: !!session?.user?.authorization,
      });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Consumer’s Favorite Retailers</Heading>
          <Info
            tooltipId="consumers-favorite-retailers"
            content="Based on the survey data collected in February 2025. (130,452 participants)"
          />
        </div>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Liked By</Th>
            <Th># of Products</Th>
          </Tr>
        </Thead>
        <Tbody>
          {mostLikedRetailers.map((retailer: { id: string; retailer_name: string; percent?: number; count: number }) => (
            <Tr key={`retailer-${retailer.id}`}>
              <Td>{retailer.retailer_name}</Td>
              <Td>{(retailer.percent || 0).toFixed(2)}%</Td>
              <Td>{(retailer.count || 0).toLocaleString()}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default ConsumersFavoriteRetailers;
