import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import titleCase from "voca/title_case";
import {
  IconCircleUpright,
  IconCircle,
  IconCircleFilled,
  IconCircleRight,
} from "@/components/icons";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMostLikedIngredients } from "@/api/ingredients";

interface MostLikedProps {
  className?: string;
}

interface MostLikedIngredient {
  guid: number;
  name: string;
  menu_adoption?: "mature" | "growth" | "emergence" | "mainstream";
  penetration?: number;
  liked_by: number;
}

const MostLiked = ({ className }: MostLikedProps) => {
  const { data: session } = useSession();
  const { data: most_liked = {} } = useQuery({
    queryFn: () => {
      return fetchMostLikedIngredients({
        auth: session?.user.authorization as string,
        page: 1,
        per_page: 5,
        sort: "",
        filters: {},
        search: "",
      });
    },
    queryKey: ["most_liked"],
    enabled: !!session?.user?.authorization,
  });

  const menuAdoptionIcon = {
    mature: <IconCircleFilled size={16} />,
    growth: <IconCircleUpright size={16} />,
    emergence: <IconCircle size={16} />,
    mainstream: <IconCircleRight size={16} />,
  };

  const { data = [] } = most_liked || { data: [] };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Liked Ingredients by Consumers</Heading>
          <Info
            tooltipId="most-liked-ingredients"
            content="Top 5 most liked ingredients based on the survey conducted in February 2025. (130,452 participants)"
          />
        </div>
        <Link href="/ingredients/most-liked-ingredients">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Menu Adoption</Th>
            <Th>Penetration</Th>
            <Th>Liked By</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data.map((row: MostLikedIngredient) => (
            <Tr key={row.name}>
              <Td>
                <Link
                  href={`/ingredients/details/${row.guid}/consumer-insights`}
                >
                  <Tag>{titleCase(row.name)}</Tag>
                </Link>
              </Td>
              <Td>
                {row.menu_adoption && (
                  <Tag
                    size="xs"
                    variant="green"
                    className="flex w-fit items-center gap-1"
                  >
                    {menuAdoptionIcon[row.menu_adoption]}
                    {titleCase(row.menu_adoption)}
                  </Tag>
                )}
              </Td>
              <Td>{(row.penetration || 0).toFixed(2)}%</Td>
              <Td>{row.liked_by.toFixed(2)}%</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostLiked;
