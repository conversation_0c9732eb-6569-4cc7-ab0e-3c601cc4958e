import { useState } from "react";
import {
  IconCircleUpright,
  IconCircle,
  IconCircleFilled,
  IconCircleRight,
  IconWorkflow,
} from "@/components/icons";
import titleCase from "voca/title_case";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import Favorite from "@/components/base/favorite";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMostLikedIngredients } from "@/api/ingredients";

interface Ingredient {
  id: string;
  name: string;
  menu_category_name?: string;
  menu_adoption?: "mature" | "growth" | "emergence" | "mainstream";
  social_mentions?: number;
  penetration?: number;
  liked_by?: number;
  known_by?: number;
}

const MostLikedTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  const { data: session } = useSession();
    const { data: most_liked }  = useQuery({
        queryFn: () => {
          return fetchMostLikedIngredients({auth: session?.user.authorization as string, page, per_page: perPage, sort, filters, search });
        },
        queryKey: [`most_liked-${page}-${perPage}`, sort, filters, search],
        enabled: !!session?.user?.authorization,
      });

    const { data, total_count } = most_liked || { data: [], total_count: 0 };

    const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Name</Th>
              <Th>Category</Th>
              <Th>Menu Adoption</Th>
              <Th>Penetration</Th>
              <Th>Social Mentions</Th>
              <Th>Liked By</Th>
              <Th>Known By</Th>
            </Tr>
          </Thead>
          <Tbody>
            {
              data?.map((ingredient: Ingredient, key: number) => (
                <Tr key={`ingredient.id-${key}`}>
                  <Td>
                    <Favorite />
                  </Td>
                  <Td>
                    <Tag>{titleCase(ingredient.name)}</Tag>
                  </Td>
                  <Td>
                    <Tag variant="white">
                      <IconWorkflow size={16} className="text-neutral-500" />
                      {titleCase(ingredient.menu_category_name) || "Unknown"}
                    </Tag>
                  </Td>
                  <Td>
                    {ingredient.menu_adoption ? (
                      <Tag variant={ingredient.menu_adoption === "mature" ? "blue" : ingredient.menu_adoption === "growth" ? "green" : ingredient.menu_adoption === "emergence" ? "yellow" : "violet"}>
                        {ingredient.menu_adoption === "mature" && <IconCircleFilled size={16} />}
                        {ingredient.menu_adoption === "growth" && <IconCircleUpright size={16} />}
                        {ingredient.menu_adoption === "emergence" && <IconCircle size={16} />}
                        {ingredient.menu_adoption === "mainstream" && <IconCircleRight size={16} />}
                        {ingredient.menu_adoption.charAt(0).toUpperCase() + ingredient.menu_adoption.slice(1)}
                      </Tag>
                    ) : (
                      "-"
                    )}
                  </Td>
                  <Td>{ingredient.penetration ? `${ingredient.penetration.toFixed(2)}%` : "-"}</Td>
                  <Td>{ingredient.social_mentions ? `${ingredient.social_mentions.toFixed(2)}%` : "-"}</Td>
                  <Td>{ingredient.liked_by ? `${ingredient.liked_by.toFixed(2)}%` : "-"}</Td>
                  <Td>{ingredient.known_by ? `${ingredient.known_by}%` : "-"}</Td>
                </Tr>
              ))
            }
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={total_count}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default MostLikedTable;
