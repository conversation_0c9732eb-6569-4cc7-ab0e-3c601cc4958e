import { twMerge } from "tailwind-merge";
import titleCase from "voca/title_case";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import SentimentBar from "@/components/charts/sentimentBar";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMostTalkedAbout } from "@/api/ingredients";

interface MostTalkedAboutProps {
  className?: string;
}

type MostTalkedAboutIngredient = {
  guid: number;
  name: string;
  negative_percentage: number;
  neutral_percentage: number;
  positive_percentage: number;
  pen_rate: number;
};

const MostTalkedAbout = ({ className }: MostTalkedAboutProps) => {
  const { data: session } = useSession();
  const { data: most_talked_about } = useQuery({
    queryFn: () => {
      return fetchMostTalkedAbout({
        auth: session?.user.authorization as string,
        page: 1,
        per_page: 5,
        sort: "",
        filters: {},
        search: "",
      });
    },
    queryKey: ["most_talked_about"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Talked About Ingredients</Heading>
          <Info
            tooltipId="most-talked-about-ingredients"
            content="Top 5 most talked about ingredients and their sentiments. Data gathered from social media on February 2025."
          />
        </div>
        <Link href="/ingredients/most-talked-about-ingredients">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Sentiment</Th>
            <Th>Penetration</Th>
          </Tr>
        </Thead>
        <Tbody>
          {most_talked_about?.data?.map(
            (ingredient: MostTalkedAboutIngredient, key: number) => (
              <Tr key={`item-${ingredient.guid}-${key}`}>
                <Td>
                  <Link
                    href={`/ingredients/details/${ingredient.guid}/consumer-insights`}
                  >
                    <Tag size="xs">{titleCase(ingredient.name)}</Tag>
                  </Link>
                </Td>
                <Td>
                  <SentimentBar
                    className="max-w-[300px]"
                    withPercentage
                    segments={{
                      "strongly-dislike": ingredient.negative_percentage || 0,
                      "somewhat-dislike": ingredient.negative_percentage || 0,
                      neutral: ingredient.neutral_percentage || 0,
                      "somewhat-like": ingredient.positive_percentage || 0,
                      "strongly-like": ingredient.positive_percentage || 0,
                    }}
                  />
                </Td>
                <Td>{(ingredient.pen_rate ?? 0).toFixed(2)}%</Td>
              </Tr>
            ),
          )}
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostTalkedAbout;
