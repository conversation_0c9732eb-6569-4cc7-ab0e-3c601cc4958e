import { useState } from "react";
import {
  IconIncrease,
  IconD<PERSON>rease,
  IconWorkflow,
} from "@/components/icons";
import titleCase from "voca/title_case";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import Favorite from "@/components/base/favorite";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import SentimentBar from "@/components/charts/sentimentBar";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMostTalkedAbout } from "@/api/ingredients";
import { IconCircleUp, IconCircleDown } from "@/components/icons";
interface Ingredient {
  guid: string;
  name: string;
  menu_category_name?: string;
  negative_percentage: number;
  positive_percentage: number;
  pen_rate: number;
  socialMentions: number;
  foodserviceChange: number;
  change_1y: number;
  retail_change: number;
  social_mentions: number;
}

const MostTalkedAboutTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  const { data: session } = useSession();
  const { data: most_talked_about }  = useQuery({
      queryFn: () => {
        return fetchMostTalkedAbout({auth: session?.user.authorization as string, page, per_page: perPage, sort, filters, search });
      },
      queryKey: [`most_talked_about-${page}-${perPage}`, sort, filters, search],
      enabled: !!session?.user?.authorization,
    });

  const {data, total_count } = most_talked_about || { data: [], total_count: 0 };

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const handleColumnSort = (column: string) => {
    if (sort.split('-').slice(0, -1).join('-') === column) {
      const direction = sort.split('-').pop() === "highest" ? "lowest" : "highest";
      setSort(`${column}-${direction}`);
    } else {
      setPage(1);
      setSort(`${column}-highest`);
    }
  };

  const thSortContent = (column: string) => {
    const field = sort.split('-').slice(0, -1).join('-');
    const direction = sort.split('-').pop() || "highest";

    if (field === column) {
      return direction === "highest" ? <IconCircleUp size={16} /> : <IconCircleDown size={16} />;
    }
    return "";
  };

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th onClick={() => handleColumnSort("name")}>Name {thSortContent("name")}</Th>
              <Th onClick={() => handleColumnSort("menu-category-name")}>Category {thSortContent("menu-category-name")}</Th>
              <Th>Sentiment</Th>
              <Th onClick={() => handleColumnSort("penetration")}>Penetration {thSortContent("penetration")}</Th>
              <Th onClick={() => handleColumnSort("social-mentions")}>Social Mentions {thSortContent("social-mentions")}</Th>
              <Th onClick={() => handleColumnSort("foodservice-growth")}>Foodservice Growth {thSortContent("foodservice-growth")}</Th>
              <Th onClick={() => handleColumnSort("retail-change")}>Retail Growth {thSortContent("retail-change")}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {data?.map((ingredient: Ingredient, key: number) => (
              <Tr key={`ingredient.id-${key}`}>
                <Td>
                  <Favorite />
                </Td>
                <Td>
                  <Tag>{titleCase(ingredient.name)}</Tag>
                </Td>
                <Td>
                  <Tag variant="white">
                    <IconWorkflow size={16} className="text-neutral-500" />
                    {titleCase(ingredient.menu_category_name)}
                  </Tag>
                </Td>
                <Td>
                  <SentimentBar
                    segments={{
                      "strongly-dislike": ingredient.negative_percentage,
                      neutral: ingredient.negative_percentage,
                      "strongly-like": ingredient.positive_percentage,
                    }}
                  />
                </Td>
                <Td>{(ingredient.pen_rate || 0).toFixed(2)}%</Td>
                <Td>{ingredient.social_mentions}</Td>
                <Td>
                  <Tag variant="greenOutline">
                    {ingredient.change_1y > 0 ? <IconIncrease size={16} /> : <IconDecrease size={16} />}
                    {ingredient.change_1y.toFixed(2)}%
                  </Tag>
                </Td>
                <Td>
                  <Tag variant="redOutline">
                    {ingredient.retail_change > 0 ? <IconIncrease size={16} /> : <IconDecrease size={16} />}
                    {ingredient.retail_change.toFixed(2)}%
                  </Tag>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={total_count}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default MostTalkedAboutTable;
