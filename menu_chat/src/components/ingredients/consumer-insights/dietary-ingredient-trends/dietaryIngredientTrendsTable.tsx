import { useState } from "react";
import {
  IconGraph,
  IconIncrease,
  IconDecrease,
  IconCircleUpright,
  IconCircle,
  IconCircleFilled,
  IconCircleRight,
  IconWorkflow,
  IconVegetarian,
  IconVegan,
  IconGlutenfree,
  IconHighprotein,
  IconDairyfree,
  IconHalal,
  IconKosher,
  IconOrganic,
  IconLocal,
  IconSustainable,
  IconSuperfood,
} from "@/components/icons";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import Favorite from "@/components/base/favorite";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";

const DietaryIngredientTrendsTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Name</Th>
              <Th>Category</Th>
              <Th>Diet Type</Th>
              <Th>Menu Adoption</Th>
              <Th>Penetration</Th>
              <Th>Foodservice Change</Th>
              <Th>Liked By</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Lettuce</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Desserts
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconVegetarian size={16} />
                  Vegeterian
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} /> Emergence
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>23,75%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>BBQ sauce</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Sweet
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconGlutenfree size={16} />
                  Gluten Free
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>43,75%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Cream</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Dairy
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconVegan size={16} />
                  Vegan
                </Tag>
              </Td>
              <Td>
                <Tag variant="blue">
                  <IconCircleRight size={16} />
                  Mature
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  3,25%
                </Tag>
              </Td>
              <Td>94%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Olive Oil</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Seasonings
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconOrganic size={16} />
                  Organic
                </Tag>
              </Td>
              <Td>
                <Tag variant="green">
                  <IconCircleUpright size={16} />
                  Growth
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  3,25%
                </Tag>
              </Td>
              <Td>94%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Ube</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Sauces
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconSustainable size={16} />
                  Sustainable
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>43,75%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Banana</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Fruit
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconHalal size={16} />
                  Halal
                </Tag>
              </Td>
              <Td>
                <Tag variant="green">
                  <IconCircleUpright size={16} />
                  Growth
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>94%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Acai</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Sweet
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconKosher size={16} />
                  Kosher
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} /> Emergence
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>23,75%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Marshmallow</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Sweet
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconSuperfood size={16} />
                  Superfood
                </Tag>
              </Td>
              <Td>
                <Tag variant="blue">
                  <IconCircleRight size={16} />
                  Mature
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  3,25%
                </Tag>
              </Td>
              <Td>94%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Cranberry</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Berries
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconDairyfree size={16} />
                  Dairy Free
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>43,75%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Ube</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Sauces
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconLocal size={16} />
                  Local
                </Tag>
              </Td>
              <Td>
                <Tag variant="green">
                  <IconCircleUpright size={16} />
                  Growth
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>23,75%</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Almond</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Nuts
                </Tag>
              </Td>
              <Td>
                <Tag variant="grey">
                  <IconHighprotein size={16} />
                  High Protein
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} /> Emergence
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  3,25%
                </Tag>
              </Td>
              <Td>94%</Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default DietaryIngredientTrendsTable;
