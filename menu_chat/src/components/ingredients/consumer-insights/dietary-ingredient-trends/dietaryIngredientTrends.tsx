import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Tag from "@/components/base/tag";
import {
  IconGraph,
  IconIncrease,
  IconDecrease,
  IconVegetarian,
  IconOrganic,
  IconSuperfood,
} from "@/components/icons";

interface DiataryIngredientTrendsProps {
  className?: string;
}

const DiataryIngredientTrends = ({
  className,
}: DiataryIngredientTrendsProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Dietary Ingredient Trends</Heading>
          <Info
            tooltipId="dietary-ingredient-trends"
            content="Most popular ingredients used in specific diet types."
          />
        </div>
        <Link href="/ingredients/dietary-ingredient-trends">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Diet Type</Th>
            <Th>Change</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag>BBQ sauce</Tag>
            </Td>
            <Td>
              <Tag variant="grey">
                <IconVegetarian size={16} />
                Vegeterian
              </Tag>
            </Td>
            <Td>
              <Tag variant="blueOutline">
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Ube</Tag>
            </Td>
            <Td>
              <Tag variant="grey">
                <IconOrganic size={16} />
                Organic
              </Tag>
            </Td>
            <Td>
              <Tag variant="redOutline">
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Cream</Tag>
            </Td>
            <Td>
              <Tag variant="grey">
                <IconSuperfood size={16} />
                Superfood
              </Tag>
            </Td>
            <Td>
              <Tag variant="greenOutline">
                <IconIncrease size={16} />
                3,25%
              </Tag>
            </Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default DiataryIngredientTrends;
