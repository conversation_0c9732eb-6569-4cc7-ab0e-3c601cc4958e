import { useMemo, useState } from "react";
import { IconWorkflow, } from "@/components/icons";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import Favorite from "@/components/base/favorite";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchIngredients } from "@/api/ingredients";
import {
  ColumnDef, ColumnResizeMode, FilterFn,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable
} from "@tanstack/react-table";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import titleCase from "voca/title_case";
import { getVariantForChange, getVariantForMenuAdoption } from "@/components/utils/helpers";
import { useFavoritesContext } from "@/contexts/FavoritesContext";
import Link from "next/link";
import { INGREDIENTS_SORT_TRANSFORM } from "@/data/ingredients";
interface Ingredient {
  guid: string;
  name: string;
  category: string;
  menu_adoption: string;
  pen_rate: number;
  social_mentions: number;
  change: number;
  retail_change: number;
}



const AllIngredientsTable = () => {
  const { data: session } = useSession();

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [pagination, setPagination] = useState({
    pageIndex: page - 1, //initial page index
    pageSize: pageSize, //default page size
  });

  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");

  const [columnSizing, setColumnSizing] = useState({});

  const {favorites, addFavorite, removeFavorite, isFavorite} = useFavoritesContext();


  const { data: ingredients, isLoading }  = useQuery({
    queryFn: () => {
      return fetchIngredients({auth: session?.user.authorization as string, sorting, pagination, search, filters})
    },
    queryKey: ["ingredients", sorting, pagination, search, filters],
    enabled: !!session?.user?.authorization,
  });



  const handleFilterChange = (newFilters: string[]) => {
    setFilters(() => newFilters);
  };

  const handleSortChange = (newSort: keyof typeof INGREDIENTS_SORT_TRANSFORM) => {
    setSorting(() => INGREDIENTS_SORT_TRANSFORM[newSort])
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setPagination({
      pageIndex: newPage,
      pageSize: pageSize,
    })
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPageSize(pageSize);
    setPagination({
      pageIndex: page,
      pageSize: pageSize,
    })
  };

  const data = useMemo(() => {
    if (!ingredients?.data) return [];
    return ingredients.data || [];
  }, [ingredients]);


  const [columnResizeMode] = useState<ColumnResizeMode>('onChange');


  const columns = useMemo<ColumnDef<Ingredient>[]>(
    () => [
      {
        id: 'favorite',
        accessorKey: 'guid',
        header: '',
        cell: (info) => {
          const guid = info.getValue<string>()
          return <Favorite
            checked={isFavorite(favorites, guid)}
            onChange={(e)=>{
              if (e.target.checked) {
                addFavorite(guid)
              }
              else {
                removeFavorite(guid)
              }
          }} />
        },
        enableSorting: false,
        size: 20,
        minSize: 20,
      },
      {
        accessorKey: 'name',
        header: 'Name',
        cell: info => {
          const {guid} = info.row.original
          return (
            <Link href={`/ingredients/details/${guid}`}>
              <Tag>
                {titleCase(info.getValue<string>())}
              </Tag>
            </Link>
          )
        },
      },
      {
        accessorKey: 'category',
        header: 'Category',
        cell: info => <Tag variant="white">
          <IconWorkflow size={16} className="text-neutral-500" />
          {titleCase(info.getValue<string>())}
        </Tag>,
      },
      {
        accessorKey: 'menu_adoption',
        header: 'Menu Adoption',
        cell: info => {
          const menuAdoption = titleCase(info.getValue<string>());
          const color = getVariantForMenuAdoption(menuAdoption)
          return (
            <Tag variant={color}>
              {menuAdoption}
            </Tag>
          );
        },
      },
      {
        accessorKey: 'pen_rate',
        header: 'Penetration',
        cell: info => (`${info.getValue<number>() || 0}%`)
      },
      {
        accessorKey: 'social_mentions',
        header: 'Social Mentions',
        cell: info => (`${info.getValue<number>() || 0}%`)
      },
      {
        accessorKey: 'change_1y',
        header: 'FoodService Growth',
        cell: info => {
          const value = info.getValue<number>();
          const {type, icon }  = getVariantForChange(value);

          return (
            <Tag variant={type} >
              {icon}
              {value?.toFixed(2) || 0.0}%
            </Tag>
          );
        },
      },
      {
        accessorKey: 'retail_change',
        header: 'Retail Growth',
        cell: info => {
          const value = info.getValue<number>();
          const {type, icon }  = getVariantForChange(value);

          return (
            <Tag variant={type} >
              {icon}
              {value?.toFixed(2)}%
            </Tag>
          );
        },
      }
    ],
    [favorites, addFavorite, isFavorite, removeFavorite]
  );

  const fuzzyFilter: FilterFn<Ingredient> = (row, columnId, value) => {
    // If no filter value, return all rows
    if (!value || typeof value !== 'string') return true;

    const rowValue = row.getValue(columnId);

    // Check if the value is a string and contains the filter value
    if (typeof rowValue === 'string') {
      return rowValue.toLowerCase().includes(value.toLowerCase());
    }

    // For complex objects like menuAdoption, attempt to match against status
    if (rowValue && typeof rowValue === 'object' && 'status' in rowValue) {
      return (rowValue.status as string).toLowerCase().includes(value.toLowerCase());
    }

    // For other cases, don't filter
    return true;
  };

  const emptyState = (
    <div className="text-center py-6">
      <p className="text-gray-500">No ingredients found</p>
      <p className="text-sm text-gray-400">Try adjusting your search or filters</p>
    </div>
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      pagination,
      sorting,
      globalFilter,
      columnSizing,
    },
    enableGlobalFilter: true,
    enableColumnResizing: true,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    manualSorting: true,
    manualPagination: true,
    manualFiltering: true,
    rowCount: ingredients?.total_records,
    onGlobalFilterChange: setGlobalFilter,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode,
    defaultColumn: {
      size: 150,
      minSize: 50,
      maxSize: 500,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    globalFilterFn: fuzzyFilter,
  });

  return (
    <div className="mt-[60px]">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
        table={table}
        tableName="ingredients"
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">

        <EnhancedTable
          table={table}
          withBorder
          enableResizing
          emptyState={emptyState}
          isLoading={isLoading}
        />
        <Pagination
          currentPage={page}
          perPage={pageSize}
          totalResults={ingredients?.total_records}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
};

export default AllIngredientsTable;
