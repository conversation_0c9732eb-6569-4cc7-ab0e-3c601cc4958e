import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";
import { fetchIngredientTrends } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

const COLORS = ["#8BC539", "#FFBE05", "#D273D8", "#9571AD", "#FF6D56"];

interface SocialMediaIngredientTrendsProps {
  className?: string;
}

const SocialMediaIngredientTrends = ({
  className,
}: SocialMediaIngredientTrendsProps) => {
  const { data: session } = useSession();

  const [selected, setSelected] = useState<string>("");

  const { data } = useQuery({
    queryFn: () => {
      return fetchIngredientTrends({
        auth: session?.user.authorization as string,
      });
    },
    queryKey: ["ingredients_trends"],
    enabled: !!session?.user?.authorization,
  });

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  interface IngredientTrendItem {
    name: string;
    data: { x: string; y: number }[];
  }

  const chartData: LineSeriesItem[] =
    (data as IngredientTrendItem[] | undefined)?.map(
      (item: IngredientTrendItem, idx: number) => ({
        id: idx + 1,
        label: item.name,
        color: COLORS[idx] || "#8BC539",
        data: item.data?.map((val) => Math.round(val.y * 100)) || [],
      }),
    ) || [];

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Social Media Ingredient Trends</Heading>
          <Info
            tooltipId="social-media-ingredient-trends"
            content="Ingredients social media trends by most engaged/viewed content."
          />
        </div>
        <div className="flex items-center gap-1">
          <Link href="/ingredients/social-media-ingredient-trends">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="border-muted-100 rounded-lg border p-4">
          <LineChart
            dataItems={chartData}
            labels={
              data?.[0]?.data?.map((val: { x: string }) => val.x) || []
            }
            symbol="$"
            yTickCallback={(val) => `$${val}`}
            height={320}
          />
        </div>
      </div>
    </Card>
  );
};

export default SocialMediaIngredientTrends;
