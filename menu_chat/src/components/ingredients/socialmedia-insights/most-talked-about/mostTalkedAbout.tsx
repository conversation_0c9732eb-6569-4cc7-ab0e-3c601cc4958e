import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import titleCase from "voca/title_case";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import { fetchIngredientSocialMediaTrends } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

interface MostTalkedAboutIngredientsProps {
  className?: string;
}

interface IngredientTrend {
  ingredient: string;
  handle: string;
  category?: string;
  published?: string;
  sentiments: {
    "strongly-like": number;
    neutral: number;
    "strongly-dislike": number;
  };
  posts_count: number;
  growth: number;
  url: string;
}

type IngredientTrendResponse = IngredientTrend[];

const MostTalkedAboutIngredients = ({
  className,
}: MostTalkedAboutIngredientsProps) => {
  const { data: session } = useSession();

  const { data } = useQuery<IngredientTrendResponse>({
    queryFn: () => {
      return fetchIngredientSocialMediaTrends({
        auth: session?.user.authorization as string,
        page: 1,
        perPage: 5,
        sort: "",
        filters: [],
        search: "",
      });
    },
    queryKey: ["ingredients_social_media_trends"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Top Social Media Influencers</Heading>
          <Info
            tooltipId="social-media-flavor-trends"
            content="Social media flavor trends for menu items based on the posts number"
          />
        </div>
        <Link href="/ingredients/social-media-flavor-trends">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Account Name</Th>
            <Th>Number of Posts</Th>
            <Th>Category</Th>
            <Th>Latest Post</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data?.slice(0, 6).map((influencer, key) => (
            <Tr key={`ingridient-trend-${key}`}>
              <Td>
                <div onClick={() => window.open(`https://www.instagram.com/${influencer.handle}`, '_blank')}>
                  {`@${influencer.handle}`}
                </div>
              </Td>
              <Td>
                {influencer.posts_count?.toLocaleString()}
              </Td>
              <Td>{titleCase(influencer.category?.replaceAll("|", ", "))}</Td>
              <Td>
                <Link href={influencer.url} target="_blank" className="text-blue-600 hover:underline">
                  Link
                </Link>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostTalkedAboutIngredients;
