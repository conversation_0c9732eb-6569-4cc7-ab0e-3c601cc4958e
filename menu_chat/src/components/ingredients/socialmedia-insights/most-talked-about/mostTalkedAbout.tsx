import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import titleCase from "voca/title_case";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import {
  IconTaste2,
  IconGraph,
  IconIncrease,
  IconDecrease,
} from "@/components/icons";
import SentimentBar from "@/components/charts/sentimentBar";
import { fetchIngredientSocialMediaTrends } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

interface MostTalkedAboutIngredientsProps {
  className?: string;
}

interface IngredientTrend {
  ingredient: string;
  sentiments: {
    "strongly-like": number;
    neutral: number;
    "strongly-dislike": number;
  };
  posts_count: number;
  growth: number;
}

interface IngredientTrendResponse {
  data: IngredientTrend[];
  total_count: number;
}

const MostTalkedAboutIngredients = ({
  className,
}: MostTalkedAboutIngredientsProps) => {
  const { data: session } = useSession();

  const { data } = useQuery<IngredientTrendResponse>({
    queryFn: () => {
      return fetchIngredientSocialMediaTrends({
        auth: session?.user.authorization as string,
        page: 1,
        perPage: 5,
        sort: "",
        filters: [],
        search: "",
      });
    },
    queryKey: ["ingredients_social_media_trends"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Social Media Flavor Trends</Heading>
          <Info
            tooltipId="social-media-flavor-trends"
            content="Social media flavor trends for menu items based on the posts number"
          />
        </div>
        <Link href="/ingredients/social-media-flavor-trends">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Sentiment</Th>
            <Th>Post Number</Th>
            <Th>Growth</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data?.data?.slice(0, 6).map((ingredient, key) => (
            <Tr key={`ingridient-trend-${key}`}>
              <Td>
                <Tag variant="white">
                  <IconTaste2 size={16} className="text-neutral-500" />
                  {titleCase(ingredient.ingredient)}
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[150px]"
                  segments={ingredient.sentiments}
                />
              </Td>
              <Td>{ingredient.posts_count?.toLocaleString()}</Td>
              <Td>
                <Tag
                  variant={
                    ingredient.growth > 0 ? "greenOutline" : "redOutline"
                  }
                >
                  {ingredient.growth > 0 ? (
                    <>
                      <IconIncrease size={16} />
                      {ingredient.growth.toFixed(2)}%
                    </>
                  ) : ingredient.growth === 0.0 ? (
                    <>
                      <IconGraph size={16} />
                      {ingredient.growth.toFixed(2)}%
                    </>
                  ) : (
                    <>
                      <IconDecrease size={16} />
                      {Math.abs(ingredient.growth).toFixed(2)}%
                    </>
                  )}
                </Tag>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostTalkedAboutIngredients;
