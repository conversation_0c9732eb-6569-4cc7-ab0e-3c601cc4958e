import { useState } from "react";
import titleCase from "voca/title_case";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import Favorite from "@/components/base/favorite";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import { IconGraph, IconIncrease, IconDecrease } from "@/components/icons";
import SentimentBar from "@/components/charts/sentimentBar";
import { fetchIngredientSocialMediaTrends } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

interface SentimentSegment {
  color: string;
  value: number;
  label: string;
}

interface IngredientTrend {
  guid: string;
  ingredient: string;
  sentiments: SentimentSegment[];
  total_engagements: number;
  posts_count: number;
  growth: number;
}

interface IngredientTrendsResponse {
  data: IngredientTrend[];
  total_count: number;
}

const MostTalkedAboutTable: React.FC = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(10);

  const { data: session } = useSession();

  const { data } = useQuery<IngredientTrendsResponse>({
    queryFn: () =>
      fetchIngredientSocialMediaTrends({
        auth: session?.user.authorization as string,
        search,
        filters,
        sort,
        page,
        perPage,
      }),
    queryKey: ["ingredients_trends", search, filters, sort, page, perPage],
    enabled: !!session?.user?.authorization,
  });

  const totalResults = data?.total_count || 0;

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />

      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Name</Th>
              <Th>Sentiment</Th>
              <Th>Engagement/Views</Th>
              <Th>Posts Number</Th>
              <Th>Growth</Th>
            </Tr>
          </Thead>
          <Tbody>
            {(data?.data || []).map((ingredient, key) => (
              <Tr key={`ingredient-trend-${ingredient.guid || key}`}>
                <Td>
                  <Favorite />
                </Td>
                <Td>
                  <Link
                    href={`/ingredients/details/${ingredient.guid}/social-media-insights`}
                  >
                    <Tag variant="white">{titleCase(ingredient.ingredient)}</Tag>
                  </Link>
                </Td>
                <Td>
                  <SentimentBar
                    className="max-w-[300px]"
                    segments={ingredient.sentiments}
                  />
                </Td>
                <Td>{ingredient.total_engagements.toLocaleString()}</Td>
                <Td>{ingredient.posts_count.toLocaleString()}</Td>
                <Td>
                  <Tag
                    variant={
                      ingredient.growth > 0 ? "greenOutline" : "redOutline"
                    }
                  >
                    {ingredient.growth > 0 ? (
                      <IconIncrease size={16} />
                    ) : ingredient.growth === 0 ? (
                      <IconGraph size={16} />
                    ) : (
                      <IconDecrease size={16} />
                    )}
                    {Math.abs(ingredient.growth)}%
                  </Tag>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>

        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default MostTalkedAboutTable;
