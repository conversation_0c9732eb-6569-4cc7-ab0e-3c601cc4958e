import BarChart from "@/components/charts/barChart";
import titleCase from "voca/title_case";

const COLORS = [
  "#78C5E3",
  "#FAAB61",
  "#DF9DE4",
  "#FF9985",
  "#BDA4CB"
];

interface GenerationData {
  theme: string;
  percentage: number;
}

const Generations = ({ data = [] }) => {
  const chartData = data.map((item: GenerationData, index) => ({
    id: index + 1,
    label: titleCase(item.theme),
    color: COLORS[index],
    value: item.percentage,
    symbol: '%',
  }));
  return (
    <div className="border-muted-200 rounded-lg border p-2">
      <BarChart dataItems={chartData} symbol="%" maxValue={100} height={270} />
    </div>
  );
};

export default Generations;
