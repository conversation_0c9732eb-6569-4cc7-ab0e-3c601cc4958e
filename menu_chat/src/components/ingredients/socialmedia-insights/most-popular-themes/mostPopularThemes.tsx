import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import Tabs, { TabContent } from "@/components/base/tabs";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { fetchIngredientsMostPopularThemesByAges } from "@/api/ingredients";

type Cat = "Appetizers" | "Entrees" | "Desserts" | "Beverages";
const CATS: Cat[] = ["Appetizers", "Entrees", "Desserts", "Beverages"];

type TrendingPost = {
  recipe: string;
  influencer: string;
  post_url: string;
  published: string;
  engagements: number;
};

type TrendingByCategory = {
  category: Cat;
  posts: TrendingPost[];
};

type TrendingResponse = {
  period: { from: string; to: string };
  categories: TrendingByCategory[];
};

interface Props { className?: string }

const MostPopularThemes = ({ className }: Props) => {
  const { data: session } = useSession();
  const [selected, setSelected] = useState("");

  const { data, isLoading } = useQuery<TrendingResponse>({
    queryFn: () => fetchIngredientsMostPopularThemesByAges({ auth: session?.user.authorization as string }),
    queryKey: ["top_trending_posts_by_category"],
    enabled: !!session?.user?.authorization,
  });

  const byCat: Record<Cat, TrendingPost[]> = (data?.categories || []).reduce(
    (acc, row) => {
      acc[row.category] = row.posts || [];
      return acc;
    },
    { Appetizers: [], Entrees: [], Desserts: [], Beverages: [] } as Record<Cat, TrendingPost[]>
  );

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Top 10 Trending Posts per Category</Heading>
          <Info
            tooltipId="top-trending-posts"
            content="Top posts by total engagements in the last 3 months, grouped into Appetizers, Entrees, Desserts, and Beverages."
          />
        </div>
        <Dropdown options={CHART_DROPDOWN} selectedValue={selected} onSelect={setSelected}>
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>

      <div className="p-4">
        <Tabs navigation={CATS}>
          {CATS.map((cat) => (
            <TabContent key={cat}>
              {isLoading ? (
                <div className="text-sm text-muted-500 p-4">Loading…</div>
              ) : (
                <Table>
                  <Thead>
                    <Tr>
                      <Th>Influencer</Th>
                      <Th>Post</Th>
                      <Th className="text-right">Engagements</Th>
                      <Th>Recipe</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {(byCat[cat] || []).slice(0, 10).map((p, i) => (
                      <Tr key={`${cat}-${i}`}>
                        <Td className="whitespace-nowrap">@{p.influencer || "—"}</Td>
                        <Td className="whitespace-nowrap">
                          {p.post_url ? (
                            <a href={p.post_url} target="_blank" className="text-blue-600 hover:underline">
                              Open
                            </a>
                          ) : (
                            "—"
                          )}
                        </Td>
                        <Td className="text-right">{p.engagements?.toLocaleString?.() ?? "—"}</Td>
                        <Td className="max-w-[320px] truncate" title={p.recipe}>
                          {p.recipe || "—"}
                        </Td>
                      </Tr>
                    ))}
                    {byCat[cat]?.length === 0 && (
                      <Tr><Td className="text-center text-muted-500 py-6">No posts found</Td></Tr>
                    )}
                  </Tbody>
                </Table>
              )}
            </TabContent>
          ))}
        </Tabs>
      </div>
    </Card>
  );
};

export default MostPopularThemes;
