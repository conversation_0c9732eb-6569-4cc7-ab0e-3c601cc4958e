import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import Tabs, { TabContent } from "@/components/base/tabs";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Generations from "./generations";
import { fetchIngredientsMostPopularThemesByAges } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

interface MostPopularThemesProps {
  className?: string;
}

interface MostPopularThemesData {
  age_group: string;
  themes: Array<{
    theme: string;
    percentage: number;
  }>;
}

const MostPopularThemes = ({ className }: MostPopularThemesProps) => {
  const { data: session } = useSession();

  const [selected, setSelected] = useState<string>("");

  const { data } = useQuery({
    queryFn: () => {
      return fetchIngredientsMostPopularThemesByAges({
        auth: session?.user.authorization as string,
      });
    },
    queryKey: ["most_popular_themes_by_ages"],
    enabled: !!session?.user?.authorization,
  });

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            Most Popular Themes on Social Media Among Generations
          </Heading>
          <Info
            tooltipId="most-popular-themes"
            content="Top 5 most popular themes for the 5 generations. Based on the social media posts and trends"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <Tabs
          navigation={[
            "Gen Alpha",
            "Gen Z",
            "Millennials",
            "Gen X",
            "Baby Boomers",
          ]}
        >
          <TabContent>
            <Generations
              data={
                (data || []).filter(
                  (row: MostPopularThemesData) => row.age_group === "Gen Alpha",
                )?.[0]?.themes
              }
            />
          </TabContent>
          <TabContent>
            <Generations
              data={
                (data || []).filter(
                  (row: MostPopularThemesData) => row.age_group === "Gen Z",
                )?.[0]?.themes
              }
            />
          </TabContent>
          <TabContent>
            <Generations
              data={
                (data || []).filter(
                  (row: MostPopularThemesData) =>
                    row.age_group === "Millennials",
                )?.[0]?.themes
              }
            />
          </TabContent>
          <TabContent>
            <Generations
              data={
                (data || []).filter(
                  (row: MostPopularThemesData) => row.age_group === "Gen X",
                )?.[0]?.themes
              }
            />
          </TabContent>
          <TabContent>
            <Generations
              data={
                (data || []).filter(
                  (row: MostPopularThemesData) =>
                    row.age_group === "Baby Boomers",
                )?.[0]?.themes
              }
            />
          </TabContent>
        </Tabs>
      </div>
    </Card>
  );
};

export default MostPopularThemes;
