import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import titleCase from "voca/title_case";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import RateTag from "@/components/base/rateTag";
import { fetchIngredientsSocialMediaMentions } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

interface MostSocialMediaMentionsProps {
  className?: string;
}

interface IngredientSocialMediaMention {
  ingredient: string;
  sov_percent: number;
  growth_percent: number;
}

const MostSocialMediaMentions = ({
  className,
}: MostSocialMediaMentionsProps) => {
  const { data: session } = useSession();

  const { data } = useQuery({
    queryFn: () => {
      return fetchIngredientsSocialMediaMentions({
        auth: session?.user.authorization as string,
      });
    },
    queryKey: ["ingredients_mentions"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Trending Ingredients</Heading>
          <Info
            tooltipId="most-social-media-mentions"
            content="Top 6 most mentioned ingredients on the social media"
          />
        </div>
        <Link href="/ingredients/social-media-ingredient-trends">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>SOV</Th>
            <Th>Growth</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data
            ?.slice(0, 6)
            .map((item: IngredientSocialMediaMention, index: number) => (
              <Tr key={`index-${index}`}>
                <Td>
                  <Tag>{titleCase(item.ingredient)}</Tag>
                </Td>
                <Td>{item.sov_percent?.toLocaleString()}</Td>
                <Td>
                  <RateTag value={item.growth_percent } />
                </Td>
              </Tr>
            ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostSocialMediaMentions;
