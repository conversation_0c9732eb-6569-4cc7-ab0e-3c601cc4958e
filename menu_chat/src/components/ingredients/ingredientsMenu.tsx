import { twMerge } from "tailwind-merge";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconCalendar } from "@/components/icons";
import { INGREDIENTS_MENU } from "@/data/ingredients";
import { useDateToggle} from "@/contexts/DateToggleContext";

const IngredientsMenu = () => {
  // const [selectedPeriod, setSelectedPeriod] = useState<string>("Last quarter");
  const { selectedPeriod, setSelectedPeriod, options } = useDateToggle();

  const pathname = usePathname();

  const handleSelectPeriod = (value: string) => {
    setSelectedPeriod(value);
  };

  return (
    <div className="bg-custom-blur absolute top-0 right-0 left-0 z-20 m-auto flex w-full items-center justify-between gap-2 px-8 py-3">
      <div className="flex items-center gap-1">
        {INGREDIENTS_MENU.map((ingredient) => (
          <Link key={ingredient.slug} href={`${ingredient.slug}`}>
            <Button
              variant="tertiary"
              size="sm"
              className={twMerge(
                "hover:bg-muted-100 active:bg-muted-100 font-archivo rounded-lg border-0 px-4",
                ingredient.slug === pathname
                  ? "bg-muted-100 hover:bg-muted-100 active:bg-muted-200"
                  : "bg-transparent",
              )}
            >
              {ingredient.title}
            </Button>
          </Link>
        ))}
      </div>
      <div className="flex items-center gap-1">
        <Dropdown
          options={options}
          selectedValue={selectedPeriod}
          onSelect={(e) => handleSelectPeriod(e)}
          icon={<IconCalendar size={16} />}
          withCaret
        />
      </div>
    </div>
  );
};

export default IngredientsMenu;
