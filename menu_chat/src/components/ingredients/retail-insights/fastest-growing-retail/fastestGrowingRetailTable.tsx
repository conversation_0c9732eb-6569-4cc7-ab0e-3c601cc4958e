import { useState } from "react";
import Pagination from "@/components/table/pagination";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import {
  IconGraph,
  IconIncrease,
  IconDecrease,
  IconCircleUpright,
  IconCircle,
  IconCircleFilled,
  IconCircleRight,
  IconWorkflow,
} from "@/components/icons";
import Favorite from "@/components/base/favorite";

const FastestGrowingRetailTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Name</Th>
              <Th>Category</Th>
              <Th>Menu Adoption</Th>
              <Th>Penetration</Th>
              <Th>Social Mentions</Th>
              <Th>Foodservice Change</Th>
              <Th>Retail Change</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Lettuce</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Vegetables
                </Tag>
              </Td>
              <Td>
                <Tag variant="blue">
                  <IconCircleRight size={16} />
                  Mature
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Cream</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Desserts
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>94,943</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>BBQ sauce</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Desserts
                </Tag>
              </Td>
              <Td>
                <Tag variant="green">
                  <IconCircleUpright size={16} />
                  Growth
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>94,943</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Almond</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Nuts
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} /> Emergence
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>23,908</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Cranberry</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Berries
                </Tag>
              </Td>
              <Td>
                <Tag variant="blue">
                  <IconCircleRight size={16} />
                  Mature
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>53,908</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Banana</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Fruit
                </Tag>
              </Td>
              <Td>
                <Tag variant="blue">
                  <IconCircleRight size={16} />
                  Mature
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>23,908</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Acai</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Berries
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>98,332</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Olive Oil</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Sauce
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} /> Emergence
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>23,908</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Ube</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Vegetables
                </Tag>
              </Td>
              <Td>
                <Tag variant="blue">
                  <IconCircleRight size={16} />
                  Mature
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>94,265</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Cream</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Desserts
                </Tag>
              </Td>
              <Td>
                <Tag variant="violet">
                  <IconCircleFilled size={16} />
                  Mainstream
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>94,943</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>BBQ sauce</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Desserts
                </Tag>
              </Td>
              <Td>
                <Tag variant="green">
                  <IconCircleUpright size={16} />
                  Growth
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>94,943</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>
                <Tag>Almond</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconWorkflow size={16} className="text-neutral-500" />
                  Nuts
                </Tag>
              </Td>
              <Td>
                <Tag variant="yellow">
                  <IconCircle size={16} /> Emergence
                </Tag>
              </Td>
              <Td>5,44%</Td>
              <Td>23,908</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default FastestGrowingRetailTable;
