import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import LineChart, {  } from "@/components/charts/lineChart";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fastestGrowingIngredientCategory } from "@/api/products";

interface FastestGrowingRetailProps {
  className?: string;
}

const FastestGrowingRetail = ({ className }: FastestGrowingRetailProps) => {
  const { data: session } = useSession();
  const { data } = useQuery({
    queryFn: () => fastestGrowingIngredientCategory({ auth: session?.user.authorization as string }),
    queryKey: ["fastest_growing_ingredient_category"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            Fastest Growing Ingredients Category{" "}
            <span className="text-muted-600">(Retail)</span>
          </Heading>
          <Info
            tooltipId="fastest-growing-ingredients"
            content="Ingredients that had highest penetetration growth in retail in the selected period"
          />
        </div>
        <Link href="/ingredients/fastest-growing-retail">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="border-muted-100 rounded-lg border p-4">
          <LineChart
            dataItems={data?.data || []}
            labels={data?.labels || []}
            symbol=""
            height={350}
            yTitle={{
              display: true,
              text: 'Product Count',
            }}
          />
        </div>
      </div>
    </Card>
  );
};

export default FastestGrowingRetail;
