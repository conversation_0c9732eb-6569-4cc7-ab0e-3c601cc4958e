import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import <PERSON><PERSON>hart from "@/components/charts/barChart";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { topRetailers } from "@/api/retailers";

interface TopRetailersProps {
  className?: string;
}

const TopRetailers = ({ className }: TopRetailersProps) => {
  const { data: session } = useSession();
  const { data } = useQuery({
    queryFn: () => topRetailers({ auth: session?.user.authorization as string }),
    queryKey: ["top_retailers"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Top Retailers</Heading>
          <Info
            tooltipId="top-retailers"
            content="Retailers with the highest products number"
          />
        </div>
        <Link href="/ingredients/top-retailers">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="font-archivo font-semibol bg-muted-500/10 mb-4 flex items-center justify-between rounded-md px-2 py-1 text-[11px] text-neutral-600 uppercase">
          <span>Name</span>
          <span># of Products</span>
        </div>
        <div className="border-muted-100 rounded-lg border px-4 py-1">
          <BarChart dataItems={data?.data || []} symbol="k" maxValue={data?.maxValue || 1000} />
        </div>
      </div>
    </Card>
  );
};

export default TopRetailers;
