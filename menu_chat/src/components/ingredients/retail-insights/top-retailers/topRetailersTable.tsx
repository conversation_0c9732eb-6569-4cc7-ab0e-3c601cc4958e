import { useState } from "react";
import PaginationInput from "@/components/table/paginationInput";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Favorite from "@/components/base/favorite";
// import SentimentBar from "@/components/charts/sentimentBar";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import {
  topRetailers,
  // RetailerData,
} from "@/api/retailers";

const TopRetailersTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const { data: session } = useSession();

  const { data, isLoading } = useQuery<TopRetailersResult, Error>({
    queryFn: () =>
      topRetailers({
        auth: session?.user.authorization as string,
        page,
        perPage: 10,
      }),
    queryKey: ["top_retailers_table", page],
    enabled: !!session?.user?.authorization,
  });

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  if (isLoading || !data) return <div>Loading...</div>;

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Retailer Name</Th>
              <Th>Product Number</Th>
              <Th>SKU Count</Th>
              {/*<Th>Sentiment</Th>*/}
              <Th>Social Mentions</Th>
              <Th># Locations</Th>
            </Tr>
          </Thead>
          <Tbody>
            {data.data.map((item, index) => {
              const originalData = data.originalData[index];

              if (!originalData) {
                console.warn(`No matching original data for item with id ${item.id}`);
                return null;
              }

              return (
                <Tr key={item.id}>
                  <Td>
                    <Favorite />
                  </Td>
                  <Td>{item.label}</Td>
                  <Td>
                    {originalData.products_with_l0?.toLocaleString() || "0"}
                  </Td>
                  <Td>{item.value.toLocaleString()}</Td>
                  {/*<Td>*/}
                  {/*  <SentimentBar*/}
                  {/*    className="max-w-[300px]"*/}
                  {/*    segments={{*/}
                  {/*      "strongly-dislike":*/}
                  {/*        originalData?.sentiment_percentages?.[0] || 0,*/}
                  {/*      "somewhat-dislike":*/}
                  {/*        originalData?.sentiment_percentages?.[1] || 0,*/}
                  {/*      neutral: originalData?.sentiment_percentages?.[2] || 0,*/}
                  {/*      "somewhat-like":*/}
                  {/*        originalData?.sentiment_percentages?.[3] || 0,*/}
                  {/*      "strongly-like":*/}
                  {/*        originalData?.sentiment_percentages?.[4] || 0,*/}
                  {/*    }}*/}
                  {/*  />*/}
                  {/*</Td>*/}
                  <Td>
                    {originalData?.social_mentions?.toLocaleString() || "0"}
                  </Td>
                  <Td>{originalData?.unique_store || "0"}</Td>
                </Tr>
              );
            })}
          </Tbody>
        </Table>
        <PaginationInput
          value={page}
          totalResults={data.total}
          onChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default TopRetailersTable;
