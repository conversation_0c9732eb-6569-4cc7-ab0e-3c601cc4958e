import { useState, useMemo, useEffect } from "react";
import Favorite from "@/components/base/favorite";
import Button from "@/components/base/button";
import { IconDownload } from "@/components/icons";
import Tag from "@/components/base/tag";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { topRetailers } from "@/api/retailers";
import {
  ColumnDef,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  ColumnResizeMode,
  SortingState,
} from "@tanstack/react-table";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import Pagination from "@/components/table/pagination";
import { exportTableToCSV } from "@/lib/exportCsv";

type Row = {
  id: string | number;
  favorite: null;
  retailer_name: string;
  products_with_l0: number;
  sku_count: number;
  social_mentions: number;
  locations: string | number;
};

const TopRetailersTable = () => {
  const { data: session } = useSession();

  const [page, setPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(10);
  const [pagination, setPagination] = useState({ pageIndex: page - 1, pageSize: perPage });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnSizing, setColumnSizing] = useState({});
  const [columnResizeMode] = useState<ColumnResizeMode>("onChange");

  const { data, isLoading } = useQuery<TopRetailersResult, Error>({
    queryFn: () =>
      topRetailers({
        auth: session?.user.authorization as string,
        page,
        perPage,
        chartLimit: perPage,
      }),
    queryKey: ["top_retailers_table", page, perPage],
    enabled: !!session?.user?.authorization,
  });

  const rows: Row[] = useMemo(() => {
    if (!data) return [];
    return data.data.map((item, index) => {
      const original = data.originalData[index];
      return {
        id: item.id,
        favorite: null,
        retailer_name: item.label,
        products_with_l0: original?.products_with_l0 ?? 0,
        sku_count: item.value ?? 0,
        social_mentions: original?.social_mentions ?? 0,
        locations: original?.unique_store ?? "0",
      } as Row;
    });
  }, [data]);

  const totalResults = data?.total ?? 0;

  const columns = useMemo<ColumnDef<Row>[]>(
    () => [
      {
        id: "favorite",
        header: () => <span className="sr-only">Favorite</span>,
        cell: () => <Favorite />,
        size: 60,
      },
      {
        id: "retailer_name",
        accessorKey: "retailer_name",
        header: "Retailer Name",
        cell: (info) => <Tag className="cursor-default">{info.getValue<string>()}</Tag>,
      },
      {
        id: "products_with_l0",
        accessorKey: "products_with_l0",
        header: "Product Number",
        cell: (info) => <Tag variant="white">{Number(info.getValue<number>() ?? 0).toLocaleString()}</Tag>,
      },
      {
        id: "sku_count",
        accessorKey: "sku_count",
        header: "SKU Count",
        cell: (info) => <Tag variant="white">{Number(info.getValue<number>() ?? 0).toLocaleString()}</Tag>,
      },
      {
        id: "social_mentions",
        accessorKey: "social_mentions",
        header: "Social Mentions",
        cell: (info) => <Tag variant="white">{Number(info.getValue<number>() ?? 0).toLocaleString()}</Tag>,
      },
      {
        id: "locations",
        accessorKey: "locations",
        header: "# Locations",
        cell: (info) => <Tag variant="white">{String(info.getValue<string | number>() ?? "0")}</Tag>,
      },
    ],
    [],
  );

  const table = useReactTable({
    data: rows,
    columns,
    state: { pagination, sorting, columnSizing },
    onPaginationChange: (updater) => {
      const next = typeof updater === "function" ? updater(pagination) : updater;
      setPagination(next);
      setPage(next.pageIndex + 1);
      setPerPage(next.pageSize);
    },
    onSortingChange: (updater) => {
      const next = typeof updater === "function" ? updater(sorting) : updater;
      setSorting(next);
    },
    manualSorting: false,
    manualPagination: true,
    rowCount: totalResults,
    enableColumnResizing: true,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode,
    defaultColumn: { size: 150, minSize: 50, maxSize: 500 },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  useEffect(() => {
    setPagination({ pageIndex: page - 1, pageSize: perPage });
  }, [page, perPage]);

  return (
    <div className="py-3">
      <div className="flex items-center justify-between gap-3 pt-3 pb-6">
        <Button
          variant="primary"
          size="sm"
          onClick={() => exportTableToCSV(table, "Top Retailers")}
        >
          <IconDownload size={20} />
          Export
        </Button>
      </div>
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <EnhancedTable table={table} withBorder enableResizing isLoading={isLoading} />
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default TopRetailersTable;
