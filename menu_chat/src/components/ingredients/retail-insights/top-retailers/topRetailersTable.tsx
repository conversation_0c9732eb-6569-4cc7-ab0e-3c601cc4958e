import { useState } from "react";
import PaginationInput from "@/components/table/paginationInput";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Favorite from "@/components/base/favorite";
import SentimentBar from "@/components/charts/sentimentBar";

const TopRetailersTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState<number>(1);

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>
                <span className="sr-only">Favorite</span>
              </Th>
              <Th>Retailer Name</Th>
              <Th>Product Number</Th>
              <Th>SKU Count</Th>
              <Th>Sentiment</Th>
              <Th>Social Mentions</Th>
              <Th># Locations</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Walmart</Td>
              <Td>94,265</Td>
              <Td>3456</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>875</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Costco</Td>
              <Td>44,265</Td>
              <Td>3766</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>99,859</Td>
              <Td>238</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Kroger</Td>
              <Td>94,265</Td>
              <Td>2053</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    neutral: 60,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>99,859</Td>
              <Td>276</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Target</Td>
              <Td>44,265</Td>
              <Td>2050</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    neutral: 55,
                    "somewhat-like": 30,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>902,265</Td>
              <Td>234</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Walmart</Td>
              <Td>44,265</Td>
              <Td>2050</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>902,265</Td>
              <Td>234</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Raley’s Supermarkets</Td>
              <Td>94,265</Td>
              <Td>3456</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>94,265</Td>
              <Td>875</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Corborn’s Inc.</Td>
              <Td>44,265</Td>
              <Td>3766</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>99,859</Td>
              <Td>238</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Harps Food Stores</Td>
              <Td>94,265</Td>
              <Td>2053</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    neutral: 60,
                    "somewhat-like": 15,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>99,859</Td>
              <Td>276</Td>
            </Tr>
            <Tr>
              <Td>
                <Favorite />
              </Td>
              <Td>Target</Td>
              <Td>44,265</Td>
              <Td>2050</Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 15,
                    neutral: 40,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>902,265</Td>
              <Td>234</Td>
            </Tr>
          </Tbody>
        </Table>
        <PaginationInput
          value={page}
          totalResults={100}
          onChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default TopRetailersTable;
