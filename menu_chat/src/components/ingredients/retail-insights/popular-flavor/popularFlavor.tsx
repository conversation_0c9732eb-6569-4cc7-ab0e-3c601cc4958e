import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";

interface PopularFlavorProps {
  className?: string;
}

const PopularFlavor = ({ className }: PopularFlavorProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Popular Flavor and Ingredient Trends</Heading>
          <Info
            tooltipId="popular-flavor-trends"
            content="Most popular flavors and ingredients based on interest level and number of competitors"
          />
        </div>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Interest Level</Th>
            <Th>Competitors #</Th>
            <Th title="NPD Opportunity">NPD Opp...</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                <Tag variant="grey">Sweet</Tag>
                <Tag variant="grey">&</Tag>
                <Tag variant="grey">Spicy</Tag>
              </div>
            </Td>
            <Td>
              <Tag variant="greenOutline">High</Tag>
            </Td>
            <Td>4</Td>
            <Td>85,44%</Td>
          </Tr>
          <Tr>
            <Td>
              <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                <Tag variant="grey">Fermented</Tag>
                <Tag variant="grey">&</Tag>
                <Tag variant="grey">Pickled</Tag>
              </div>
            </Td>
            <Td>
              <Tag variant="magentaOutline">Growing</Tag>
            </Td>
            <Td>2</Td>
            <Td>60,78%</Td>
          </Tr>
          <Tr>
            <Td>
              <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
                <Tag variant="grey">Exotic Citrus</Tag>
                <Tag variant="grey">&</Tag>
                <Tag variant="grey">Tropical</Tag>
              </div>
            </Td>
            <Td>
              <Tag variant="yellowOutline">Rising</Tag>
            </Td>
            <Td>5</Td>
            <Td>78,45%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="grey">Floral Botanicals</Tag>
            </Td>
            <Td>
              <Tag variant="magentaOutline">Growing</Tag>
            </Td>
            <Td>3</Td>
            <Td>12,44%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="grey">Chef Inspired</Tag>
            </Td>
            <Td>
              <Tag variant="greenOutline">High</Tag>
            </Td>
            <Td>1</Td>
            <Td>67,44%</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default PopularFlavor;
