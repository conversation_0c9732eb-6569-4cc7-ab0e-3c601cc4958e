import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchWhiteSpaceOpportunities } from "@/api/products";

interface WhiteSpaeOpportunitiesProps {
  className?: string;
}

interface WhiteSpaceOpportunity {
  category_name: string;
  saturation: "High" | "Medium" | "Low";
  saturation_percent: string;
  unique_brands: number;
}

const WhiteSpaeOpportunities = ({ className }: WhiteSpaeOpportunitiesProps) => {
  const { data: session } = useSession();
  const { data = [] } = useQuery({
    queryFn: () => fetchWhiteSpaceOpportunities({ auth: session?.user.authorization as string }),
    queryKey: ["white_space_opportunities"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            White Space Opportunities in Emerging Categories
          </Heading>
          <Info
            tooltipId="most-liked-ingredients"
            content="Top opportunities in emerging categories, considering market saturation"
          />
        </div>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Product Category</Th>
            <Th>Saturation</Th>
            <Th>Competitors #</Th>
            <Th>Predicted Growth</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data.map((item: WhiteSpaceOpportunity, idx: number) => (
            <Tr key={idx}>
              <Td>
                <Tag variant="white">{item.category_name}</Tag>
              </Td>
              <Td>
                <Tag
                  variant={
                    item.saturation === "High"
                      ? "greenOutline"
                      : item.saturation === "Medium"
                      ? "blueOutline"
                      : "redOutline"
                  }
                >
                  {item.saturation}
                </Tag>
              </Td>
              <Td>{item.unique_brands}</Td>
              <Td>{item.saturation_percent}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default WhiteSpaeOpportunities;
