import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Tag from "@/components/base/tag";
import { IconIncrease  } from "@/components/icons";
import Image from "next/image";
import Link from "next/link";
import { ingredientsInnovation } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import titleCase from "voca/title_case";

interface IngredientsInnovationProps {
  className?: string;
}

const IngredientsInnovation = ({ className }: IngredientsInnovationProps) => {
  const { data: session } = useSession();
  // TODO: use setSorting([{id: 'last_trend_value', desc: true}]); instead params: { preview: '1' } ?
  const { data: ingredients_innovations = [] } = useQuery({
    queryFn: () => ingredientsInnovation({ auth: session?.user.authorization as string, params: { preview: '1' } }),
    queryKey: ["ingredients_innovations"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Ingredients Innovation</Heading>
          <Info
            tooltipId="ingredients-innovation-tooltip"
            content="New ingredients in past 3 months."
          />
        </div>
        <Link href="/ingredients/ingredients-innovation">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="grid grid-cols-4 gap-2 p-4">
        {ingredients_innovations.slice(0, 4).map((item: InnovationIngredient, key: number) => (
          <div key={`ingredient-${key}`} className="border-muted-100 flex flex-col gap-2 rounded-lg border p-2">
            <Image
              src={item.image_url ?? "/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={140}
              className="h-[140px] w-full rounded-lg object-cover"
            />
            <div className="px-2 py-1">
              <Link href={`/ingredients/details/${item.guid}`}>
                <Tag>{titleCase(item.name)}</Tag>
              </Link>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Penetration
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  {item.penetration?.toFixed(2)}%
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  change
                </Paragraph>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  {item.change?.toFixed(2)}%
                </Tag>
              </div>
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appeared
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  {new Date(item.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short' })}
                </Paragraph>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default IngredientsInnovation;
