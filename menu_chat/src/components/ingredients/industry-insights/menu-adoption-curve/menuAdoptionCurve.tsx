import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import MenuAdoptionCurveItem from "./menuAdoptionCurveItem";
import { INGREDIENTS_FILTER, MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import { menuAdoptionCurve } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import FilterDropdown from "@/components/base/filterDropdown";

interface MenuAdoptionCurveProps {
  className?: string;
}

const MenuAdoptionCurve = ({ className }: MenuAdoptionCurveProps) => {
  const [filters, setFilters] = useState<string[]>([]);
  const { data: session } = useSession();
  const { data: menu_adoption_curve = {} } = useQuery({
    queryFn: () => menuAdoptionCurve({ auth: session?.user.authorization as string, filters }),
    queryKey: ["menu_adoption_curve", filters],
    enabled: !!session?.user?.authorization,
  });
  const [selected, setSelected] = useState<string>("");

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(() => newFilters);
  };

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const items = [
    { type: "emergence" as const, data: menu_adoption_curve?.emergence || [] },
    { type: "growth" as const, data: menu_adoption_curve?.growth || [] },
    { type: "mainstream" as const, data: menu_adoption_curve?.mainstream || [] },
    { type: "mature" as const, data: menu_adoption_curve?.mature || [] },
  ];

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            Menu Adoption Curve for 10 Most Used Ingredients
          </Heading>
          <Info
            tooltipId="menu-adoption-tooltip"
            content="Chart shows fastest growing ingredients in each of the four menu adoption curve stages in the selected period."
          />
        </div>
        <div className="flex items-center gap-1">
          <FilterDropdown
            selectedValue={filters}
            options={INGREDIENTS_FILTER}
            onSelect={(value) => handleFilterChange(value)}
          />
          <Button variant="ghost" size="xs">
            View all
          </Button>
          <Dropdown
            options={MENU_ADOPTION_CURVE_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="overflow-x-auto">
        <div className="grid min-w-[1116px] grid-cols-4 gap-4 p-4">
          {items.map((item, key) => <MenuAdoptionCurveItem key={`item-${key}-${item.data.length}`} type={item.type} items={item.data} />)}
        </div>
      </div>
    </Card>
  );
};

export default MenuAdoptionCurve;
