import Link from "next/link";
import { useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";
import {
  useReactTable,
  getCoreRowModel,
  // createColumnHelper,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import Tag from "@/components/base/tag";
import { IconIncrease } from "@/components/icons";
import { pairingTrends } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import titleCase from "voca/title_case";

interface PairingTrendsProps {
  className?: string;
}

// Define the tag variant types
type TagVariant =
  | "greenOutline"
  | "redOutline"
  | "blueOutline"
  | "neutral"
  | "blue"
  | "red"
  | "green"
  | "yellow"
  | "violet"
  | "white"
  | undefined;

// Define the pairing data type

interface PairingChange {
  value: number;
  icon: React.ReactNode;
  type: TagVariant;
}

const getVariantForPairingChange = (change: number): PairingChange => {
  return {
    value: change,
    icon: <IconIncrease size={16} />,
    type: "greenOutline",
  };
};

const PairingTrends = ({ className }: PairingTrendsProps) => {
  const { data: session } = useSession();
  const [pagination] = useState({
    pageIndex: 0, //initial page index
    pageSize: 5, //default page size
  });
  const [sorting] = useState<SortingState>([
    { id: "pairing_percent", desc: true },
  ]);

  const { data: pairing_trends, isLoading } = useQuery({
    queryFn: () =>
      pairingTrends({
        auth: session?.user.authorization as string,
        pagination,
        sorting,
      }),
    queryKey: ["pairing_trends"],
    enabled: !!session?.user?.authorization,
  });

  // Sample data for pairings
  const data = useMemo<Pairing[]>(() => {
    if (!pairing_trends?.data) return [];
    return pairing_trends.data.data.slice(0, 5);
  }, [pairing_trends]);

  // Create column helper
  // const columnHelper = createColumnHelper<Pairing>();

  // Define columns for the table
  const columns = useMemo<ColumnDef<Pairing>[]>(
    () => [
      {
        header: "Name",
        cell: (info) => {
          const {
            ingredient_a_name,
            ingredient_a_guid,
            ingredient_b_name,
            ingredient_b_guid,
          } = info.row.original;
          const ingredients = [
            { name: ingredient_a_name, guid: ingredient_a_guid },
            { name: ingredient_b_name, guid: ingredient_b_guid },
          ];

          return (
            <div className="border-muted-200 inline-flex gap-0.5 rounded-lg border p-0.5">
              {ingredients.map((ingredient, index) => (
                <Link
                  key={index}
                  href={`/ingredients/details/${ingredient.guid}`}
                >
                  <Tag>{titleCase(ingredient.name)}</Tag>
                </Link>
              ))}
            </div>
          );
        },
      },
      {
        accessorKey: "pairing_percent",
        header: "Pairing %",
        cell: (info) => `${info.getValue<number>()?.toFixed(2)}%`,
      },
      {
        accessorKey: "change",
        header: "Change",
        cell: (info) => {
          const change = info.getValue<number>();
          const variant = getVariantForPairingChange(change);
          return (
            <div className="flex items-center">
              <Tag size="xs" variant={variant.type} className="w-fit">
                {variant.icon}
                {variant.value?.toFixed(2)}%
              </Tag>
            </div>
          );
        },
      },
    ],
    [],
  );

  // Initialize table without sorting, filtering, or pagination
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Disable features we don't need in this view
    enableSorting: false,
    enableFilters: false,
    enableColumnResizing: false,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Pairing Trends</Heading>
          <Info
            tooltipId="pairing-trends-tooltip"
            content="Most popular (used) pairing ingredients trends in last quarter."
          />
        </div>
        <Link href="/ingredients/pairing-trends">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="p-4 pb-0">
        <EnhancedTable
          table={table}
          isLoading={isLoading}
          withBorder={false}
          // Making it simpler, without all the advanced features
        />
      </div>
    </Card>
  );
};

export default PairingTrends;
