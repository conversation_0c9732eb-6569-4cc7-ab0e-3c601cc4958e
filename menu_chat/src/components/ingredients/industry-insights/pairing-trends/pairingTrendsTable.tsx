import { useState, useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  // createColumnHelper,
  FilterFn,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import Tag from "@/components/base/tag";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import { pairingTrends } from "@/api/ingredients";

import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import titleCase from "voca/title_case";

// Define the tag variant types
// type TagVariant = "greenOutline" | "redOutline" | "blueOutline" | "neutral" | "blue" | "red" | "green" | "yellow" | "violet" | "white" | undefined;

import { getVariantForChange } from "@/components/utils/helpers";
import Pagination from "@/components/table/pagination";

// Define the pairing data type

import Link from "next/link";


const PairingTrendsTable = () => {
  const { data: session } = useSession();

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [pagination, setPagination] = useState({
    pageIndex: page - 1, //initial page index
    pageSize: pageSize, //default page size
  });

  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");

  const { data: pairing_trends, isLoading }  = useQuery({
    queryFn: () => pairingTrends({auth: session?.user.authorization as string, sorting, pagination, search}),
    queryKey: ["pairing_trends",sorting, pagination, search],
    enabled: !!session?.user?.authorization,
  });

  // Sample data for pairings
  const data = useMemo<Pairing[]>(() => {
    if (!pairing_trends?.data) return [];
    return pairing_trends.data.data;
  }, [pairing_trends]);

  // Create column helper
  // const columnHelper = createColumnHelper<Pairing>();

  // Define columns for the table
  const columns = useMemo<ColumnDef<Pairing>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: info => {
        const {ingredient_a_name, ingredient_a_guid, ingredient_b_name, ingredient_b_guid } = info.row.original
        const ingredients = [
          {name: ingredient_a_name, guid: ingredient_a_guid },{name: ingredient_b_name, guid: ingredient_b_guid} ];

        return (
          <div className="inline-flex gap-0.5 rounded-lg border border-neutral-200 p-0.5">
            {ingredients.map((ingredient, index) => (
              <Link key={index} href={`/ingredients/details/${ingredient.guid}`}>
                <Tag>{titleCase(ingredient.name)}</Tag>
              </Link>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: 'category',
      header: 'Category',
      cell: info => <Tag variant="white">{titleCase(info.getValue<string>())}</Tag>,
    },
    {
      accessorKey: 'pairing_percent',
      header: 'Pairing %',
      cell: info => `${info.getValue<number>()?.toFixed(2)}%`,
    },
    {
      accessorKey: 'change',
      header: 'Pairing Change',
      cell: info => {
        const change = info.getValue<number>();
        const variant = getVariantForChange(change)
        return (
          <Tag variant={variant.type}>
            {variant.icon}
            {variant.value?.toFixed(2)}%
          </Tag>
        );
      },
    },
  ], []);

  // Custom filter function
  const fuzzyFilter: FilterFn<Pairing> = (row, columnId, value) => {
    // If no filter value, return all rows
    if (!value || typeof value !== 'string') return true;
    
    const rowValue = row.getValue(columnId);
    
    // For ingredient arrays
    if (Array.isArray(rowValue) && columnId === 'ingredients') {
      return rowValue.some(ingredient => 
        (ingredient as string).toLowerCase().includes(value.toLowerCase())
      );
    }
    
    // For string values
    if (typeof rowValue === 'string') {
      return rowValue.toLowerCase().includes(value.toLowerCase());
    }
    
    // For complex objects
    if (rowValue && typeof rowValue === 'object') {
      return false; // We don't search in pairingChange object
    }
    
    return false;
  };

  // Initialize table
  const table = useReactTable({
    data,
    columns,
    state: {
      pagination,
      sorting,
      globalFilter,
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    manualSorting: true,
    manualPagination: true,
    manualFiltering: true,
    rowCount: pairing_trends?.data?.total_records,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    globalFilterFn: fuzzyFilter,
  });

  // Handler functions
  const handleSearch = (value: string) => {
    setSearch(value);
    setGlobalFilter(value);
  };

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
    // Add additional filtering logic based on filters if needed
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
    // Add custom sorting logic if needed
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
    setPagination({
      pageIndex: newPage,
      pageSize: pageSize,
    })
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPageSize(pageSize);
    setPagination({
      pageIndex: page,
      pageSize: pageSize,
    })
  };


  // Empty state content
  const emptyState = (
    <div className="text-center py-6">
      <p className="text-gray-500">No pairing trends found</p>
      <p className="text-sm text-gray-400">Try adjusting your search or filters</p>
    </div>
  );

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={handleSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
        table={table}
        tableName="pairing_trends"
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        {/* Using the enhanced table component */}
        <EnhancedTable 
          table={table}
          withBorder
          isLoading={isLoading}
          emptyState={emptyState}
        />
        <Pagination
          currentPage={page}
          perPage={pageSize}
          totalResults={pairing_trends?.data?.total_records}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
};

export default PairingTrendsTable;
