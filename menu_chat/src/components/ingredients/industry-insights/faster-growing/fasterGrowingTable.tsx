import { useState, useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  ColumnResizeMode,
  FilterFn,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import Tag from "@/components/base/tag";

import Favorite from "@/components/base/favorite";
import IngredientsFilters from "@/components/ingredients/ingredientsFilters";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchFastestGrowingIngredients } from "@/api/ingredients";
import titleCase from "voca/title_case";
import { getVariantForMenuAdoption, getVariantForChange } from "@/components/utils/helpers";
import { useFavoritesContext } from "@/contexts/FavoritesContext";
import Link from "next/link";
import Pagination from "@/components/table/pagination";
import { INGREDIENTS_SORT_TRANSFORM } from "@/data/ingredients";


// Define the tag variant types
type TagVariant = "greenOutline" | "redOutline" | "blueOutline" | "neutral" | "blue" | "red" | "green" | "yellow" | "violet" | "white" | undefined;

// Define the data type for our table
interface Ingredient {
  guid: string;
  id: number;
  name: string;
  category: string;
  menuAdoption: {
    status: string;
    icon: React.ReactNode;
  };
  penetration: string;
  foodServiceChange: {
    value: string;
    icon: React.ReactNode;
    type: TagVariant;
  };
  foodServiceInOneYear: {
    value: string;
    icon: React.ReactNode;
    type: TagVariant;
  };
  penetrationInOneYear: string;
}


const FasterGrowingIngredientsTable = () => {
  const { data: session } = useSession();
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [pagination, setPagination] = useState({
    pageIndex: page - 1, //initial page index
    pageSize: pageSize, //default page size
  });
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  // Add column resizing state
  const [columnResizeMode] = useState<ColumnResizeMode>('onChange');
  const [columnSizing, setColumnSizing] = useState({});
  const {favorites, addFavorite, removeFavorite, isFavorite} = useFavoritesContext();

  // TODO: on init setSorting([{id: 'change', desc: true}]);
  const { data: fastest_growing_ingredients }  = useQuery({
    queryFn: () => {
      return fetchFastestGrowingIngredients({auth: session?.user.authorization as string, sorting, pagination, search, filters})
    },
    queryKey: ["fastest_growing_ingredients", sorting, pagination, search, filters],
    enabled: !!session?.user?.authorization,
  });

  const data = useMemo(() => {
    if (!fastest_growing_ingredients?.data) return [];
    return fastest_growing_ingredients.data || [];
  }, [fastest_growing_ingredients]);

  // Create a column helper to define column configuration
  // const columnHelper = createColumnHelper<Ingredient>();

  // Define columns for TanStack React Table
  const columns = useMemo<({ accessorKey: string } & ColumnDef<Ingredient>)[]>(
    () => [
      {
        id: 'favorite',
        accessorKey: 'guid',
        header: '',
        cell: (info) => {
          const guid = info.getValue<string>()
          return <Favorite
            checked={isFavorite(favorites, guid)}
            onChange={(e)=>{
              if (e.target.checked) {
                addFavorite(guid)
              }
              else {
                removeFavorite(guid)
              }
            }} />
        },
        enableSorting: false,
        size: 20,
        minSize: 20,
      },
      {
        accessorKey: 'name',
        header: 'Name',
        cell: info => {
          const {guid} = info.row.original
          return (
            <Link href={`/ingredients/details/${guid}`}>
              <Tag>
                {titleCase(info.getValue<string>())}
              </Tag>
            </Link>
          )
        },
      },
      {
        accessorKey: 'category',
        header: 'Category',
        cell: info => <Tag variant="white">{titleCase(info.getValue<string>())}</Tag>,
      },
      {
        accessorKey: 'menu_adoption',
        header: 'Menu Adoption',
        cell: info => {
          const menuAdoption = titleCase(info.getValue<string>());
          const color = getVariantForMenuAdoption(menuAdoption)
          return (
            <Tag variant={color}>
              {menuAdoption}
            </Tag>
          );
        },
      },
      {
        accessorKey: 'pen_rate',
        header: 'Penetration',
        cell: info => (`${info.getValue<number>() || 0}%`)
      },
      {
        accessorKey: 'change_1y',
        header: 'FoodService Growth',
        cell: info => {
          const value = info.getValue<number>();
          const {type, icon }  = getVariantForChange(value);

          return (
            <Tag variant={type} >
              {icon}
              {value?.toFixed(2) || 0.0}%
            </Tag>
          );
        },
      }
    ],
    [
      favorites, addFavorite, isFavorite, removeFavorite
    ]
  );

  // Custom global filter function
  const fuzzyFilter: FilterFn<Ingredient> = (row, columnId, value) => {
    // If no filter value, return all rows
    if (!value || typeof value !== 'string') return true;

    const rowValue = row.getValue(columnId);

    // Check if the value is a string and contains the filter value
    if (typeof rowValue === 'string') {
      return rowValue.toLowerCase().includes(value.toLowerCase());
    }

    // For complex objects like menuAdoption, attempt to match against status
    if (rowValue && typeof rowValue === 'object' && 'status' in rowValue) {
      return (rowValue.status as string).toLowerCase().includes(value.toLowerCase());
    }

    // For other cases, don't filter
    return true;
  };


  // Initialize the table with column resizing
  const table = useReactTable({
    data,
    columns,
    state: {
      pagination,
      sorting,
      globalFilter,
      columnSizing,
    },
    enableGlobalFilter: true,
    enableColumnResizing: true,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    manualSorting: true,
    manualPagination: true,
    manualFiltering: true,
    rowCount: fastest_growing_ingredients?.total_records,
    onGlobalFilterChange: setGlobalFilter,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode,
    defaultColumn: {
      size: 150,
      minSize: 50,
      maxSize: 500,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    globalFilterFn: fuzzyFilter,
  });

  // Handler functions
  const handleSearch = (value: string) => {
    setSearch(value);
    setGlobalFilter(value);
  };

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(() => newFilters);
  };

  const handleSortChange = (newSort: keyof typeof INGREDIENTS_SORT_TRANSFORM) => {
    setSorting(() => INGREDIENTS_SORT_TRANSFORM[newSort])
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setPagination({
      pageIndex: newPage,
      pageSize: pageSize,
    })
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPageSize(pageSize);
    setPagination({
      pageIndex: page,
      pageSize: pageSize,
    })
  };

  // Empty state content
  const emptyState = (
    <div className="text-center py-6">
      <p className="text-gray-500">No ingredients found</p>
      <p className="text-sm text-gray-400">Try adjusting your search or filters</p>
    </div>
  );

  return (
    <div className="py-3">
      <IngredientsFilters
        search={search}
        onSearch={handleSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
        table={table}
        tableName="fastest_growing_ingredients"
        columns={columns.map(column => column.accessorKey)}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        {/* Using the enhanced table component */}
        <EnhancedTable
          table={table}
          withBorder
          enableResizing
          emptyState={emptyState}
        />
        <Pagination
          currentPage={page}
          perPage={pageSize}
          totalResults={fastest_growing_ingredients?.total_records}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
      </div>
      {/* Optional: Add a note about resizable columns */}
      <div className="mt-2 text-xs text-gray-500">
        <span>Tip: You can resize columns by dragging the edge of each column header.</span>
      </div>
    </div>
  );
};

export default FasterGrowingIngredientsTable;
