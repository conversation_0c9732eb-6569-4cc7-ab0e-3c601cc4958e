import Link from "next/link";
import { useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";
import {
  useReactTable,
  getCoreRowModel,
  // createColumnHelper,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import titleCase from "voca/title_case";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import Tag from "@/components/base/tag";
import {
  IconCircleUpright,
  // IconDecrease,
  // IconCircle,
  // IconCircleFilled,
  // IconCircleRight,
} from "@/components/icons";
import { useQuery } from "@tanstack/react-query";
import { fetchFastestGrowingIngredients } from "@/api/ingredients";
import { useSession } from "next-auth/react";
import {
  getVariantForMenuAdoption,
  getVariantForRate,
} from "@/components/utils/helpers";

interface FastestGrowingIngredientsProps {
  className?: string;
}

// Define the ingredient data type
interface FastestGrowingIngredient {
  guid: string;
  name: string;
  menu_adoption: string;
  penetration: number;
  change: number;
  change_1y: number
}

const FastestGrowingIngredients = ({
  className,
}: FastestGrowingIngredientsProps) => {
  const { data: session } = useSession();
  const [pagination] = useState({
    pageIndex: 0, //initial page index
    pageSize: 5, //default page size
  });
  const [sorting] = useState<SortingState>([{ id: "change", desc: true }]);

  const { data: fastest_growing_ingredients, isLoading } = useQuery({
    queryFn: () =>
      fetchFastestGrowingIngredients({
        auth: session?.user.authorization as string,
        pagination,
        sorting,
      }),
    queryKey: ["fastest_growing_ingredients"],
    enabled: !!session?.user?.authorization,
  });

  // Create a column helper
  // const columnHelper = createColumnHelper<FastestGrowingIngredient>();

  // Define columns for the table
  const columns = useMemo<ColumnDef<FastestGrowingIngredient>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Name",
        cell: (info) => {
          const { guid } = info.row.original;
          return (
            <Link href={`/ingredients/details/${guid}`}>
              <Tag label={titleCase(info.getValue<string>())} truncateAt={20} />
            </Link>
          );
        },
      },
      {
        accessorKey: "menu_adoption",
        header: "Menu Adoption",
        cell: (info) => {
          const menuAdoption = titleCase(info.getValue<string>());
          const color = getVariantForMenuAdoption(menuAdoption);
          return (
            <div className="flex items-center">
              <Tag size="xs" variant={color} className="w-fit">
                <IconCircleUpright size={16} />
                {menuAdoption}
              </Tag>
            </div>
          );
        },
      },
      {
        accessorKey: "pen_rate",
        header: "Penetration",
        cell: (info) => `${info.getValue<number>()?.toFixed(2)}%`,
      },
      {
        accessorKey: "change_1y",
        header: "Change",
        cell: (info) => {
          const change = info.getValue<number>();
          const { type, icon } = getVariantForRate(change);
          return (
            <div className="flex items-center">
              <Tag size="xs" variant={type} className="w-fit">
                {icon}
                {change?.toFixed(2)}%
              </Tag>
            </div>
          );
        },
      },
    ],
    [],
  );

  // Create table data
  const data = useMemo(() => {
    if (!fastest_growing_ingredients?.data) return [];
    return fastest_growing_ingredients.data?.slice(0, 5) ?? [];
  }, [fastest_growing_ingredients]);

  // Initialize table
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Disable features we don't need in this view
    enableSorting: false,
    enableFilters: false,
    enableColumnResizing: false,
  });

  // Empty state
  const emptyState = (
    <div className="py-4 text-center">
      <p className="text-gray-500">No fastest growing ingredients found</p>
    </div>
  );

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Fastest Growing Ingredients</Heading>
          <Info
            tooltipId="faster-growing-tooltip"
            content="Ingredients that had highest penetration growth in the selected period."
          />
        </div>
        <Link href="/ingredients/fastest-growing-ingredients">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="p-4 pb-0">
        <EnhancedTable
          table={table}
          isLoading={isLoading}
          emptyState={emptyState}
        />
      </div>
    </Card>
  );
};

export default FastestGrowingIngredients;
