import Button from "@/components/base/button";
import Input from "@/components/base/input";
import { IconDownload, IconClose, IconSearch, IconCircleUp, IconCircleDown } from "@/components/icons";
import FilterDropdown from "@/components/base/filterDropdown";
import SortDropdown from "@/components/base/sortDropdown";
import { INGREDIENTS_FILTER, RAW_INGREDIENTS_SORT } from "@/data/ingredients";
import { Table as TanStackTable } from "@tanstack/react-table";
import { exportTableToCSV } from "@/lib/exportCsv";

interface IngredientsFiltersProps<T> {
  search: string;
  onSearch: (value: string) => void;
  filters: string[];
  onFilter: (value: string[]) => void;
  sort: string;
  onSort: (value: string) => void;
  table?: TanStackTable<T>;
  tableName?: string;
  columns?: string[];
}

const IngredientsFilters = <T,>({
  search,
  onSearch,
  filters,
  onFilter,
  sort,
  onSort,
  table,
  tableName,
  columns,
}: IngredientsFiltersProps<T>) => {

  const filterOptions = columns
    ? INGREDIENTS_FILTER.filter((option) => columns.includes(option.key))
    : INGREDIENTS_FILTER;

  const rawSortOptions = columns
    ? RAW_INGREDIENTS_SORT.filter((option) => columns.includes(option.key))
    : RAW_INGREDIENTS_SORT;

  const sortOptions: { label: React.ReactNode; value: string }[] = [];

  rawSortOptions.forEach(option => {
    if (sortOptions.length) {
      sortOptions.push({ label: "", value: "divider" });
    }

    sortOptions.push({ label: option.label, value: "heading" });

    sortOptions.push({
      label: (
        <>
          <IconCircleUp size={20} className="text-neutral-500" /> Highest
        </>
      ),
      value: `${option.value}-highest`,
    });

    sortOptions.push({
      label: (
        <>
          <IconCircleDown size={20} className="text-neutral-500" /> Lowest
        </>
      ),
      value: `${option.value}-lowest`,
    });
  });

  return (
    <>
      <div className="flex items-center justify-between gap-3 pt-3 pb-6">
        <Button variant="primary" size="sm" onClick={() => exportTableToCSV(table, tableName)}>
          <IconDownload size={20} />
          Export
        </Button>
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => onSearch(e.target.value)}
          />
          <FilterDropdown
            selectedValue={filters}
            options={filterOptions}
            onSelect={(value) => onFilter(value)}
          />
          <SortDropdown
            selectedValue={sort}
            options={sortOptions}
            onSelect={(value) => onSort(value)}
          />
        </div>
      </div>
      {filters.length > 0 && (
        <div className="mb-3 flex items-center justify-between gap-2">
          <div className="flex items-center gap-1.5">
            {filters.map((filter) => (
              <span
                key={filter}
                className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
              >
                {filter}
                <button
                  type="button"
                  className="flex size-4 items-center justify-center"
                  onClick={() => onFilter(filters.filter((f) => f !== filter))}
                >
                  <IconClose
                    size={16}
                    className="text-muted-900 cursor-pointer"
                  />
                </button>
              </span>
            ))}
          </div>
          <Button variant="ghost" size="sm" onClick={() => onFilter([])}>
            Reset
          </Button>
        </div>
      )}
    </>
  );
};

export default IngredientsFilters;
