import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import { IconReport, IconCircleUpright } from "@/components/icons";
import { AnimatePresence, motion } from "motion/react";

interface DataSourcesReportsProps {
  className?: string;
  count?: number;
  hide?: boolean;
}

// TODO: Replace with actual data
const reportData = [
  {
    title: "2025 Specialty Food Industry Outlook Report",
    source: "Specialty Food Association",
    link: "https://menudata-reports.s3.us-west-2.amazonaws.com/2025_SF_Outlook_Report.pdf",
  },
  {
    title: "Top Restaurant Trends for 2025",
    source: "Kantar",
    link: "https://menudata-reports.s3.us-west-2.amazonaws.com/10+Kantar+Restaurant+Trends+for+2025.pptx",
  },
  {
    title: "NRA State of the Restaurant Industry",
    source: "National Restaurant Association",
    link: "https://menudata-reports.s3.us-west-2.amazonaws.com/2025-NRA-State-of-the-Restaurant-Industry.pdf",
  },
  {
    title: "James Beard Independent Restaurant Industry Report 2025",
    source: "James Beard Foundation",
    link: "https://menudata-reports.s3.us-west-2.amazonaws.com/JBF+Independent+Restaurant+Industry+Report+2025_vFinal_hq.pdf",
  },
];

const DataSourcesReports = ({
  className,
  count = 4,
  hide = false,
}: DataSourcesReportsProps) => {
  return (
    <AnimatePresence initial={false} mode="wait">
      {!hide && (
        <motion.div
          layout
          key="data-sources-reports"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className={twMerge(className)}
        >
          <div className="mb-4 flex items-center gap-3">
            <IconReport className="text-neutral-600" />
            <Heading level={4}>Data Sources & Reports</Heading>
          </div>
          <div className={`grid grid-cols-${count} gap-5`}>
            {reportData.slice(0, count).map((item, idx) => (
              <div
                key={idx}
                className="bg-muted-100 flex flex-col justify-between rounded-lg p-3.5"
              >
                <Heading level={4} className="relative mb-2 pr-7 text-balance">
                  <a
                    href={item.link}
                    target="_blank"
                    className="peer hover:underline"
                  >
                    {item.title}
                  </a>
                  <IconCircleUpright className="absolute top-0 right-0 hidden text-yellow-800 peer-hover:block" />
                </Heading>
                <Paragraph
                  size="xs"
                  className="font-semibold tracking-[-0.3px] text-neutral-700"
                >
                  {item.source}
                </Paragraph>
              </div>
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DataSourcesReports;
