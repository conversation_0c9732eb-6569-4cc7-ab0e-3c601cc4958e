import React from "react";
import ReportsResourcesCard from "../reports-resources/reportsResourcesCard";

export default function AllReportsResources() {
  return (
    <div className="grid grid-cols-4 gap-2 px-4 py-3 md:grid-cols-3 lg:grid-cols-4">
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Instagram User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Facebook User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Instagram User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Facebook User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Instagram User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Facebook User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Instagram User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Facebook User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Instagram User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Facebook User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Instagram User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
      <ReportsResourcesCard
        imageSrc="/assets/images/<EMAIL>"
        imageAlt="Facebook User"
        title="Report Name"
        description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
        date="20/12/2025"
      />
    </div>
  );
}
