import { useState } from "react";
import { IconDelete } from "@/components/icons";
import Button from "@/components/base/button";
import Paragraph from "@/components/base/paragraph";
import Card from "@/components/base/card";
import { twMerge } from "tailwind-merge";

interface AllLatestConversationsProps {
  className?: string;
}

interface Conversation {
  id: string;
  title: string;
  promptCount: number;
  date: string;
}

const AllLatestConversations = ({ className }: AllLatestConversationsProps) => {
  const [conversations] = useState<Conversation[]>([
    {
      id: "1",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "2",
      title: "Chicken strips — foodservice analysis",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "3",
      title: "Popular American desserts in Portland",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "4",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "5",
      title: "Chicken strips — foodservice analysis",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "6",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "7",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "8",
      title: "Chicken strips — foodservice analysis",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "9",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "10",
      title: "Chicken strips — foodservice analysis",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "11",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "12",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "13",
      title: "Chicken strips — foodservice analysis",
      promptCount: 3,
      date: "20/12/2025",
    },
  ]);

  return (
    <Card className={twMerge(className)}>
      {/* Conversation List */}
      <div className="px-4 pb-1">
        {conversations.map((conversation) => (
          <div
            key={conversation.id}
            className="group !border-b-muted-500/30 hover:border-muted-100 hover:bg-muted-500/5 flex cursor-pointer items-center justify-between border-[1px] !border-y-[0.5px] border-transparent px-1 py-3 hover:rounded-lg hover:border-[1px]"
          >
            {/* Left side - Title */}
            <div className="flex-1/3">
              <Paragraph size="base" className="font-medium text-neutral-900">
                {conversation.title}
              </Paragraph>
            </div>

            {/* Middle - Prompt count */}
            <div className="flex-1 text-center">
              <Paragraph size="sm" className="font-medium text-neutral-900">
                {conversation.promptCount} prompts
              </Paragraph>
            </div>

            {/* Right side - Date or Action button */}
            <div className="h-6 flex-1 text-right">
              <Paragraph
                size="sm"
                className="font-medium text-neutral-900 group-hover:hidden"
              >
                {conversation.date}
              </Paragraph>
              <Button
                variant="tertiary"
                size="xs"
                className="border-muted-100 hidden border-[0.5px] font-medium text-neutral-900 shadow-md group-hover:inline-flex"
              >
                <IconDelete size={16} className="text-red-700" />
                Delete
              </Button>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default AllLatestConversations;
