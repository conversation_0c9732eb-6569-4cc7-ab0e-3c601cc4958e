import { useState } from "react";
import { IconMore, IconCir<PERSON><PERSON><PERSON>right } from "@/components/icons";
import Button from "@/components/base/button";
import Heading from "../base/heading";
import Paragraph from "../base/paragraph";
import Card, { CardHeader } from "../base/card";
import Link from "next/link";
import Dropdown from "../base/dropdown";
import { twMerge } from "tailwind-merge";
import { CHART_DROPDOWN } from "@/data/ingredients";

interface LatestConversationsProps {
  className?: string;
}

interface Conversation {
  id: string;
  title: string;
  promptCount: number;
  date: string;
}

const LatestConversations = ({ className }: LatestConversationsProps) => {
  const [selected, setSelected] = useState<string>("");

  const [conversations] = useState<Conversation[]>([
    {
      id: "1",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "2",
      title: "Chicken strips — foodservice analysis",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "3",
      title: "Popular American desserts in Portland",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "4",
      title: "Fast growing ingredients and dishes in United States",
      promptCount: 3,
      date: "20/12/2025",
    },
    {
      id: "5",
      title: "Chicken strips — foodservice analysis",
      promptCount: 3,
      date: "20/12/2025",
    },
  ]);

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Latest Conversations with MenuData Bot</Heading>
        </div>
        <div className="flex items-center gap-1">
          <Link href="/conversations-reports/all-latest-conversations">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => setSelected(value)}
          >
            <Button variant="ghost" size="xs" isSquared={true}>
              <IconMore size={16} className="text-neutral-600" />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>

      {/* Conversation List */}
      <div className="px-4 pt-4 pb-1">
        {conversations.map((conversation) => (
          <div
            key={conversation.id}
            className="group !border-b-muted-500/30 hover:border-muted-100 hover:bg-muted-500/5 flex cursor-pointer items-center justify-between border-[1px] !border-y-[0.5px] border-transparent px-1 py-3 hover:rounded-lg hover:border-[1px]"
          >
            {/* Left side - Title */}
            <div className="flex-1/3">
              <Paragraph size="base" className="font-medium text-neutral-900">
                {conversation.title}
              </Paragraph>
            </div>

            {/* Middle - Prompt count */}
            <div className="flex-1 text-center">
              <Paragraph size="sm" className="font-medium text-neutral-900">
                {conversation.promptCount} prompts
              </Paragraph>
            </div>

            {/* Right side - Date or Action button */}
            <div className="h-6 flex-1 text-right">
              <Paragraph
                size="sm"
                className="font-medium text-neutral-900 group-hover:hidden"
              >
                {conversation.date}
              </Paragraph>
              <Button
                variant="secondary"
                size="xs"
                className="hidden bg-yellow-500/25 text-neutral-700 group-hover:inline-flex"
              >
                View Details
                <IconCircleUpright size={16} className="text-yellow-800" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default LatestConversations;
