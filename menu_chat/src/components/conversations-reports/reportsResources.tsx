import { useState } from "react";
import { IconMore } from "@/components/icons";
import Button from "@/components/base/button";
import Heading from "../base/heading";
import Card, { CardHeader } from "../base/card";
import Link from "next/link";
import Dropdown from "../base/dropdown";
import { twMerge } from "tailwind-merge";
import { CHART_DROPDOWN } from "@/data/ingredients";
import ReportsResourcesCard from "./reports-resources/reportsResourcesCard";

interface ReportsResourcesProps {
  className?: string;
}

const ReportsResources = ({ className }: ReportsResourcesProps) => {
  const [selected, setSelected] = useState<string>("");

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Reports & Resources</Heading>
        </div>
        <div className="flex items-center gap-1">
          <Link href="/conversations-reports/all-reports-resources">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => setSelected(value)}
          >
            <Button variant="ghost" size="xs" isSquared={true}>
              <IconMore size={16} className="text-neutral-600" />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="grid grid-cols-4 gap-2 px-4 py-3 md:grid-cols-3 lg:grid-cols-4">
        <ReportsResourcesCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="Instagram User"
          title="Report Name"
          description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
          date="20/12/2025"
        />
        <ReportsResourcesCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="Facebook User"
          title="Report Name"
          description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
          date="20/12/2025"
        />
        <ReportsResourcesCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="Instagram User"
          title="Report Name"
          description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
          date="20/12/2025"
        />
        <ReportsResourcesCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="Facebook User"
          title="Report Name"
          description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
          date="20/12/2025"
        />
        <ReportsResourcesCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="Instagram User"
          title="Report Name"
          description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
          date="20/12/2025"
        />
        <ReportsResourcesCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="Facebook User"
          title="Report Name"
          description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
          date="20/12/2025"
        />
        <ReportsResourcesCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="Instagram User"
          title="Report Name"
          description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
          date="20/12/2025"
        />
        <ReportsResourcesCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="Facebook User"
          title="Report Name"
          description="Report Description in one or two rows. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed"
          date="20/12/2025"
        />
      </div>
    </Card>
  );
};

export default ReportsResources;
