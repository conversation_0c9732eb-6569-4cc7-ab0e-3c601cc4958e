import Button from "@/components/base/button";
import Card from "@/components/base/card";
import Paragraph from "@/components/base/paragraph";
import { IconCircleUpright } from "@/components/icons";
import { SafeImage } from "@/components/utils/helpers";

interface ReportsResourcesCardProps {
  imageSrc: string;
  imageAlt: string;
  title: string;
  description: string;
  date: string;
  className?: string;
  imageClassName?: string;
}

const ReportsResourcesCard = ({
  imageSrc,
  imageAlt,
  title,
  description,
  date,
  className = "",
  imageClassName = "h-[140px] w-full rounded-lg object-cover",
}: ReportsResourcesCardProps) => {
  return (
    <Card className="bg-transparent">
      <div className={`flex flex-col gap-2 p-2 ${className}`}>
        <SafeImage
          src={imageSrc}
          alt={imageAlt}
          width={100}
          height={140}
          className={imageClassName}
        />
        <div className="px-2 py-1">
          <Paragraph
            size="sm"
            className="flex items-center gap-1 font-semibold"
          >
            <span className="inline-block max-w-[70%] truncate text-neutral-700">
              {title}
            </span>
          </Paragraph>
        </div>
        <div className="flex w-full flex-col gap-1">
          <div className="flex items-center justify-between px-1 py-0.5">
            <Paragraph className="line-clamp-2 pl-1 text-xs font-medium text-neutral-900">
              {description}
            </Paragraph>
          </div>
          <div className="flex items-center justify-between px-1 py-0.5">
            <Paragraph className="font-archivo pl-1 text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
              {date}
            </Paragraph>
            <Button
              className="group gap-0 p-1 hover:gap-1"
              variant="secondary"
              size="xs"
              onClick={() => {}}
            >
              {/* Text (hidden until hover) */}
              <span className="max-w-0 overflow-hidden text-neutral-700 opacity-0 group-hover:max-w-xs group-hover:opacity-100">
                Read Now
              </span>
              {/* Icon */}
              <IconCircleUpright
                size={16}
                className="cursor-pointer text-yellow-800"
              />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ReportsResourcesCard;
