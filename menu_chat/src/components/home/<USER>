import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import { IconIncrease, IconCircleUpright } from "@/components/icons";
import Image from "next/image";
import { config } from "@/config";

interface InsignHighlightsProps {
  className?: string;
}

const InsightHighlights = ({ className = "" }: InsignHighlightsProps) => {
  return (
    <section className={twMerge("grid grid-cols-3 gap-5 py-4", className)}>
      <div className="border-muted-200 rounded-lg border bg-white p-6">
        <div className="mb-2 flex flex-wrap items-center justify-between gap-2">
          <Heading level={5}>Top Social Media Trend</Heading>
          <Tag variant="greenOutline">
            <IconIncrease size={16} />
            +20%
          </Tag>
        </div>
        <Tag className="mb-2" size="md">
          Dubai Chocolate
        </Tag>
        <div className="flex items-end justify-between">
          <div>
            <Heading level={1}>400K</Heading>
            <Paragraph
              size="sm"
              className="font-archivo text-[11px] font-semibold tracking-[0.2px] text-neutral-700 uppercase"
            >
              Social Mentions
            </Paragraph>
          </div>
          <Image
            src="/assets/images/<EMAIL>"
            alt="chocolates"
            width={80}
            height={80}
            className="size-20 min-w-20 rounded-lg object-cover"
          />
        </div>
      </div>
      <div className="border-muted-200 rounded-lg border bg-white p-6">
        <div className="mb-2 flex flex-wrap items-center justify-between gap-2">
          <Heading level={5}>News Article</Heading>
          <IconCircleUpright size={24} className="text-yellow-800" />
        </div>
        <div className="bg-muted-100 mt-6 flex flex-col gap-2 rounded-lg p-3.5">
          <Heading level={4} className="underline">
            <a
              target="_blank"
              href="https://www.meatpoultry.com/articles/31864-taking-the-jab-the-glp-1-effect-on-food-choices"
            >
              The GLP-1 effect on food choices
            </a>
          </Heading>
          <Paragraph size="sm" className="font-medium text-neutral-700">
            Prescription GLP-1 drugs are relatively new...
          </Paragraph>
        </div>
      </div>
      <div className="border-muted-200 rounded-lg border bg-white p-6">
        <div className="mb-2 flex flex-wrap items-center justify-between gap-2">
          <Heading level={5}>Trending in Foodservice</Heading>
          <Tag variant="greenOutline">
            <IconIncrease size={16} />
            +4%
          </Tag>
        </div>
        <a
          href={config.getIngredientUrl("ca1049aa-6373-45ba-b6b8-ec98887800d1")}
        >
          <Tag className="mb-2 underline" size="md">
            Cold Brew
          </Tag>
        </a>
        <div className="flex items-end justify-between">
          <div>
            <Heading level={1} className="flex items-end gap-1.5">
              9.54 <span className="text-2xl text-neutral-600">%</span>
            </Heading>
            <Paragraph
              size="sm"
              className="font-archivo text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase"
            >
              of menus
            </Paragraph>
          </div>
          <Image
            src="/assets/images/cold_brew.avif"
            alt="cold brew"
            width={80}
            height={80}
            className="size-20 min-w-20 rounded-lg object-cover"
          />
        </div>
      </div>
    </section>
  );
};

export default InsightHighlights;
