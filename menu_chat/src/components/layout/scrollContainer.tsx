import { twMerge } from "tailwind-merge";

interface ScrollContainerProps {
  children: React.ReactNode;
  className?: string;
}

const ScrollContainer = ({
  children,
  className = "",
}: ScrollContainerProps) => {
  return (
    <div
      className={twMerge(
        "h-[calc(100vh-93px)] overflow-y-auto px-8 pb-4",
        className,
      )}
    >
      {children}
    </div>
  );
};

export default ScrollContainer;
