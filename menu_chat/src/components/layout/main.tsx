import { twMerge } from "tailwind-merge";

interface MainProps {
  children: React.ReactNode;
  className?: string;
}

const Main = ({ children, className }: MainProps) => {
  return (
    <main
      className={twMerge(
        "flex h-screen min-h-screen flex-col items-stretch overflow-hidden p-2.5",
        className,
      )}
    >
      <div className="bg-muted-50 h-full flex-1 overflow-hidden rounded-lg">
        {children}
      </div>
    </main>
  );
};

export default Main;
