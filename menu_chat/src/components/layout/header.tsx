import { twMerge } from "tailwind-merge";

interface HeaderProps {
  children: React.ReactNode;
  className?: string;
  withBorder?: boolean;
}

const Header = ({
  children,
  className = "",
  withBorder = true,
}: HeaderProps) => {
  return (
    <header
      className={twMerge(
        "flex flex-col items-start justify-between gap-2 px-8 py-4 lg:flex-row lg:items-center",
        withBorder && "border-muted-200 border-b",
        className,
      )}
    >
      {children}
    </header>
  );
};

export default Header;
