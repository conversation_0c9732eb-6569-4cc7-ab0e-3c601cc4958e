import Head from "next/head";
import { twMerge } from "tailwind-merge";
import { archivo, workSans } from "@/styles/fonts";
import { useAppStore } from "@/store/useAppStore";
import { motion } from "motion/react";

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
  withSideNav?: boolean;
}

const Layout = ({ children, className, withSideNav = false }: LayoutProps) => {
  const { navCollapsed: collapsed } = useAppStore();

  return (
    <>
      <Head>
        <link rel="icon" href="/assets/images/favicon.png" />
      </Head>
      <motion.div
        initial={false}
        animate={{
          paddingLeft: collapsed
            ? "var(--nav-collapsed-width)"
            : "var(--nav-width)",
        }}
        transition={{ duration: 0.2, ease: "easeInOut" }}
        className={twMerge(
          archivo.variable,
          workSans.variable,
          "font-worksans min-h-screen overflow-y-hidden",
          !withSideNav && `!pl-0`,
          className,
        )}
      >
        {children}
      </motion.div>
    </>
  );
};

export default Layout;
