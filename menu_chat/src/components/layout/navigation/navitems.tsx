import { twMerge } from "tailwind-merge";
import Link from "next/link";
import Tooltip from "@/components/base/tooltip";
import { useAppStore } from "@/store/useAppStore";
import { usePathname } from "next/navigation";

type NavItem = {
  label: string;
  icon?: React.ReactNode;
  iconSolid?: React.ReactNode;
  link?: string;
  onClick?: () => void;
};

interface NavItemsProps {
  navItems: NavItem[];
  withActive?: boolean;
}

const NavItems = ({ navItems, withActive }: NavItemsProps) => {
  const { navCollapsed: collapsed } = useAppStore();
  const pathname = usePathname();

  return (
    <ul className="flex flex-col gap-1">
      {navItems.map((item, index) => {
        return item.label === "divider" ? (
          <li key={index} className={twMerge("bg-muted-200 h-[1px] w-full")} />
        ) : (
          <li key={index} className="w-full">
            <Tooltip
              tooltipId={item.label}
              content={item.label}
              place="right"
              className="!py-2 !text-sm"
              wrapperClassName="block w-full"
              disabled={!collapsed}
              offset={5}
              withArrow={false}
            >
              {item.link ? (
                (() => {
                  const normalize = (str: string) => str.replace(/\/+$/, "");

                  const currentPath = normalize(pathname || "");
                  const itemPath = normalize(item.link || "");

                  const isActive =
                    item.label === "Ingredients"
                      ? currentPath.startsWith("/ingredients")
                      : item.label === "Menu Items"
                        ? currentPath.startsWith("/menu-items")
                        : item.label === "Retail"
                          ? currentPath.startsWith("/retail")
                          : item.link === "/"
                            ? currentPath === "" || currentPath === "/"
                            : currentPath === itemPath ||
                              currentPath.startsWith(itemPath + "/");

                  return (
                    <Link
                      href={item.link}
                      className={twMerge(
                        "group hover:text-muted-900 flex h-12 items-center gap-2 rounded-lg p-3 text-neutral-600 transition-colors duration-300 hover:bg-[#AFAC8326]",
                        collapsed && "justify-center",
                        isActive && "bg-[#AFAC8326] text-neutral-900",
                      )}
                    >
                      {withActive && isActive ? item.iconSolid : item.icon}
                      {!collapsed && (
                        <span className="font-archivo font-medium text-nowrap text-neutral-700 group-hover:text-neutral-900">
                          {item.label}
                        </span>
                      )}
                    </Link>
                  );
                })()
              ) : (
                <button
                  className={twMerge(
                    "group hover:text-muted-900 flex h-12 w-full cursor-pointer items-center gap-2 rounded-lg p-3 text-neutral-600 transition-colors duration-300 hover:bg-[#AFAC8326]",
                    collapsed && "justify-center",
                  )}
                  onClick={item.onClick}
                >
                  {item.icon}
                  {!collapsed && (
                    <span className="font-archivo font-medium text-nowrap text-neutral-700 group-hover:text-neutral-900">
                      {item.label}
                    </span>
                  )}
                </button>
              )}
            </Tooltip>
          </li>
        );
      })}
    </ul>
  );
};

export default NavItems;
