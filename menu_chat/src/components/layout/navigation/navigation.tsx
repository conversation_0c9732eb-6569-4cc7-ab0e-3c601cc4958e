import { twMerge } from "tailwind-merge";
import Button from "@/components/base/button";
import { IconLeft, IconRight } from "@/components/icons";
import NavItems from "@/components/layout/navigation/navitems";
import { useAppStore } from "@/store/useAppStore";
import { motion, AnimatePresence } from "motion/react";
import { NAV_TOP, NAV_BOTTOM } from "@/data/navigation";
import LogoColor from "@/components/base/logoColor";
import SearchButton from "@/components/search/searchButton";
import SearchModal from "@/components/search/searchModal";
interface NavigationProps {
  className?: string;
}

const Navigation = ({ className }: NavigationProps) => {
  const { navCollapsed: collapsed, setNavCollapsed } = useAppStore();

  const handleNavCollapse = () => {
    setNavCollapsed(true);
  };

  const handleNavExpand = () => {
    setNavCollapsed(false);
  };

  return (
    <>
      <motion.nav
        initial={false}
        animate={{
          width: collapsed ? "var(--nav-collapsed-width)" : "var(--nav-width)",
        }}
        transition={{ duration: 0.2, ease: "easeInOut" }}
        className={twMerge(
          `bg-muted-100 fixed top-0 bottom-0 left-0 z-40 flex flex-col justify-between overflow-hidden px-2.5 py-4`,
          className,
        )}
      >
        <div>
          <div className="relative mb-9 flex items-center justify-between gap-2 p-2">
            <LogoColor className="text-muted-900" collapse={collapsed} />
            <div className="absolute top-0 right-0 bottom-0 my-auto flex items-center gap-2">
              <AnimatePresence mode="wait">
                {!collapsed && (
                  <motion.div
                    key="collapse-btn"
                    initial={{ opacity: 1 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <Button
                      variant="ghost"
                      size="xs"
                      className="hover:bg-muted-200 active:bg-muted-300"
                      isSquared
                      onClick={handleNavCollapse}
                    >
                      <IconLeft size={16} className="text-neutral-600" />
                    </Button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
          <div className="mb-2">
            <SearchButton />
          </div>
          <NavItems navItems={NAV_TOP} withActive />
        </div>
        <div>
          <AnimatePresence mode="wait">
            {collapsed && (
              <motion.div
                key="expand-btn"
                initial={{ opacity: 1 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="border-muted-200 mb-1 flex items-center justify-between border-b p-3"
              >
                <Button
                  variant="ghost"
                  size="xs"
                  className="hover:bg-muted-200 active:bg-muted-300"
                  isSquared
                  onClick={handleNavExpand}
                >
                  <IconRight size={16} className="text-neutral-600" />
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
          <NavItems navItems={NAV_BOTTOM} />
        </div>
      </motion.nav>
      <SearchModal />
    </>
  );
};

export default Navigation;
