import React, { useState, useRef, useEffect, useCallback } from "react";
import { twMerge } from "tailwind-merge";
import { motion, AnimatePresence } from "framer-motion";
import { createPopper, Instance, Placement } from "@popperjs/core";
import { IconSort } from "@/components/icons";

interface SortOption {
  label: string | React.ReactNode;
  value: string;
  link?: string;
}

interface SortDropdownProps {
  options: SortOption[];
  onSelect: (value: string) => void;
  children?: React.ReactNode;
  className?: string;
  selectedValue?: string | number;
  placement?: Placement;
}

const usePopper = (
  referenceElement: HTMLElement | null,
  popperElement: HTMLElement | null,
  placement: Placement = "bottom-start",
) => {
  const [popperInstance, setPopperInstance] = useState<Instance | null>(null);

  useEffect(() => {
    if (referenceElement && popperElement) {
      const instance = createPopper(referenceElement, popperElement, {
        placement,
        modifiers: [
          {
            name: "offset",
            options: {
              offset: [0, 4],
            },
          },
          {
            name: "preventOverflow",
            options: {
              padding: 8,
            },
          },
        ],
      });
      setPopperInstance(instance);

      return () => {
        instance.destroy();
      };
    }
  }, [referenceElement, popperElement, placement]);

  return popperInstance;
};

const SortDropdown = ({
  options,
  onSelect,
  children,
  className = "",
  selectedValue,
  placement = "bottom-start",
}: SortDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const popperInstance = usePopper(
    triggerRef.current,
    dropdownRef.current,
    placement,
  );

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (
      triggerRef.current &&
      !triggerRef.current.contains(event.target as Node) &&
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handleClickOutside]);

  const toggleDropdown = useCallback(() => {
    setIsOpen((prevIsOpen) => !prevIsOpen);
  }, []);

  useEffect(() => {
    if (popperInstance) {
      popperInstance.update();
    }
  }, [isOpen, popperInstance]);

  const handleSelect = useCallback(
    (value: string) => {
      onSelect(value);
      setIsOpen(false);
    },
    [onSelect],
  );

  return (
    <div className={twMerge("relative inline-block flex-none", className)}>
      <div
        className="flex cursor-pointer items-center gap-1"
        ref={triggerRef}
        onClick={toggleDropdown}
      >
        {children ? (
          children
        ) : (
          <button
            role="button"
            tabIndex={0}
            aria-haspopup="listbox"
            aria-expanded={isOpen}
            className={twMerge(
              "border-muted-200 font-worksans flex h-8 cursor-pointer items-center gap-1 rounded-md border bg-white px-2 text-sm font-medium text-neutral-900 transition-colors duration-200 hover:cursor-pointer hover:border-[#EBEBE9] hover:bg-[#EBEBE9] active:border-[#E2E2DF] active:bg-[#E2E2DF]",
              selectedValue &&
                "bg-muted-100 !border-muted-300 hover:bg-muted-100 active:bg-muted-100",
            )}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") toggleDropdown();
            }}
          >
            <IconSort size={16} className="text-neutral-600" />
            Sort
          </button>
        )}
      </div>
      <div
        ref={dropdownRef}
        className="absolute z-40"
        style={{
          visibility: isOpen ? "visible" : "hidden",
          opacity: isOpen ? 1 : 0,
        }}
      >
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="max-w-72 min-w-64 rounded-lg border border-[#F0EDE6] bg-white p-1 shadow-[0px_12px_16px_-2px_rgba(33,31,28,0.08),_0px_1px_6px_0px_rgba(33,31,28,0.06)]"
            >
              <div className="max-h-[420px] overflow-y-auto">
                {options.map((option, index) => (
                  <div
                    key={`${option.value}-${index}`}
                    className={twMerge(
                      "hover:bg-muted-500/10 flex cursor-pointer items-center gap-2 rounded-sm px-3 py-2 transition-colors duration-300",
                      option.value === selectedValue && "bg-muted-500/20",
                      option.value === "divider" && "pointer-events-none px-0",
                      option.value === "heading" && "pointer-events-none py-1",
                    )}
                    onClick={() => handleSelect(option.value)}
                  >
                    {option.value === "divider" ? (
                      <div className="bg-muted-100 h-px w-full" />
                    ) : option.value === "heading" ? (
                      <span className="flex items-center text-xs font-semibold tracking-[-0.036px] text-neutral-600">
                        {option.label}
                      </span>
                    ) : (
                      <span className="flex items-center gap-2 text-sm font-medium text-neutral-900">
                        {option.label}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default SortDropdown;
