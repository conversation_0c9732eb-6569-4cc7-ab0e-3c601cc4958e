import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";

interface SwitchProps {
  checked?: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  className?: string;
}

const Switch = ({ checked = false, onChange, label, className }: SwitchProps) => {
  const [isChecked, setIsChecked] = useState(checked);

  useEffect(() => setIsChecked(checked), [checked]);

  const toggle = () => {
    const next = !isChecked;
    setIsChecked(next);
    onChange(next);
  };

  return (
    <div className={twMerge("inline-flex items-center gap-2 whitespace-nowrap shrink-0 select-none", className)}>
      {label && (
        <span className="text-sm text-neutral-700 leading-none">
          {label}
        </span>
      )}

      <button
        type="button"
        role="switch"
        aria-checked={isChecked}
        onClick={toggle}
        onKeyDown={(e) => {
          if (e.key === " " || e.key === "Enter") {
            e.preventDefault();
            toggle();
          }
        }}
        className={twMerge(
          "relative inline-flex h-6 w-12 min-w-[48px] flex-none items-center rounded-full p-0.5 transition-[background-color] duration-200",
          isChecked ? "bg-blue-500" : "bg-gray-300"
        )}
      >
        <span
          className={twMerge(
            "pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow-md transform transition-transform duration-200 will-change-transform",
            isChecked ? "translate-x-6" : "translate-x-0"
          )}
        />
      </button>
    </div>
  );
};

export default Switch;
