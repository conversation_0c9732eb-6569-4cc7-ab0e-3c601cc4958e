import React, { useRef, useState, useEffect } from "react";
import { createPopper, Placement } from "@popperjs/core";
import { createPortal } from "react-dom";
import { twMerge } from "tailwind-merge";

interface HoverCardProps {
  content: React.ReactNode;
  placement?: Placement;
  className?: string;
  children: React.ReactNode;
  offset?: [number, number];
}

const HoverCard = ({
  content,
  placement = "top",
  className,
  children,
  offset = [0, 10],
}: HoverCardProps) => {
  const [visible, setVisible] = useState(false);
  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Safe for client-only rendering
    setPortalNode(document.getElementById("hovercard-portal"));
  }, []);

  useEffect(() => {
    if (visible && triggerRef.current && cardRef.current) {
      createPopper(triggerRef.current, cardRef.current, {
        placement,
        modifiers: [
          {
            name: "offset",
            options: {
              offset,
            },
          },
        ],
      });
    }
  }, [visible, placement, offset]);

  const card = (
    <div
      ref={cardRef}
      onMouseEnter={() => setVisible(true)}
      onMouseLeave={() => setVisible(false)}
      className={twMerge(
        "border-muted-100 z-50 max-w-52 rounded-lg border bg-white p-1.5 text-sm shadow-[0px_4px_8px_-2px_rgba(33,31,28,0.08),0px_1px_4px_0px_rgba(33,31,28,0.06)] transition-opacity duration-150",
        className,
      )}
    >
      {content}
    </div>
  );

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
        className="inline-block"
      >
        {children}
      </div>
      {visible && portalNode && createPortal(card, portalNode)}
    </>
  );
};

export default HoverCard;
