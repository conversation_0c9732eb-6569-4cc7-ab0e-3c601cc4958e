import React from "react";
import { twMerge } from "tailwind-merge";

interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  disabled?: boolean;
  className?: string;
}

const Checkbox = ({
  label,
  disabled = false,
  className = "",
  ...props
}: CheckboxProps) => {
  return (
    <label
      className={twMerge(
        "flex cursor-pointer items-center gap-2",
        disabled && "cursor-not-allowed opacity-40",
        className,
      )}
    >
      <input
        type="checkbox"
        disabled={disabled}
        className="peer absolute h-0 w-0 opacity-0 focus-visible:outline-none"
        {...props}
      />
      <span className="border-muted-200 grid h-5 w-5 place-content-center rounded-sm border bg-white transition-colors peer-checked:border-yellow-600 peer-checked:bg-yellow-600 peer-focus:outline-2 peer-focus:outline-offset-2 peer-focus:outline-yellow-600 peer-checked:hover:bg-yellow-700">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="9"
          fill="none"
          viewBox="0 0 12 9"
          className="pointer-events-none"
        >
          <path
            stroke="#fff"
            strokeLinecap="square"
            strokeMiterlimit="16"
            strokeWidth="1.5"
            d="m2 5 2.18 2.18c.23.23.343.343.484.341.141-.002.252-.12.474-.355L10 2"
          />
        </svg>
      </span>
      {label && (
        <span className="text-base leading-none font-medium text-neutral-900">
          {label}
        </span>
      )}
    </label>
  );
};

export default Checkbox;
