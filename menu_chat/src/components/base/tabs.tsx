import React, { useState } from "react";
import { twMerge } from "tailwind-merge";
import { AnimatePresence, motion } from "motion/react";
import Button from "./button";

interface TabsProps {
  navigation: string[] | React.ReactNode[];
  children: React.ReactNode;
  onChange?: (index: number) => void;
}

interface TabContentProps {
  children: React.ReactNode;
  hidden?: boolean;
}

export const TabContent = ({ children }: TabContentProps) => {
  return children;
};

const Tabs = ({ navigation, children, onChange }: TabsProps) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (index: number) => {
    setActiveTab(index);
    onChange?.(index);
  };

  return (
    <div className="tabs relative flex h-full flex-1 flex-col">
      <div className="border-muted-100 mb-4 flex items-center gap-4 border-b">
        {navigation.map((label, index) => (
          <Button
            key={index}
            variant="tertiary"
            size="sm"
            onClick={() => handleTabChange(index)}
            className={twMerge(
              "rounded-none border-0 border-b-[3px] !bg-transparent px-1",
              activeTab === index
                ? "!border-yellow-800 text-neutral-900"
                : "!border-transparent text-neutral-600",
            )}
          >
            {label}
          </Button>
        ))}
      </div>
      <div className="relative flex h-full flex-1 flex-col overflow-y-auto">
        <AnimatePresence mode="wait" initial={false}>
          {React.Children.map(children, (child, index) =>
            activeTab === index && React.isValidElement(child) ? (
              <motion.div
                key={index}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="flex w-full flex-1 flex-col items-start [&>*]:w-full"
              >
                {child}
              </motion.div>
            ) : null,
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Tabs;
