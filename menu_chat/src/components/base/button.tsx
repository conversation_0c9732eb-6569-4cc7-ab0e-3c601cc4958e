import React from "react";
import { twMerge } from "tailwind-merge";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: "primary" | "secondary" | "tertiary" | "ghost" | "danger";
  size?: "xs" | "sm" | "md" | "lg";
  type?: "button" | "submit";
  disabled?: boolean;
  isSquared?: boolean;
  className?: string;
}

const Button = ({
  children,
  variant = "primary",
  size = "md",
  type = "button",
  disabled = false,
  isSquared = false,
  className = "",
  ...props
}: ButtonProps) => {
  // Base styles for buttons
  // focus:outline-2 focus:outline-offset-2 focus:outline-yellow-600
  const buttonStyles = {
    primary:
      "text-neutral-900 bg-yellow-400 hover:bg-yellow-500 active:bg-yellow-600",
    secondary:
      "bg-yellow-500/15 text-neutral-700 border border-muted-500/20 hover:bg-yellow-500/20 active:bg-yellow-500/35",
    tertiary:
      "bg-white text-neutral-700 border border-muted-200 hover:border-[#EBEBE9] hover:bg-[#EBEBE9] active:bg-[#E2E2DF] active:border-[#E2E2DF]",
    ghost:
      "text-neutral-700 bg-transparent hover:bg-[#EBEBE9] active:bg-[#E2E2DF]",
    danger: "text-white bg-red-600 hover:bg-red-700 active:bg-red-800",
  };

  const buttonSize = {
    xs: "rounded-sm px-2 py-0.5 text-sm h-6",
    sm: "rounded-md px-3 py-1 text-sm h-9",
    md: "rounded-md px-4 py-2.5 text-base font-semibold h-11",
    lg: "rounded-md text-base font-semibold px-4 py-3 h-12",
  };

  return (
    <button
      className={twMerge(
        "text-neutral-1000 font-worksans inline-flex items-center justify-center gap-2 bg-yellow-500 leading-[1.5] font-semibold tracking-[-0.0056em] transition-colors duration-200 hover:cursor-pointer disabled:cursor-not-allowed disabled:opacity-50",
        buttonStyles[variant],
        buttonSize[size],
        isSquared && size === "lg" && "size-12 px-3",
        isSquared && size === "md" && "size-11 px-2.5",
        isSquared && size === "sm" && "size-9 px-1",
        isSquared && size === "xs" && "size-6 px-0.5",
        className,
      )}
      {...props}
      type={type}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default Button;
