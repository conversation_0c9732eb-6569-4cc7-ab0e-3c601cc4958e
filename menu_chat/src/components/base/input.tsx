import React from "react";
import { twMerge } from "tailwind-merge";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  inputSize?: "sm" | "md" | "lg";
  className?: string;
  inputClassName?: string;
  icon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      disabled = false,
      inputSize = "md",
      className = "",
      inputClassName = "",
      icon,
      ...props
    },
    ref,
  ) => {
    // Define styles based on the inputSize prop
    const sizeStyles = {
      sm: "text-sm px-2 py-1 h-8",
      md: "text-base px-3.5 py-2 h-12",
      lg: "text-base px-3.5 py-2 h-14 rounded-lg",
    };

    return (
      <div
        className={twMerge("relative flex w-full flex-col gap-1", className)}
      >
        {label && (
          <label className="text-sm font-medium text-neutral-900">
            {label}
          </label>
        )}
        {icon && (
          <span className="absolute top-0 right-1.5 bottom-0 m-auto flex items-center text-neutral-600">
            {icon}
          </span>
        )}
        <input
          ref={ref}
          disabled={disabled}
          className={twMerge(
            "border-muted-200 font-worksans block w-full rounded-md border bg-white font-medium text-neutral-900 transition-colors placeholder:text-neutral-600 focus:border-yellow-600 focus:bg-white focus:outline-none",
            error && "border-red-500",
            disabled && "cursor-not-allowed opacity-40",
            icon && "!pr-7",
            sizeStyles[inputSize],
            inputClassName,
          )}
          {...props}
        />
        {error && <p className="text-xs font-medium text-red-900">{error}</p>}
      </div>
    );
  },
);

Input.displayName = "Input";

export default Input;
