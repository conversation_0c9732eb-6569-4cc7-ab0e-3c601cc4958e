import React from "react";
import { twMerge } from "tailwind-merge";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  inputSize?: "sm" | "md" | "lg";
  className?: string;
  inputClassName?: string;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      disabled = false,
      inputSize = "md",
      className = "",
      inputClassName = "",
      icon,
      iconPosition = "right",
      ...props
    },
    ref,
  ) => {
    // Define styles based on the inputSize prop
    const sizeStyles = {
      sm: "text-sm px-2 py-1 h-8",
      md: "text-base px-3.5 py-2 h-12",
      lg: "text-base p-4 h-14 rounded-lg",
    };

    // Icon positioning and padding based on size
    const iconPositions = {
      left: { sm: "left-2", md: "left-3", lg: "left-4" },
      right: { sm: "right-2", md: "right-3", lg: "right-4" },
    };

    const iconPadding = {
      left: { sm: "!pl-7", md: "!pl-10", lg: "!pl-12" },
      right: { sm: "!pr-7", md: "!pr-10", lg: "!pr-12" },
    };

    return (
      <div
        className={twMerge("h- relative flex w-full flex-col gap-1", className)}
      >
        {label && (
          <label className="text-sm font-medium text-neutral-900">
            {label}
          </label>
        )}
        {icon && (
          <span
            className={twMerge(
              "absolute top-0 bottom-0 m-auto flex items-center text-neutral-600",
              iconPositions[iconPosition][inputSize],
            )}
          >
            {icon}
          </span>
        )}
        <input
          ref={ref}
          disabled={disabled}
          className={twMerge(
            "border-muted-200 font-worksans hover:bg-muted-200 block w-full rounded-md border bg-white font-medium tracking-[-0.03em] text-neutral-900 transition-colors placeholder:text-neutral-600 hover:cursor-pointer focus:border-yellow-600 focus:bg-white focus:outline-none",
            error && "border-red-500",
            disabled && "cursor-not-allowed opacity-40",
            icon && iconPadding[iconPosition][inputSize],
            sizeStyles[inputSize],
            inputClassName,
          )}
          {...props}
        />
        {error && <p className="text-xs font-medium text-red-900">{error}</p>}
      </div>
    );
  },
);

Input.displayName = "Input";

export default Input;
