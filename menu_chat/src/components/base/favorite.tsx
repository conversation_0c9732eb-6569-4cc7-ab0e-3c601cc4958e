import { twMerge } from "tailwind-merge";
import { IconStar, IconStarSolid } from "@/components/icons";

interface FavoriteProps extends React.InputHTMLAttributes<HTMLInputElement> {
  disabled?: boolean;
  className?: string;
}

const Favorite = ({
  disabled = false,
  className = "",
  ...props
}: FavoriteProps) => {
  return (
    <label
      className={twMerge(
        "relative flex size-6 cursor-pointer items-center justify-center",
        disabled && "cursor-not-allowed opacity-40",
        className,
      )}
      tabIndex={disabled ? -1 : 0}
    >
      <input
        type="checkbox"
        disabled={disabled}
        className="peer absolute h-0 w-0 opacity-0 focus-visible:outline-none"
        {...props}
      />
      <span className="absolute top-0 right-0 bottom-0 left-0 m-auto size-4 text-neutral-600 peer-checked:hidden">
        <IconStar size={16} />
      </span>
      <span className="absolute top-0 right-0 bottom-0 left-0 z-10 m-auto hidden size-4 text-yellow-800 peer-checked:block">
        <IconStarSolid size={16} />
      </span>
    </label>
  );
};

export default Favorite;
