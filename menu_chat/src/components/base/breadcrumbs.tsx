import React from "react";
import Link from "next/link";
import { twMerge } from "tailwind-merge";
import { IconArrowRight } from "@/components/icons";

interface BreadcrumbItem {
  label: string;
  icon?: React.ReactNode;
  link?: string;
}

interface BreadcrumbsProps extends React.HTMLAttributes<HTMLElement> {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumbs = ({ items, className = "", ...props }: BreadcrumbsProps) => {
  return (
    <nav
      aria-label="Breadcrumb"
      className={twMerge(
        "text-muted-foreground flex items-center text-sm",
        className,
      )}
      {...props}
    >
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        return (
          <div key={index} className="flex items-center text-sm font-bold">
            {!isLast && item.link ? (
              <Link
                href={item.link}
                className="flex items-center gap-1 text-neutral-600 hover:underline"
              >
                {item.icon && (
                  <span className="relative top-0.5">{item.icon}</span>
                )}
                {item.label}
              </Link>
            ) : (
              <span className="flex items-center gap-1 text-neutral-700">
                {item.label}
              </span>
            )}
            {!isLast && (
              <span className="mx-1 flex items-center gap-1 text-neutral-600">
                <IconArrowRight size={12} />
              </span>
            )}
          </div>
        );
      })}
    </nav>
  );
};

export default Breadcrumbs;
