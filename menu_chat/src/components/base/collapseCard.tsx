import { twMerge } from "tailwind-merge";
import { useState } from "react";
import Paragraph from "@/components/base/paragraph";
import { IconCircleUpright } from "@/components/icons";

interface CollapseCardProps {
  text?: string;
}

const CollapseCard = ({ text = "" }: CollapseCardProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleCollapse = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="bg-muted-100 rounded-lg p-3">
      <Paragraph
        size="sm"
        className={twMerge(
          "relative cursor-pointer overflow-y-hidden pr-5 font-medium text-neutral-800",
        )}
        onClick={toggleCollapse}
      >
        <span className={isOpen ? "" : "line-clamp-3"}>{text}</span>
        <IconCircleUpright
          size={16}
          className="absolute top-0 right-0 min-w-5 text-yellow-800"
        />
      </Paragraph>
    </div>
  );
};

export default CollapseCard;
