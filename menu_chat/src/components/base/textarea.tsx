import React, { useEffect, useRef } from "react";
import { twMerge } from "tailwind-merge";

interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  className?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      label,
      error,
      disabled = false,
      maxLength,
      className = "",
      value,
      onChange,
      ...props
    },
    ref,
  ) => {
    const internalRef = useRef<HTMLTextAreaElement | null>(null);
    const combinedRef = (node: HTMLTextAreaElement) => {
      if (typeof ref === "function") ref(node);
      else if (ref)
        (ref as React.MutableRefObject<HTMLTextAreaElement | null>).current =
          node;
      internalRef.current = node;
    };

    const resizeTextarea = () => {
      const el = internalRef.current;
      if (el) {
        el.style.height = "auto";
        el.style.height = `${el.scrollHeight}px`;
      }
    };

    useEffect(() => {
      resizeTextarea();
    }, [value]);

    return (
      <div className="relative flex w-full flex-col gap-1">
        {label && (
          <label className="text-sm font-medium text-neutral-900">
            {label}
          </label>
        )}
        <textarea
          ref={combinedRef}
          disabled={disabled}
          maxLength={maxLength}
          value={value}
          onChange={(e) => {
            resizeTextarea();
            onChange?.(e);
          }}
          className={twMerge(
            "border-muted-200 font-worksans block w-full resize-none overflow-hidden rounded-md border bg-white p-3 font-medium text-neutral-900 placeholder:text-neutral-600 focus:border-yellow-600 focus:outline-none",
            error && "border-red-500",
            disabled && "cursor-not-allowed opacity-40",
            className,
          )}
          {...props}
        />
        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    );
  },
);

Textarea.displayName = "Textarea";

export default Textarea;
