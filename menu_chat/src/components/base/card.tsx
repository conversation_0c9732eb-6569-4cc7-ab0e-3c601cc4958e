import { twMerge } from "tailwind-merge";

interface Cardprops {
  children: React.ReactNode;
  className?: string;
}

interface CardHeaderProps {
  children: React.ReactNode;
}

const Card = ({ children, className, ...props }: Cardprops) => {
  return (
    <div
      className={twMerge(
        "border-muted-100 rounded-lg border bg-white p-1",
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
};

const CardHeader = ({ children }: CardHeaderProps) => {
  return (
    <div className="border-muted-100 flex items-center justify-between border-b px-4 py-3">
      {children}
    </div>
  );
};

export default Card;
export { CardHeader };
