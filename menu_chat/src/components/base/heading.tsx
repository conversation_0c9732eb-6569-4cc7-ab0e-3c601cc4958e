import React from "react";
import { twMerge } from "tailwind-merge";

interface HeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
}

const Heading = ({
  children,
  level = 1,
  className = "",
  ...props
}: HeadingProps) => {
  const Tag = `h${level}` as React.ElementType;

  // Base styles for headings
  const baseStyles = {
    1: "text-[2.75rem] leading-[1.09]", // 44px / 1.09
    2: "text-[2rem] leading-[1.125]", // 32px / 1.125
    3: "text-[1.5rem] leading-[1.33]", // 24px / 1.33
    4: "text-[1rem] leading-[1.5]", // 16px / 1.5
    5: "text-[0.875rem] leading-[1.43]", // 14px / 1.43
    6: "text-xs leading-[1.25]", // 12px / 1.25
  };

  return (
    <Tag
      className={twMerge(
        "font-archivo font-semibold tracking-[-0.003em]",
        baseStyles[level],
        className,
      )}
      {...props}
    >
      {children}
    </Tag>
  );
};

export default Heading;
