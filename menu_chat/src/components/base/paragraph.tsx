import React from "react";
import { twMerge } from "tailwind-merge";

interface ParagraphProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
  size?: "xs" | "sm" | "base" | "lg";
  className?: string;
}

const Paragraph = ({
  children,
  size = "base",
  className = "",
  ...props
}: ParagraphProps) => {
  // Base styles for paragraphs
  const fontSize = {
    xs: "text-xs",
    sm: "text-sm",
    base: "text-base",
    lg: "text-lg",
  };

  return (
    <p
      className={twMerge(
        "font-worksans leading-[1.5] tracking-[-0.3px]",
        fontSize[size],
        className,
      )}
      {...props}
    >
      {children}
    </p>
  );
};

export default Paragraph;
