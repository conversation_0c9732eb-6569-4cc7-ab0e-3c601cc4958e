import { twMerge } from "tailwind-merge";

interface LogoProps {
  collapse?: boolean;
  className?: string;
}

const Logo = ({ collapse, className }: LogoProps) => {
  return (
    <div className={twMerge("flex-center inline-flex items-center", className)}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={collapse ? "28" : "139"}
        height="28"
        fill="none"
      >
        <path
          fill="currentColor"
          d="M19.05 4.462H5.786l.488-2.388h13.264l-.488 2.388ZM6.745 8.803H3.821l.569-2.388h2.924l-.569 2.388ZM23.841 4.462h-2.993l.498-2.388h2.994l-.499 2.388ZM8.583 12.927H2.074l.476-2.605h6.51l-.477 2.605ZM7.443 23.13H20.63l-.485 2.388H6.877l.566-2.388ZM19.6 18.788h2.994l-.499 2.388h-2.993l.498-2.388ZM2.573 23.13h2.994l-.5 2.388H2.075l.5-2.388Z"
        />
        <path
          fill="currentColor"
          fillRule="evenodd"
          d="M8.842 8.803H22.11l.486-2.388H9.409l-.567 2.388Zm1.965 4.124h9.344l.479-2.605h-9.344l-.48 2.605Zm-6.42 5.86h13.187l-.486 2.389H3.821l.567-2.388Zm11.223-4.123H6.266l-.48 2.604h9.264l.56-2.604Z"
          clipRule="evenodd"
        />
        <path
          fill="currentColor"
          d="M17.911 14.664h6.43l-.477 2.605h-6.509l.556-2.605Z"
        />
        {!collapse && (
          <path
            fill="currentColor"
            d="M127.321 11.557c1.044-1.064 2.297-1.596 3.761-1.596 1.15 0 2.14.364 2.966 1.091v-.91h2.947v10.71h-2.947v-.889c-.813.7-1.802 1.051-2.966 1.051-1.45 0-2.703-.546-3.761-1.637-1.042-1.09-1.564-2.384-1.564-3.88 0-1.549.522-2.862 1.564-3.94Zm1.179 3.94c0 .782.272 1.456.814 2.021.555.566 1.239.849 2.051.849.786 0 1.464-.283 2.033-.849.569-.579.853-1.253.853-2.02 0-.768-.284-1.442-.853-2.021-.569-.58-1.247-.869-2.033-.869-.812 0-1.496.29-2.051.869a2.822 2.822 0 0 0-.814 2.02ZM116.745 10.143h1.83V7.8l2.905-.828v3.172h3.109v2.526h-3.109v4.587a.88.88 0 0 0 .346.707c.23.189.474.297.731.324.271.013.434.02.488.02.46 0 1.023-.128 1.687-.384v2.829c-.773.215-1.49.323-2.154.323-.827 0-1.701-.236-2.622-.707-.921-.485-1.381-1.516-1.381-3.092V12.67h-1.83v-2.526ZM105.354 11.557c1.043-1.064 2.296-1.596 3.759-1.596 1.152 0 2.141.364 2.967 1.091v-.91h2.947v10.71h-2.947v-.889c-.812.7-1.801 1.051-2.967 1.051-1.45 0-2.703-.546-3.759-1.637-1.043-1.09-1.565-2.384-1.565-3.88 0-1.549.522-2.862 1.565-3.94Zm1.178 3.94c0 .782.271 1.456.813 2.021.555.566 1.24.849 2.053.849.785 0 1.463-.283 2.032-.849.569-.579.854-1.253.854-2.02 0-.768-.285-1.442-.854-2.021-.569-.58-1.247-.869-2.032-.869-.813 0-1.498.29-2.053.869a2.82 2.82 0 0 0-.813 2.02ZM89.786 20.873V6.687h5.385c2.018 0 3.733.688 5.142 2.062 1.422 1.374 2.14 3.05 2.154 5.031v.06c0 1.968-.705 3.631-2.114 4.992-1.408 1.36-3.136 2.04-5.182 2.04h-5.385Zm3.069-2.83h2.316c1.287 0 2.324-.403 3.11-1.212.798-.808 1.198-1.825 1.198-3.05 0-1.24-.4-2.257-1.198-3.052-.8-.808-1.837-1.212-3.11-1.212h-2.316v8.527ZM77.224 10.144h2.946v5.9c0 .674.204 1.22.61 1.637.42.417.928.626 1.524.626.719 0 1.294-.25 1.728-.747.447-.512.67-1.139.67-1.88v-5.536h2.947v10.71h-2.947v-.99c-.812.78-1.815 1.171-3.008 1.171-1.218 0-2.269-.384-3.149-1.152-.88-.781-1.321-1.812-1.321-3.091v-6.648ZM64.86 10.143h2.947v.99c.813-.781 1.815-1.172 3.007-1.172 1.22 0 2.27.39 3.15 1.172.88.768 1.321 1.792 1.321 3.071v6.648h-2.946v-5.9c0-.674-.21-1.22-.63-1.637a2.015 2.015 0 0 0-1.504-.626c-.718 0-1.3.256-1.748.768-.433.498-.65 1.118-.65 1.859v5.536H64.86v-10.71ZM53.859 11.557c1.084-1.064 2.384-1.596 3.902-1.596 1.314 0 2.479.418 3.495 1.253s1.66 1.886 1.93 3.152c.095.404.143.795.143 1.172 0 .364-.041.714-.122 1.05h-8.088c.162.594.48 1.052.955 1.375.474.31 1.036.465 1.687.465 1.03 0 1.876-.303 2.54-.91l2.56.728c-.42.835-1.117 1.509-2.093 2.02-.975.499-1.978.748-3.007.748-1.545 0-2.859-.539-3.943-1.616-1.07-1.092-1.605-2.392-1.605-3.9 0-1.55.548-2.863 1.646-3.94Zm1.26 2.809h5.222a2.586 2.586 0 0 0-.934-1.293c-.461-.35-1.016-.526-1.667-.526-.65 0-1.212.175-1.687.526-.46.35-.772.781-.934 1.293ZM38.62 6.708l3.496 11.7 3.495-11.7h4.775v14.145H47.44V9.86l-3.272 10.993h-4.105L36.812 9.92v10.932h-2.947V6.708h4.755Z"
          />
        )}
      </svg>
    </div>
  );
};

export default Logo;
