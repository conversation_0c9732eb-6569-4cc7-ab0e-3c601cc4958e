import React, { useState, useRef, useEffect, useCallback } from "react";
import { twMerge } from "tailwind-merge";
import { motion, AnimatePresence } from "framer-motion";
import { createPopper, Instance, Placement } from "@popperjs/core";
import { IconFilter, IconArrowLeft, IconArrowRight } from "@/components/icons";

interface FilterDropdownProps {
  options: FilterOption[];
  onSelect: (value: string[]) => void;
  children?: React.ReactNode;
  className?: string;
  selectedValue?: string[];
  placement?: Placement;
  showReset?: boolean;
}

const usePopper = (
  referenceElement: HTMLElement | null,
  popperElement: HTMLElement | null,
  placement: Placement = "bottom-start",
) => {
  const [popperInstance, setPopperInstance] = useState<Instance | null>(null);

  useEffect(() => {
    if (referenceElement && popperElement) {
      const instance = createPopper(referenceElement, popperElement, {
        placement,
        modifiers: [
          { name: "offset", options: { offset: [0, 4] } },
          { name: "preventOverflow", options: { padding: 8 } },
        ],
      });
      setPopperInstance(instance);

      return () => instance.destroy();
    }
  }, [referenceElement, popperElement, placement]);

  return popperInstance;
};

const FilterDropdown = ({
  options,
  onSelect,
  children,
  className = "",
  selectedValue = [],
  placement = "bottom-start",
  showReset = false
}: FilterDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeMenu, setActiveMenu] = useState<string>("main");

  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const popperInstance = usePopper(
    triggerRef.current,
    dropdownRef.current,
    placement,
  );

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (
      triggerRef.current &&
      !triggerRef.current.contains(event.target as Node) &&
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
      setActiveMenu("main");
    }
  }, []);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handleClickOutside]);

  const toggleDropdown = useCallback(() => {
    setIsOpen((prev) => !prev);
    setActiveMenu("main");
  }, []);

  useEffect(() => {
    if (popperInstance) {
      popperInstance.update();
    }
  }, [isOpen, popperInstance]);

  const handleOptionClick = useCallback((value: string) => {
    setActiveMenu(value);
  }, []);

  const handleSelect = useCallback(
    (value: string) => {
      let newSelected: string[];
      if (selectedValue.includes(value)) {
        newSelected = selectedValue.filter((v) => v !== value);
      } else {
        newSelected = [...selectedValue, value];
      }
      onSelect(newSelected);
    },
    [onSelect, selectedValue],
  );

  const activeFilters =
    options.find((option) => option.label === activeMenu)?.filters || [];

  return (
    <div className={twMerge("relative inline-block flex-none", className)}>
      <div
        className="flex cursor-pointer items-center gap-1"
        ref={triggerRef}
        onClick={toggleDropdown}
      >
        {children ? (
          children
        ) : (
          <button
            role="button"
            tabIndex={0}
            aria-haspopup="listbox"
            aria-expanded={isOpen}
            className={twMerge(
              "border-muted-200 font-worksans flex h-8 cursor-pointer items-center gap-1 rounded-md border bg-white px-2 text-sm font-medium text-neutral-900 transition-colors duration-200 hover:cursor-pointer hover:border-[#EBEBE9] hover:bg-[#EBEBE9] active:border-[#E2E2DF] active:bg-[#E2E2DF]",
              selectedValue.length > 0 &&
                "bg-muted-100 !border-muted-300 hover:bg-muted-100 active:bg-muted-100",
            )}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") toggleDropdown();
            }}
          >
            <IconFilter size={16} className="text-neutral-600" />
            Filter
            {selectedValue.length > 0 && (
              <span className="font-archivo border-muted-300 flex size-5 min-w-5 items-center justify-center rounded-xs border bg-yellow-300 text-sm font-semibold text-neutral-900">
                {selectedValue.length}
              </span>
            )}
          </button>
        )}
      </div>
      <div
        ref={dropdownRef}
        className="absolute z-40"
        style={{
          visibility: isOpen ? "visible" : "hidden",
          opacity: isOpen ? 1 : 0,
        }}
      >
        <AnimatePresence initial={false} mode="wait">
          {isOpen && (
            <motion.div
              key={activeMenu}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="max-w-72 min-w-64 rounded-lg border border-[#F0EDE6] bg-white p-1 shadow-[0px_12px_16px_-2px_rgba(33,31,28,0.08),_0px_1px_6px_0px_rgba(33,31,28,0.06)]"
            >
              {activeMenu === "main" ? (
                <div className="flex max-h-[420px] flex-col overflow-y-auto">
                  {options.map((option, index) => (
                    <div
                      key={index}
                      className={twMerge(
                        "group flex cursor-pointer items-center justify-between gap-2 rounded-sm px-3 py-2 transition-colors duration-300 hover:bg-neutral-500/10",
                      )}
                      onClick={() => handleOptionClick(option.label as string)}
                    >
                      <span className="flex items-center gap-2 text-sm font-medium text-neutral-900">
                        {option.label}
                      </span>
                      <IconArrowRight
                        size={16}
                        className="text-neutral-500 opacity-0 transition-opacity group-hover:opacity-100"
                      />
                    </div>
                  ))}
                  {showReset && (
                    <div
                      key="reset"
                      className={twMerge(
                        "group flex cursor-pointer items-center justify-center gap-2 rounded-sm px-3 py-2 transition-colors duration-300 hover:bg-neutral-500/10",
                      )}
                      onClick={() => onSelect([])}
                    >
                      <span className="flex items-center gap-2 text-sm font-medium text-neutral-900">
                        Reset
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex w-full flex-col gap-2 p-1 text-neutral-900">
                  <button
                    onClick={() => setActiveMenu("main")}
                    className="font-archivo flex cursor-pointer items-center gap-1 text-sm font-semibold text-neutral-700"
                  >
                    <IconArrowLeft size={16} className="text-neutral-500" />
                    Go Back
                  </button>
                  <div className="bg-muted-100 h-px w-full" />
                  <span className="filter-heading-label flex items-center px-3 py-1 text-xs font-semibold tracking-[-0.036px] text-neutral-600">
                    {activeMenu}
                  </span>
                  <div className="max-h-[420px] overflow-y-auto">
                    <div className="flex w-full flex-col">
                      {activeFilters.map(
                        (filter: { label: string; value: string }) => (
                          <div
                            key={filter.value}
                            className={twMerge(
                              "flex cursor-pointer items-center gap-2 rounded-sm px-3 py-2 transition-colors duration-300 hover:bg-neutral-500/10",
                              selectedValue.includes(filter.value) &&
                                "bg-neutral-500/10",
                            )}
                            onClick={() => handleSelect(filter.value)}
                          >
                            <span
                              className={twMerge(
                                "border-muted-200 grid h-5 w-5 place-content-center rounded-sm border bg-white transition-colors",
                                selectedValue.includes(filter.value) &&
                                  "border-yellow-600 bg-yellow-600",
                              )}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="12"
                                height="9"
                                fill="none"
                                viewBox="0 0 12 9"
                                className="pointer-events-none"
                              >
                                <path
                                  stroke="#fff"
                                  strokeLinecap="square"
                                  strokeMiterlimit="16"
                                  strokeWidth="1.5"
                                  d="m2 5 2.18 2.18c.23.23.343.343.484.341.141-.002.252-.12.474-.355L10 2"
                                />
                              </svg>
                            </span>
                            <span className="flex items-center gap-2 text-sm font-medium text-neutral-900">
                              {filter.label}
                            </span>
                          </div>
                        ),
                      )}
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default FilterDropdown;
