import { twMerge } from "tailwind-merge";

interface LogoProps {
  collapse?: boolean;
  className?: string;
}

const LogoColor = ({ collapse, className }: LogoProps) => {
  return (
    <div className={twMerge("flex-center inline-flex items-center", className)}>
      <svg
        version="1.2"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 169 32"
        width="139"
        height="28"
      >
        <defs>
          <linearGradient
            id="g1"
            x2="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="matrix(-12.993,17.682,-11.476,-8.433,24.619,7.468)"
          >
            <stop offset="0" stopColor="#fed660" />
            <stop offset="1" stopColor="#e8b020" />
          </linearGradient>
        </defs>
        <style>{`
		.s0 { fill: #ff3d00 } 
		.s1 { fill: url(#g1) } 
		.s2 { fill: #1e1e1e } 
	`}</style>
        <path className="s0" d="m22.7 5.2h-15.3l0.6-2.8h15.3z" />
        <path className="s0" d="m8.5 10.2h-3.4l0.7-2.7h3.4z" />
        <path className="s0" d="m28.3 5.2h-3.5l0.6-2.8h3.5z" />
        <path className="s0" d="m10.6 15h-7.5l0.6-3h7.5z" />
        <path className="s0" d="m9.3 26.9h15.3l-0.6 2.8h-15.3z" />
        <path className="s0" d="m23.4 21.8h3.4l-0.5 2.8h-3.5z" />
        <path className="s0" d="m3.7 26.9h3.5l-0.6 2.8h-3.5z" />
        <path
          fillRule="evenodd"
          className="s1"
          d="m11.6 7.5h15.2l-0.5 2.7h-15.4zm2.2 4.6h10.8l-0.5 3h-10.8zm-8 9.9h15.2l-0.5 2.7h-15.4zm12.4-1.7h-10.8l0.6-3.1h10.8z"
        />
        <path className="s0" d="m21.4 17.1h7.5l-0.6 3h-7.5z" />
        {!collapse && (
          <>
            <path
              className="s2"
              d="m155 13.4q1.9-1.8 4.4-1.8 2 0 3.4 1.3v-1.1h3.4v12.4h-3.4v-1q-1.4 1.2-3.4 1.2-2.5 0-4.4-1.9-1.8-1.9-1.8-4.5 0-2.7 1.8-4.6zm1.4 4.6q0 1.4 0.9 2.4 1 1 2.4 1 1.4 0 2.4-1 1-1 1-2.4 0-1.3-1-2.3-1-1-2.4-1-1.4 0-2.4 1-0.9 1-0.9 2.3z"
            />
            <path
              className="s2"
              d="m142.8 11.8h2.1v-2.7l3.4-1v3.7h3.6v2.9h-3.6v5.4q0 0.5 0.4 0.8 0.4 0.3 0.8 0.4 0.5 0 0.6 0 0.8 0 1.9-0.5l0.1 3.3q-1.4 0.4-2.5 0.4-1.5 0-3.1-0.8-1.6-0.9-1.6-3.6v-5.4h-2.1z"
            />
            <path
              className="s2"
              d="m129.7 13.4q1.8-1.8 4.3-1.8 2 0 3.4 1.3v-1.1h3.4v12.4h-3.4v-1q-1.4 1.2-3.4 1.2-2.5 0-4.3-1.9-1.8-1.9-1.8-4.5 0-2.7 1.8-4.6zm1.3 4.6q0 1.4 1 2.4 0.9 1 2.3 1 1.4 0 2.4-1 1-1 1-2.4 0-1.3-1-2.3-1-1-2.4-1-1.4 0-2.3 1-1 1-1 2.3z"
            />
            <path
              className="s2"
              d="m111.7 24.3v-16.5h6.2q3.5 0 5.9 2.4 2.5 2.4 2.5 5.8 0 0.1 0 0.1 0 3.4-2.4 5.8-2.5 2.4-6 2.4zm3.5-3.3h2.7q2.2 0 3.6-1.4 1.4-1.4 1.4-3.6 0-2.1-1.4-3.5-1.4-1.4-3.6-1.4h-2.7z"
            />
            <path
              className="s2"
              d="m97.2 11.8h3.4v6.9q0 1.1 0.7 1.9 0.7 0.7 1.7 0.7 1.3 0 2-0.9 0.8-0.9 0.8-2.2v-6.4h3.4v12.4h-3.4v-1.1q-1.4 1.4-3.5 1.4-2.1 0-3.6-1.4-1.5-1.3-1.5-3.6z"
            />
            <path
              className="s2"
              d="m82.9 11.8h3.4v1.2q1.4-1.4 3.4-1.4 2.2 0 3.7 1.4 1.5 1.3 1.5 3.5v7.7h-3.4v-6.8q0-1.2-0.7-1.9-0.7-0.7-1.8-0.7-1.2 0-2 0.9-0.7 0.8-0.7 2.1v6.4h-3.4z"
            />
            <path
              className="s2"
              d="m70.2 13.4q1.8-1.8 4.5-1.8 2.2 0 4 1.4 1.8 1.5 2.2 3.7 0.2 0.7 0.2 1.4 0 0.6-0.1 1.2h-9.4q0.3 1 1.1 1.6 0.8 0.5 2 0.5 1.7 0 2.9-1l3 0.8q-0.8 1.5-2.5 2.4-1.7 0.8-3.4 0.8-2.7 0-4.6-1.8-1.8-1.9-1.9-4.6 0-2.7 2-4.6zm1.4 3.3h6q-0.2-0.9-1-1.5-0.8-0.6-2-0.6-1.1 0-1.9 0.6-0.8 0.6-1.1 1.5z"
            />
            <path
              className="s2"
              d="m52.5 7.8l4.1 13.6 4-13.6h5.5v16.4h-3.4v-12.7l-3.7 12.7h-4.8l-3.7-12.7v12.7h-3.5v-16.4z"
            />
          </>
        )}
      </svg>
    </div>
  );
};

export default LogoColor;
