import { twMerge } from "tailwind-merge";
import Tooltip from "@/components/base/tooltip";
import { IconQuestion } from "@/components/icons";

interface InfoProps {
  content: string;
  className?: string;
  tooltipId: string;
}

const Info = ({ content, className, tooltipId }: InfoProps) => {
  return (
    <Tooltip tooltipId={tooltipId} content={content}>
      <span
        className={twMerge(
          "flex size-6 items-center justify-center text-neutral-600",
          className,
        )}
      >
        <IconQuestion size={16} />
      </span>
    </Tooltip>
  );
};

export default Info;
