import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import Tooltip from "@/components/base/tooltip";

interface TagProps {
  children?: React.ReactNode;
  className?: string;
  variant?:
    | "neutral"
    | "ghost"
    | "blue"
    | "blueOutline"
    | "red"
    | "redOutline"
    | "green"
    | "greenOutline"
    | "yellow"
    | "violet"
    | "white"
    | "grey"
    | "magenta"
    | "magentaOutline"
    | "salmon"
    | "salmonOutline"
    | "yellowOutline"
    | "restaurantXs"
    | "restaurantSm"
    | "restaurantMd";
  size?: "sm" | "md" | "xs";
  label?: string;
  truncateAt?: number;
  showTooltipOnTruncate?: boolean;
  tooltipPlace?: "top" | "bottom" | "left" | "right";
  tooltipId?: string;
  tooltipClassName?: string;
  onClick?: () => void;
}

const Tag = ({
  children,
  className,
  variant = "neutral",
  size = "sm",
  label,
  truncateAt,
  showTooltipOnTruncate = true,
  tooltipPlace = "top",
  tooltipId,
  tooltipClassName,
  onClick,
}: TagProps) => {
  const tagStyles = {
    neutral: "bg-muted-100 text-neutral-900 rounded-md !px-1.5",
    ghost: "bg-transparent text-muted-900 rounded-md",
    blue: "bg-blue-200 text-blue-1000 rounded",
    blueOutline: "bg-blue-50 text-blue-700 border border-blue-200 rounded",
    red: "bg-red-200 text-red-1000 rounded",
    redOutline: "bg-red-100 text-red-900 border border-red-200 rounded",
    green: "bg-green-200 text-green-1000 rounded",
    greenOutline: "bg-green-100 text-green-900 border border-green-200 rounded",
    yellow: "bg-yellow-300 text-yellow-1000 rounded",
    violet: "bg-violet-200 text-violet-1000 rounded",
    white: "bg-white text-muted-900 border border-muted-200 rounded-md",
    grey: "bg-muted-500/10 text-muted-900 border border-muted-300 rounded-md",
    magenta: "bg-[#F3DEF6] text-[#37352F] rounded",
    magentaOutline:
      "bg-[#FAEFFB] text-[#9B30A1] border border-[#DF9DE4] rounded",
    yellowOutline:
      "bg-yellow-100 text-yellow-900 border border-yellow-400 rounded",
    salmon: "bg-[#FFCFC6] text-[#37352F] rounded",
    salmonOutline:
      "bg-[#FFCFC6] text-[#37352F] border border-[#FF9985] rounded",
    restaurantXs:
      "h-5 rounded-md px-1 text-xs font-medium text-neutral-900 bg-muted-200 border-[1px] border-muted-300 leading-4",
    restaurantSm:
      "h-6 rounded-md px-1.5 text-md font-medium text-neutral-900 bg-muted-200 border-[1px] border-muted-300 leading-5",
    restaurantMd:
      "h-8 rounded-md px-3 py-1.5 text-md font-medium text-neutral-900 bg-muted-200 border-[1px] border-muted-300 leading-5",
  };

  const tagSize = {
    xs: "px-1 h-5 text-xs",
    sm: "px-1.5 h-6 text-xs",
    md: "px-2 h-8 text-sm",
  };

  // Detect viewport >= 1280px (Tailwind xl)
  const [isXlUp, setIsXlUp] = useState(true);
  useEffect(() => {
    if (typeof window === "undefined") return;
    const mql = window.matchMedia("(min-width: 1280px)");
    const handleChange = (e: MediaQueryListEvent) => setIsXlUp(e.matches);
    setIsXlUp(mql.matches);
    mql.addEventListener("change", handleChange);
    return () => mql.removeEventListener("change", handleChange);
  }, []);

  const effectiveTruncateAt =
    typeof truncateAt === "number" && !isXlUp ? truncateAt : undefined;

  const childrenIsString = typeof children === "string";
  const rawText =
    label ?? (childrenIsString ? (children as string) : undefined);
  const shouldTruncate =
    typeof effectiveTruncateAt === "number" &&
    rawText !== undefined &&
    rawText.length > effectiveTruncateAt;
  const displayText =
    shouldTruncate && rawText !== undefined
      ? `${rawText.slice(0, effectiveTruncateAt)}...`
      : rawText;

  const spanContent = (
    <span
      className={twMerge(
        "font-worksans inline-flex min-w-0 items-center gap-1 font-medium tracking-[-0.3px]",
        tagStyles[variant],
        tagSize[size],
        onClick && "cursor-pointer",
        className,
      )}
      onClick={onClick}
    >
      {label !== undefined ? (
        <>
          {children}
          <span className="truncate">{displayText}</span>
        </>
      ) : childrenIsString ? (
        <span className="truncate">{displayText}</span>
      ) : (
        children
      )}
    </span>
  );

  if (shouldTruncate && showTooltipOnTruncate && rawText) {
    return (
      <Tooltip
        tooltipId={tooltipId}
        content={rawText}
        contentNode={
          <span
            className={twMerge(
              "font-worksans inline-flex items-center font-medium tracking-[-0.3px] text-nowrap",
              tagStyles.neutral,
              tagSize[size],
            )}
          >
            {rawText}
          </span>
        }
        place={tooltipPlace}
        className={tooltipClassName ?? "!-mt-1.5 !bg-white !p-2"}
      >
        {spanContent}
      </Tooltip>
    );
  }

  return spanContent;
};
export default Tag;
