import { twMerge } from "tailwind-merge";

interface TagProps {
  children?: React.ReactNode;
  className?: string;
  variant?:
    | "neutral"
    | "blue"
    | "blueOutline"
    | "red"
    | "redOutline"
    | "green"
    | "greenOutline"
    | "yellow"
    | "violet"
    | "white"
    | "grey"
    | "magenta"
    | "magentaOutline"
    | "salmon"
    | "salmonOutline"
    | "yellowOutline";
  size?: "sm" | "md";
}

const Tag = ({
  children,
  className,
  variant = "neutral",
  size = "sm",
}: TagProps) => {
  const tagStyles = {
    neutral: "bg-muted-100 text-muted-900",
    blue: "bg-blue-200 text-blue-1000",
    blueOutline: "bg-blue-100 text-blue-900 border border-blue-200",
    red: "bg-red-200 text-red-1000",
    redOutline: "bg-red-100 text-red-900 border border-red-200",
    green: "bg-green-200 text-green-1000",
    greenOutline: "bg-green-100 text-green-900 border border-green-200",
    yellow: "bg-yellow-300 text-yellow-1000",
    violet: "bg-violet-200 text-violet-1000",
    white: "bg-white text-muted-900 border border-muted-200",
    grey: "bg-muted-500/10 text-muted-900 border border-muted-300",
    magenta: "bg-[#F3DEF6] text-[#37352F]",
    magentaOutline: "bg-[#FAEFFB] text-[#9B30A1] border border-[#DF9DE4]",
    yellowOutline: "bg-yellow-100 text-yellow-900 border border-yellow-400",
    salmon: "bg-[#FFCFC6] text-[#37352F]",
    salmonOutline: "bg-[#FFCFC6] text-[#37352F] border border-[#FF9985]",
  };

  const tagSize = {
    sm: "rounded-md px-1.5 h-6 text-xs",
    md: "rounded-lg px-2 h-8 text-sm",
  };

  return (
    <span
      className={twMerge(
        "font-worksans inline-flex items-center gap-1 font-medium tracking-[-0.3px] text-nowrap",
        tagStyles[variant],
        tagSize[size],
        className,
      )}
    >
      {children}
    </span>
  );
};
export default Tag;
