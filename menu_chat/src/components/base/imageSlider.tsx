import { useState } from "react";
import Image from "next/image";
import { motion } from "motion/react";
import { twMerge } from "tailwind-merge";

interface ImageSliderProps {
  images: string[];
  height?: number;
  className?: string;
  thumbnailPosition?: "left" | "right" | "top" | "bottom";
}

const ImageSlider = ({
  images,
  height = 270,
  className,
  thumbnailPosition = "bottom",
}: ImageSliderProps) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const isVertical =
    thumbnailPosition === "left" || thumbnailPosition === "right";

  const goTo = (index: number) => {
    const count = images.length;
    setActiveIndex((index + count) % count);
  };

  const renderThumbnails = () => (
    <div
      className={twMerge(
        isVertical
          ? twMerge(
              "flex h-full w-10.5 flex-col justify-center gap-1 overflow-y-auto",
              thumbnailPosition === "right" ? "ml-2" : "mr-2",
            )
          : twMerge(
              "flex w-full flex-row justify-center gap-1 overflow-x-auto",
              thumbnailPosition === "bottom" ? "mt-2" : "mb-2",
            ),
      )}
    >
      {images.map((img, idx) => (
        <button
          key={img}
          onClick={() => goTo(idx)}
          className={twMerge(
            "relative aspect-[1] w-full shrink-0 cursor-pointer overflow-hidden rounded border-1",
            idx === activeIndex ? "border-yellow-600" : "border-transparent",
            !isVertical && "size-10",
          )}
        >
          <Image
            src={img}
            alt={`Thumbnail ${idx + 1}`}
            fill
            className="object-cover object-center"
          />
        </button>
      ))}
    </div>
  );

  return (
    <div
      className={twMerge(
        "border-muted-100 relative w-full overflow-hidden rounded-lg border bg-white p-2",
        className,
      )}
      style={{ height: `${height}px` }}
    >
      <div
        className={twMerge(
          "flex h-full w-full",
          thumbnailPosition === "top" || thumbnailPosition === "bottom"
            ? "flex-col"
            : thumbnailPosition === "left"
              ? "flex-row-reverse"
              : "flex-row",
        )}
      >
        {thumbnailPosition === "top" && renderThumbnails()}

        <div className="relative h-full flex-1">
          {images.map((img, idx) => {
            const isActive = idx === activeIndex;
            return (
              <motion.div
                key={img}
                initial={{ opacity: 0 }}
                animate={{ opacity: isActive ? 1 : 0 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0 h-full w-full overflow-hidden rounded-sm"
                style={{ zIndex: isActive ? 1 : 0 }}
              >
                <Image
                  src={img}
                  alt={`Slide ${idx + 1}`}
                  fill
                  className="object-cover object-center"
                  priority={isActive}
                />
              </motion.div>
            );
          })}
          <div className="absolute right-0 bottom-2 left-0 z-20 flex items-end justify-center space-x-2">
            {images.map((_, idx) => (
              <button
                key={idx}
                onClick={() => goTo(idx)}
                className={twMerge(
                  "bg-muted-200 h-2 w-2 cursor-pointer rounded-full transition-all",
                  idx === activeIndex && "h-3.5 w-2 bg-white",
                )}
              />
            ))}
          </div>
        </div>

        {thumbnailPosition === "bottom" && renderThumbnails()}
        {(thumbnailPosition === "left" || thumbnailPosition === "right") &&
          renderThumbnails()}
      </div>
    </div>
  );
};

export default ImageSlider;
