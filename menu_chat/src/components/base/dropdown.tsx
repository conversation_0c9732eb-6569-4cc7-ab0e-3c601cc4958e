import React, { useState, useRef, useEffect, useCallback } from "react";
import { twMerge } from "tailwind-merge";
import { motion, AnimatePresence } from "framer-motion";
import { createPopper, Instance, Placement } from "@popperjs/core";
import { IconArrowDown } from "@/components/icons";
import Link from "next/link";

interface Option {
  label: string | React.ReactNode;
  value: string;
  link?: string;
}

interface DropdownProps {
  options: Option[];
  onSelect: (value: string) => void;
  children?: React.ReactNode;
  withCaret?: boolean;
  className?: string;
  triggerClassName?: string;
  menuClassName?: string;
  selectedValue?: string | number;
  placement?: Placement;
  triggerLabel?: string | React.ReactNode;
  icon?: React.ReactNode;
}

const usePopper = (
  referenceElement: HTMLElement | null,
  popperElement: HTMLElement | null,
  placement: Placement = "bottom-start",
) => {
  const [popperInstance, setPopperInstance] = useState<Instance | null>(null);

  useEffect(() => {
    if (referenceElement && popperElement) {
      const instance = createPopper(referenceElement, popperElement, {
        placement,
        modifiers: [
          {
            name: "offset",
            options: {
              offset: [0, 4],
            },
          },
          {
            name: "preventOverflow",
            options: {
              padding: 8,
            },
          },
        ],
      });
      setPopperInstance(instance);

      return () => {
        instance.destroy();
      };
    }
  }, [referenceElement, popperElement, placement]);

  return popperInstance;
};

const Dropdown = ({
  options,
  onSelect,
  children,
  withCaret,
  triggerClassName = "",
  menuClassName = "",
  className = "",
  selectedValue,
  placement = "bottom-start",
  triggerLabel = "Select an option",
  icon,
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const popperInstance = usePopper(
    triggerRef.current,
    dropdownRef.current,
    placement,
  );

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (
      triggerRef.current &&
      !triggerRef.current.contains(event.target as Node) &&
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handleClickOutside]);

  const toggleDropdown = useCallback(() => {
    setIsOpen((prevIsOpen) => !prevIsOpen);
  }, []);

  useEffect(() => {
    if (popperInstance) {
      popperInstance.update();
    }
  }, [isOpen, popperInstance]);

  const handleSelect = useCallback(
    (value: string) => {
      onSelect(value);
      setIsOpen(false);
    },
    [onSelect],
  );

  return (
    <div className={twMerge("relative inline-block flex-none", className)}>
      <div
        className="flex cursor-pointer items-center gap-1"
        ref={triggerRef}
        onClick={toggleDropdown}
      >
        {children ? (
          children
        ) : (
          <div
            role="button"
            tabIndex={0}
            aria-haspopup="listbox"
            aria-expanded={isOpen}
            className={twMerge(
              "bg-muted-100 border-muted-300 font-worksans flex h-9 items-center gap-1 rounded-md border px-2 text-sm font-medium text-neutral-900",
              withCaret ? "pr-8" : "",
              icon ? "pl-8" : "",
              triggerClassName,
            )}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") toggleDropdown();
            }}
          >
            {icon && (
              <span className="absolute left-2 flex size-6 items-center">
                {icon}
              </span>
            )}
            {options.find((option) => option.value === selectedValue)?.label ||
              triggerLabel ||
              "Select an option"}
          </div>
        )}
        {withCaret && (
          <IconArrowDown
            size={16}
            className={twMerge(
              "pointer-events-none absolute top-0 right-2 bottom-0 my-auto transition-transform duration-200",
              isOpen ? "rotate-180" : "",
            )}
          />
        )}
      </div>
      <div
        ref={dropdownRef}
        className="absolute z-40"
        style={{
          visibility: isOpen ? "visible" : "hidden",
          opacity: isOpen ? 1 : 0,
        }}
      >
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className={twMerge(
                "max-w-72 min-w-52 rounded-lg border border-[#F0EDE6] bg-white p-1 shadow-[0px_12px_16px_-2px_rgba(33,31,28,0.08),_0px_1px_6px_0px_rgba(33,31,28,0.06)]",
                menuClassName,
              )}
            >
              <div className="max-h-[420px] overflow-y-auto">
                {options.map((option) => (
                  <div
                    key={option.value}
                    className={twMerge(
                      "hover:bg-muted-500/10 flex cursor-pointer items-center gap-2 rounded-sm px-3 py-2 transition-colors duration-300",
                      option.value === selectedValue && "bg-muted-500/20",
                      option.value === "divider" && "pointer-events-none px-0",
                      option.value === "heading" && "pointer-events-none py-1",
                    )}
                    onClick={() => handleSelect(option.value)}
                  >
                    {option.link ? (
                      <Link
                        href={option.link}
                        className="block w-full text-sm font-medium text-neutral-900"
                      >
                        {option.label}
                      </Link>
                    ) : option.value === "divider" ? (
                      <div className="bg-muted-100 h-px w-full" />
                    ) : option.value === "heading" ? (
                      <span className="flex items-center text-xs font-semibold tracking-[-0.036px] text-neutral-600">
                        {option.label}
                      </span>
                    ) : (
                      <span className="flex items-center gap-2 text-sm font-medium text-neutral-900">
                        {option.label}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Dropdown;
