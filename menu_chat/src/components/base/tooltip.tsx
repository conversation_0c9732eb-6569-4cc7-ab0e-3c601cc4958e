import React, { useId } from "react";
import { Tooltip as ReactTooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import { twMerge } from "tailwind-merge";

interface TooltipProps {
  tooltipId?: string;
  content: string;
  place?: "top" | "bottom" | "left" | "right";
  className?: string;
  wrapperClassName?: string;
  disabled?: boolean;
  children: React.ReactNode;
  offset?: number;
  withArrow?: boolean;
  contentNode?: React.ReactNode;
}

const Tooltip = ({
  tooltipId,
  content,
  place = "top",
  className,
  disabled,
  children,
  offset = 1,
  wrapperClassName,
  withArrow = true,
  contentNode,
}: TooltipProps) => {
  const internalId = useId();
  const resolvedId = tooltipId ?? `tooltip-${internalId}`;

  return (
    <>
      <div
        data-tooltip-id={resolvedId}
        data-tooltip-content={content}
        className={twMerge("inline-block", wrapperClassName)}
      >
        {children}
      </div>

      {!disabled && (
        <ReactTooltip
          id={resolvedId}
          anchorSelect={`[data-tooltip-id='${resolvedId}']`}
          place={place}
          offset={offset}
          positionStrategy="fixed"
          noArrow={!withArrow}
          render={({ content: defaultContent }) =>
            contentNode ?? defaultContent
          }
          className={twMerge(
            "!bg-muted-1000 font-worksans z-50 max-w-60 !rounded-lg !px-3 !py-1.5 !text-xs !font-medium !tracking-[-0.3px] !text-white !opacity-100 !shadow-[0px_3px_12px_1px_rgba(0,0,0,0.12)]",
            className,
          )}
        />
      )}
    </>
  );
};

export default Tooltip;
