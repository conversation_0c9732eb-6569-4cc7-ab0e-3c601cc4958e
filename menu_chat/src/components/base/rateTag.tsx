import Tag from './tag';
import { IconGraph, IconIncrease, IconDecrease } from "@/components/icons";

type Trend = "down" | "up" | "flat" | "na";

const RateTag = ({ value }: { value: number | string | null | undefined }) => {
  const n = Number(value);
  const isNum = Number.isFinite(n);
  const normalized = isNum && Object.is(n, -0) ? 0 : n;

  const trend: Trend = !isNum ? "na" : normalized < 0 ? "down" : normalized > 0 ? "up" : "flat";

  const Icon = {
    down: IconDecrease,
    up: IconIncrease,
    flat: IconGraph,
    na: IconGraph,
  }[trend];

  const variant = ({
    down: "redOutline",
    up: "greenOutline",
    flat: "blueOutline",
    na: "blueOutline",
  } as const)[trend];

  return (
    <Tag variant={variant}>
      <Icon size={16} />
      {isNum ? `${(normalized as number).toFixed(2)}%` : "—"}
    </Tag>
  );
};

export default RateTag;
