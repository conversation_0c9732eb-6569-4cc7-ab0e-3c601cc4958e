import { twMerge } from "tailwind-merge";
import { useState } from "react";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Input from "@/components/base/input";
import Button from "@/components/base/button";
import { IconEmail } from "@/components/icons";
import Link from "next/link";

const ForgotPasswordForm = () => {
  const [email, setEmail] = useState("");

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  return (
    <div className="px-8 py-7">
      <Heading level={2} className="mb-1">
        Forgot Password
      </Heading>
      <Paragraph className="font-archivo mb-8 font-semibold text-neutral-600">
        Please enter email associated with your account and we will send you the
        reset link.
      </Paragraph>
      <div className="relative mb-5">
        <label
          className={twMerge(
            "pointer-events-none absolute top-2 left-4 z-10 text-xs font-semibold text-neutral-400 transition-colors",
            email && "text-neutral-700",
          )}
        >
          Email
        </label>
        <Input
          value={email}
          onChange={handleEmailChange}
          inputSize="lg"
          type="email"
          placeholder="Type in your email"
          inputClassName={twMerge(
            "bg-muted-500/15 pt-5.5 pb-2 pr-8 h-auto",
            email && "bg-white",
          )}
        />
        <IconEmail
          size={16}
          className="pointer-events-none absolute top-0 right-3 bottom-0 my-auto text-neutral-500"
        />
      </div>
      <div className="mb-8">
        <Button size="lg" className="w-full" disabled={!email}>
          Send Reset Link
        </Button>
      </div>
      <div className="w-full text-center">
        <Link
          href="/login"
          className="font-archivo font-semibold text-neutral-600 underline transition-colors duration-300 hover:text-neutral-700"
        >
          Back
        </Link>
      </div>
    </div>
  );
};

export default ForgotPasswordForm;
