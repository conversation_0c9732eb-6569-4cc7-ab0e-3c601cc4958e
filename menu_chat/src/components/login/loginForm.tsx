import { twMerge } from "tailwind-merge";
import { FormEvent, useState } from "react";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Input from "@/components/base/input";
import { IconEmail, IconEye, IconEye2 } from "@/components/icons";
import Link from "next/link";
import { signIn } from "next-auth/react";
// import { useRouter } from "next/router";
import Button from "@/components/base/button";

const LoginForm = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  // const router = useRouter();

  // Set this to true for first-time users to redirect to onboarding
  const firstLogin = true;

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const result = await signIn("credentials", {
      email: email,
      password: password,
      redirect: false,
    });

    if (result?.error) {
      // Handle error here
      console.error(result.error);
    } else {
      const queryString = location.search;
      const urlParams = new URLSearchParams(queryString);
      const callbackUrl = urlParams.get("callbackUrl");

      if (firstLogin) {
        // Redirect to onboarding for first-time users
        location.href = "/onboarding"
      } else if (callbackUrl) {
        location.href = callbackUrl;
      } else {
        location.href = "/";
      }
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  const handleTogglePassword = () => {
    setShowPassword((prev) => !prev);
  };

  const isDisabled = !email || !password; //Add your own validation logic here

  return (
    <div className="px-8 py-7">
      <Heading level={2} className="mb-1">
        Login into your account
      </Heading>
      <Paragraph className="font-archivo mb-8 font-semibold text-neutral-600">
        Leverage GenAI to discover  flavors, menu innovation, and new customers
        in real time.
      </Paragraph>
      <form onSubmit={handleSubmit}>
        <div className="group relative mb-5">
          <label
            className={twMerge(
              "pointer-events-none absolute top-2 left-4 z-10 text-xs font-semibold text-neutral-400 group-hover:text-neutral-700",
              email && "text-neutral-700",
            )}
          >
            Email
          </label>
          <Input
            value={email}
            onChange={handleEmailChange}
            inputSize="lg"
            type="email"
            placeholder="Type in your email"
            inputClassName={twMerge(
              "bg-muted-500/15 pt-5.5 pb-2 pr-8 h-auto group-hover:text-neutral-700 group-hover:placeholder:text-neutral-900",
              email && "bg-white",
            )}
          />
          <IconEmail
            size={16}
            className="pointer-events-none absolute top-0 right-3 bottom-0 my-auto text-neutral-600"
          />
        </div>
        <div className="group relative mb-5">
          <label
            className={twMerge(
              "pointer-events-none absolute top-2 left-4 z-10 text-xs font-semibold text-neutral-400 group-hover:text-neutral-700",
              password && "text-neutral-700",
            )}
          >
            Password
          </label>
          <Input
            value={password}
            onChange={handlePasswordChange}
            inputSize="lg"
            type={showPassword ? "text" : "password"}
            placeholder="Type in your password"
            inputClassName={twMerge(
              "bg-muted-500/15 pt-5.5 pb-2 pr-8 h-auto group-hover:text-neutral-700 group-hover:placeholder:text-neutral-900",
              password && "bg-white",
            )}
            // error={password.length < 8 ? "Example error message" : ""}
          />
          <button
            onClick={handleTogglePassword}
            className="absolute top-0 right-3 bottom-0 my-auto cursor-pointer"
          >
            {showPassword ? (
              <IconEye2 size={16} className="text-neutral-600" />
            ) : (
              <IconEye size={16} className="text-neutral-600" />
            )}
          </button>
        </div>
        <div className="mb-8">
          <Button className="w-full" type="submit" disabled={isDisabled}>
            Sign In
          </Button>
        </div>
        <div className="w-full text-center">
          <Link
            href="/forgot-password"
            className="font-archivo font-semibold text-neutral-600 underline transition-colors duration-300 hover:text-neutral-700"
          >
            Forgot your password?
          </Link>
        </div>
      </form>
    </div>
  );
};

export default LoginForm;
