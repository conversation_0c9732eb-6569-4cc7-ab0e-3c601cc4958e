import { useRef, useEffect } from "react";
import { twMerge } from "tailwind-merge";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  ChartData,
  ScriptableContext,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels,
);

export type BarChartVerticalItem = {
  id: number;
  label: string;
  color: string[];
  values: number[];
  seriesLabels?: string[];
  secondaryValue?: number;
  symbol?: string;
};

interface BarChartVerticalProps {
  dataItems: BarChartVerticalItem[];
  maxValue: number;
  symbol: string;
  className?: string;
  height?: number;
  showLabels?: boolean;
  showLegend?: boolean;
}

const createStripedPattern = (
  color: string,
  ctx: CanvasRenderingContext2D | null,
) => {
  if (!ctx) return color;
  const patternCanvas = document.createElement("canvas");
  const patternContext = patternCanvas.getContext("2d");
  if (!patternContext) return color;

  const size = 8;
  const stripeColor = "rgba(0, 0, 0, 0.1)";
  const lineWidth = 1;

  patternCanvas.width = size;
  patternCanvas.height = size;

  patternContext.fillStyle = color;
  patternContext.fillRect(0, 0, size, size);

  patternContext.strokeStyle = stripeColor;
  patternContext.lineWidth = lineWidth;
  patternContext.beginPath();
  patternContext.moveTo(0, size);
  patternContext.lineTo(size, 0);
  patternContext.stroke();

  const pattern = ctx.createPattern(patternCanvas, "repeat");
  return pattern || color;
};

export default function BarChartVertical({
  dataItems,
  className,
  maxValue,
  symbol,
  height = 350,
  showLabels = false,
  showLegend = true,
}: BarChartVerticalProps) {
  const labels = dataItems.map((item) => item.label);
  const patternCache = useRef<Record<string, string | CanvasPattern | null>>(
    {},
  );

  useEffect(() => {
    patternCache.current = {};
  }, [dataItems]);

  const numSeries = Math.max(...dataItems.map((item) => item.values.length));
  const datasets: ChartData<"bar", number[], string>["datasets"] = [];

  for (let i = 0; i < numSeries; i++) {
    const seriesLabel = dataItems[0]?.seriesLabels?.[i] ?? `Series ${i + 1}`;
    datasets.push({
      label: seriesLabel,
      data: dataItems.map((item) => item.values[i] ?? 0),
      backgroundColor: (context: ScriptableContext<"bar">) => {
        const chart = context.chart;
        const { dataIndex } = context;
        if (!chart || !chart.ctx || dataIndex === undefined)
          return "rgba(0,0,0,0.1)";
        const itemColor = dataItems[dataIndex]?.color[i] ?? "rgba(0,0,0,0.1)";
        const cacheKey = `${itemColor}_${i}`;
        if (patternCache.current[cacheKey])
          return patternCache.current[cacheKey];
        const pattern = createStripedPattern(itemColor, chart.ctx);
        patternCache.current[cacheKey] = pattern;
        return pattern;
      },
      borderColor: "#ffffff",
      borderWidth: 2,
      borderRadius: { topLeft: 6, topRight: 6, bottomLeft: 6, bottomRight: 6 },
      barThickness: "flex",
      maxBarThickness: 35,
      order: i,
      borderSkipped: false,
      hoverBorderColor: "#ffffff",
      hoverBackgroundColor: dataItems.map(
        (item) => item.color[i] ?? "rgba(0,0,0,0.1)",
      ),
    });
  }

  const data: ChartData<"bar", number[], string> = {
    labels,
    datasets,
  };

  const options: ChartOptions<"bar"> = {
    indexAxis: "x",
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: showLegend,
        position: "bottom",
        align: "start",
        labels: {
          boxWidth: 4,
          boxHeight: 12,
          font: {
            family: '"Work Sans", sans-serif',
            size: 12,
            weight: 500,
          },
        },
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const value = context.raw as number;
            const item = dataItems[context.dataIndex];
            return item.symbol
              ? `${value.toLocaleString()}${item.symbol}`
              : `${value.toLocaleString()}`;
          },
        },
      },
      datalabels: {
        display: showLabels,
        anchor: "end",
        align: "end",
        offset: -2,
        formatter: (value: number, context) => {
          const item = dataItems[context.dataIndex];
          return item.symbol
            ? `${value.toLocaleString()}${item.symbol}`
            : `${value.toLocaleString()}`;
        },
        color: "#58554B",
        font: {
          family: '"Work Sans", sans-serif',
          size: 12,
          weight: 600,
        },
      },
    },
    scales: {
      x: {
        type: "category",
        labels: dataItems.map((item) => item.label),
        stacked: false,
        ticks: {
          padding: 8,
          color: "#37352F",
          font: {
            family: '"Work Sans", sans-serif',
            size: 12,
            weight: 500,
          },
        },
        grid: {
          drawOnChartArea: false,
          drawTicks: false,
          color: "#F0EDE6",
        },
        border: {
          dash: [4, 4],
          display: false,
        },
      },
      y: {
        type: "linear",
        stacked: false,
        min: 0,
        max: maxValue,
        ticks: {
          padding: 8,
          maxTicksLimit: 8,
          color: "#58554B",
          callback: (val) => {
            const formattedValue = ["k", "K"].includes(symbol)
              ? Number(val) / 1000
              : val;
            return `${formattedValue}${symbol}`;
          },
          font: {
            family: '"Work Sans", sans-serif',
            size: 12,
            weight: 500,
          },
        },
        grid: {
          drawOnChartArea: true,
          drawTicks: false,
          color: "#F0EDE6",
        },
        border: {
          dash: [4, 4],
          display: false,
        },
      },
    },
  };

  return (
    <div className={twMerge("relative w-full", className)}>
      <div className={twMerge(`h-[${height}px] w-full !tracking-[-0.3px]`)}>
        <Bar options={options} data={data} height={height} />
      </div>
    </div>
  );
}
