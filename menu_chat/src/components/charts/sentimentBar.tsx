import { twMerge } from "tailwind-merge";
import Tooltip from "@/components/base/tooltip";

const SENTIMENT_CONFIG: Record<string, { color: string; label: string }> = {
  "strongly-dislike": { color: "bg-red-500", label: "Strongly dislike" },
  "somewhat-dislike": { color: "bg-orange-600", label: "Somewhat dislike" },
  neutral: { color: "bg-yellow-500", label: "Neutral" },
  "somewhat-like": { color: "bg-green-500", label: "Somewhat like" },
  "strongly-like": { color: "bg-green-700", label: "Strongly like" },
};

interface Segment {
  color: string;
  value: number;
  label: string;
}

type SegmentInput = Segment[] | Record<string, number>;

interface SentimentBarProps {
  segments: SegmentInput;
  className?: string;
  height?: number;
  showTooltip?: boolean;
  showLabel?: boolean;
  withPercentage?: boolean;
  pattern?: "none" | "vertical" | "diagonal";
}

const normalizeSegments = (input: SegmentInput): Segment[] => {
  if (Array.isArray(input)) return input;
  return Object.entries(input).map(([key, value]) => {
    const config = SENTIMENT_CONFIG[key] ?? {
      color: "bg-gray-400",
      label: key,
    };
    return { value, color: config.color, label: config.label };
  });
};

const SentimentBar: React.FC<SentimentBarProps> = ({
  segments,
  className = "",
  height = 20,
  showTooltip = true,
  showLabel = false,
  withPercentage = false,
  pattern = "none",
}) => {
  const resolved = normalizeSegments(segments);
  const total = resolved.reduce((sum, seg) => sum + seg.value, 0);

  return (
    <div
      className={twMerge(
        "flex w-full min-w-[200px] gap-0.5",
        showLabel && "pt-5",
        className,
      )}
    >
      {resolved.map((seg, index) => {
        const width = `${(seg.value / total) * 100}%`;
        const tooltipId = `tooltip-${index}`;
        const isPattern = pattern !== "none";

        const patternStyle =
          pattern === "vertical"
            ? "bg-[repeating-linear-gradient(to_right,rgba(0,0,0,0.1)_0,rgba(0,0,0,0.1)_1px,transparent_1px,transparent_4px)]"
            : pattern === "diagonal"
              ? "bg-[repeating-linear-gradient(130deg,rgba(0,0,0,0.07)_0,rgba(0,0,0,0.07)_1px,transparent_1px,transparent_5px)]"
              : "";

        return (
          <div
            key={index}
            className={twMerge("relative rounded-sm", !isPattern && seg.color)}
            style={{
              width,
              height: `${height}px`,
              ...(isPattern && { backgroundColor: seg.color }),
            }}
            {...(showTooltip && { "data-tooltip-id": tooltipId })}
          >
            {showLabel && (
              <span className="absolute inset-x-0 -top-5 mx-auto text-center text-xs font-medium text-neutral-600">
                {seg.value}
              </span>
            )}

            <div className="relative h-full overflow-hidden rounded-sm">
              {isPattern && (
                <div
                  className={twMerge(
                    "pointer-events-none absolute inset-0",
                    patternStyle,
                  )}
                  aria-hidden="true"
                />
              )}
              {showTooltip ? (
                <Tooltip
                  tooltipId={tooltipId}
                  content={`${seg.label}${withPercentage ? ` - ${seg.value}%` : ""}`}
                  wrapperClassName="block w-full h-full"
                >
                  <span className="sr-only">{seg.value}</span>
                </Tooltip>
              ) : (
                <span className="block h-full w-full" />
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default SentimentBar;
