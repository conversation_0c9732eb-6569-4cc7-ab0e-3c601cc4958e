import { twMerge } from "tailwind-merge";
import { TagCloud } from "react-tagcloud";

interface TagData {
  value: string;
  count: number;
  color?: string;
}

interface TagCloudChartProps {
  data: TagData[];
  minSize?: number;
  maxSize?: number;
}

const TagCloudChart = ({
  data,
  minSize = 12,
  maxSize = 28,
}: TagCloudChartProps) => {
  const customRenderer = (
    tag: { value: string; count: number; color?: string },
    size: number,
  ) => {
    const getBorderRadius = (count: number) => {
      if (count >= 35) return "rounded-xl";
      if (count >= 30) return "rounded-lg";
      if (count >= 25) return "rounded-lg";
      if (count >= 20) return "rounded-md";
      return "rounded-md";
    };

    return (
      <span
        key={tag.value}
        className={twMerge(
          "m-0.5 inline-block px-4 py-2 font-medium tracking-tight text-gray-800",
          getBorderRadius(tag.count),
        )}
        style={{
          fontSize: `${size}px`,
          backgroundColor: tag.color || "#F0EDE6",
        }}
      >
        {tag.value}
      </span>
    );
  };

  return (
    <TagCloud
      tags={data}
      minSize={minSize}
      maxSize={maxSize}
      renderer={customRenderer}
    />
  );
};

export default TagCloudChart;
