import { twMerge } from "tailwind-merge";

interface SingleBarProps {
  value: number;
  maxValue: number;
  color?: string;
  height?: number;
  pattern?: "none" | "vertical" | "diagonal";
  className?: string;
}

const SingleBar: React.FC<SingleBarProps> = ({
  value,
  maxValue,
  color = "#D7D5C1",
  height = 20,
  pattern = "none",
  className,
}) => {
  const isPattern = pattern !== "none";
  const percent = Math.min(value / maxValue, 1) * 100;

  const patternStyle =
    pattern === "vertical"
      ? "bg-[repeating-linear-gradient(to_right,rgba(0,0,0,0.07)_0,rgba(0,0,0,0.07)_1px,transparent_1px,transparent_4px)]"
      : pattern === "diagonal"
        ? "bg-[repeating-linear-gradient(130deg,rgba(0,0,0,0.07)_0,rgba(0,0,0,0.07)_1px,transparent_1px,transparent_5px)]"
        : "";

  return (
    <div className={twMerge("w-full min-w-[100px]", className)}>
      <div
        className="bg-muted-100 relative overflow-hidden rounded-sm"
        style={{ height: `${height}px` }}
      >
        <div
          className="relative h-full rounded-sm"
          style={{ width: `${percent}%`, backgroundColor: color }}
        >
          {isPattern && (
            <div
              className={twMerge(
                "pointer-events-none absolute inset-0 rounded-sm",
                patternStyle,
              )}
              aria-hidden="true"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default SingleBar;
