import { Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Too<PERSON><PERSON>,
  Legend,
  ChartOptions,
  ChartData,
  ScriptableContext,
} from "chart.js";
import { twMerge } from "tailwind-merge";
import { useRef, useEffect } from "react";

ChartJS.register(ArcE<PERSON>, Tooltip, Legend);

type DonutSegment = {
  value: number;
  color: string;
  label?: string;
};

interface DonutChartProps {
  data: DonutSegment[];
  size?: number;
  showLabel?: boolean;
  className?: string;
  cutout?: number;
  pattern?: boolean;
}

export default function DonutChart({
  data,
  size = 180,
  showLabel = true,
  className,
  cutout = 65,
  pattern = false,
}: DonutChartProps) {
  const totalValue = data.reduce((sum, seg) => sum + seg.value, 0);
  const gapValue = 100 - totalValue;

  const patternCache = useRef<Record<string, string | CanvasPattern | null>>(
    {},
  );
  useEffect(() => {
    patternCache.current = {};
  }, [data]);

  const createStripedPattern = (
    color: string,
    ctx: CanvasRenderingContext2D | null,
  ) => {
    if (!ctx) return color;
    const patternCanvas = document.createElement("canvas");
    const patternContext = patternCanvas.getContext("2d");
    if (!patternContext) return color;

    const size = 5;
    const stripeColor = "rgba(0, 0, 0, 0.1)";
    const lineWidth = 1;

    patternCanvas.width = size;
    patternCanvas.height = size;

    patternContext.fillStyle = color;
    patternContext.fillRect(0, 0, size, size);

    patternContext.strokeStyle = stripeColor;
    patternContext.lineWidth = lineWidth;

    patternContext.beginPath();
    patternContext.moveTo(0, 0);
    patternContext.lineTo(0, size);
    patternContext.stroke();

    return ctx.createPattern(patternCanvas, "repeat") || color;
  };

  const datasetData = [...data.map((seg) => seg.value)];
  if (gapValue > 0) datasetData.push(gapValue);

  const chartData: ChartData<"doughnut", number[], string> = {
    labels: [...data.map((seg) => seg.label || ""), gapValue > 0 ? "" : ""],
    datasets: [
      {
        data: datasetData,
        backgroundColor: (context: ScriptableContext<"doughnut">) => {
          const { dataIndex, chart } = context;
          if (!chart || !chart.ctx || dataIndex === undefined) return "#ccc";
          const color =
            dataIndex < data.length ? data[dataIndex].color : "#F0EDE6";

          if (!pattern || color === "#F0EDE6") return color;

          if (patternCache.current[color]) return patternCache.current[color]!;
          const patternResult = createStripedPattern(color, chart.ctx);
          patternCache.current[color] = patternResult;
          return patternResult;
        },
        borderWidth: 2,
        borderColor: "#ffffff",
        borderRadius: 4,
        hoverOffset: 0,
        hoverBorderColor: "#ffffff",
      },
    ],
  };

  const options: ChartOptions<"doughnut"> = {
    cutout: `${cutout}%`,
    plugins: {
      tooltip: {
        enabled: true,
        callbacks: {
          label: (context) => {
            if (context.dataIndex >= data.length) return "";
            const segment = data[context.dataIndex];
            return ` ${segment.value}%`;
          },
        },
        filter: (tooltipItem) => {
          const bgColor = tooltipItem.dataset.backgroundColor;
          if (Array.isArray(bgColor)) {
            return bgColor[tooltipItem.dataIndex] !== "#F0EDE6";
          }
          return true;
        },
      },
      legend: {
        display: false,
      },
      datalabels: {
        display: false,
      },
    },
    responsive: true,
    maintainAspectRatio: false,
  };

  return (
    <div
      className={twMerge(
        "relative flex items-center justify-center",
        className,
      )}
      style={{ width: size, height: size }}
    >
      <Doughnut data={chartData} options={options} />
      {showLabel && (
        <div className="font-archivo pointer-events-none absolute text-center text-base font-semibold text-neutral-800">
          {totalValue}
          <span className="text-xs text-neutral-600">%</span>
        </div>
      )}
    </div>
  );
}
