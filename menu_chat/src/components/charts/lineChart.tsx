import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartData,
  ChartOptions,
  ChartDataset,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { twMerge } from "tailwind-merge";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels,
);

export type LineSeriesItem = {
  id: number;
  label: string;
  color: string;
  data: number[];
  percentData?: number[];
};

type yTitle = {
  display: boolean;
  text: string;
};

interface LineChartProps {
  dataItems: LineSeriesItem[];
  labels: string[];
  symbol?: string;
  className?: string;
  height?: number;
  showLegend?: boolean;
  yTitle?: yTitle;
  yTickCallback?: (value: string | number) => string;
  yMax?: number;
}

interface CustomLineDataset extends ChartDataset<"line", (number | null)[]> {
  percentData?: number[];
}

export default function LineChart({
  dataItems,
  labels,
  symbol = "",
  className,
  height = 350,
  showLegend = true,
  yTitle = {
    display: false,
    text: "Your Y Axis Label",
  },
  yTickCallback = (val) => `${val}${symbol}`,
  yMax,
}: LineChartProps) {
  const data: ChartData<"line", (number | null)[], string> = {
    labels,
    datasets: dataItems.map((item) => ({
      label: item.label,
      data: item.data,
      fill: false,
      borderColor: item.color,
      backgroundColor: item.color,
      tension: 0.3,
      pointRadius: 0,
      pointHoverRadius: 0,
      percentData: item.percentData,
    })),
  };

  const options: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: "nearest" as const,
      axis: "x" as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: showLegend,
        position: "bottom",
        labels: {
          boxWidth: 3,
          boxHeight: 10,
          font: {
            family: '"Work Sans", sans-serif',
            size: 12,
            weight: 500,
          },
        },
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const dataset = context.dataset as CustomLineDataset;
            const index = context.dataIndex;
            const value = context.raw as number;
            const percent = dataset.percentData?.[index];
            if (percent !== undefined) {
              return `${dataset.label}: ${percent.toFixed(2)}%`;
            }
            return `${dataset.label}: ${yTickCallback(value)}`;
          },
        },
      },
      datalabels: {
        display: false,
      },
    },
    scales: {
      x: {
        ticks: {
          font: {
            family: '"Work Sans", sans-serif',
            size: 12,
          },
          color: "#58554B",
        },
        grid: {
          drawTicks: false,
          drawOnChartArea: false,
          color: "transparent",
        },
        border: {
          display: false,
        },
      },
      y: {
        ticks: {
          callback: yTickCallback,
          color: "#58554B",
          font: {
            family: '"Work Sans", sans-serif',
            size: 12,
            weight: 600,
          },
        },
        grid: {
          drawTicks: false,
          color: "rgba(0,0,0,0.05)",
        },
        border: {
          dash: [4, 4],
          display: false,
        },
        title: yTitle,
        min: 0,
        max: typeof yMax === 'number' ? yMax : undefined,
      },
    },
  };

  return (
    <div className={twMerge(`h-[${height}px] w-full`, className)}>
      <Line data={data} options={options} height={height} />
    </div>
  );
}
