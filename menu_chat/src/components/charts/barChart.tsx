import {
  useRef,
  useEffect,
  forwardRef,
  useImperative<PERSON>andle,
  useState,
} from "react";
import { twMerge } from "tailwind-merge";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  ChartData,
  ScriptableContext,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels,
);

export type BarChartItem = {
  id: number;
  label: string;
  color: string;
  value: number;
  secondaryValue?: number;
  symbol?: string;
};

interface BarChartProps {
  dataItems: BarChartItem[];
  maxValue: number;
  symbol: string;
  className?: string;
  height?: number;
  withSecondaryValue?: boolean;
}

export type BarChartHandle = {
  getChart: () => ChartJS<"bar"> | null;
  getChartData: () => {
    labels: string[];
    datasets: { label: string; data: number[] }[];
  };
};

const createStripedPattern = (
  color: string,
  ctx: CanvasRenderingContext2D | null,
) => {
  if (!ctx) return color;
  const patternCanvas = document.createElement("canvas");
  const patternContext = patternCanvas.getContext("2d");
  if (!patternContext) return color;

  const size = 8;
  const stripeColor = "rgba(0, 0, 0, 0.1)";
  const lineWidth = 1;

  patternCanvas.width = size;
  patternCanvas.height = size;

  patternContext.fillStyle = color;
  patternContext.fillRect(0, 0, size, size);

  patternContext.strokeStyle = stripeColor;
  patternContext.lineWidth = lineWidth;
  patternContext.beginPath();
  patternContext.moveTo(0, size);
  patternContext.lineTo(size, 0);
  patternContext.stroke();

  const pattern = ctx.createPattern(patternCanvas, "repeat");
  return pattern || color;
};

const BarChart = forwardRef(function BarChart(
  {
    dataItems,
    className,
    maxValue,
    symbol,
    height = 350,
    withSecondaryValue = false,
  }: BarChartProps,
  ref,
) {
  const chartRef = useRef<ChartJS<"bar"> | null>(null);
  const labels = dataItems.map((item) => item.label);
  const patternCache = useRef<Record<string, string | CanvasPattern | null>>(
    {},
  );
  const [labelTooltip, setLabelTooltip] = useState<{
    show: boolean;
    text: string;
    x: number;
    y: number;
  }>({ show: false, text: "", x: 0, y: 0 });

  useEffect(() => {
    patternCache.current = {};
  }, [dataItems]);

  const normalizedData = dataItems.map((item) => item.value);

  const data: ChartData<"bar", number[], string> = {
    labels,
    datasets: [
      {
        label: "Track",
        data: new Array(dataItems.length).fill(maxValue),
        backgroundColor: "#F0EDE6",
        borderColor: "#ffffff",
        borderWidth: 2,
        barThickness: "flex",
        maxBarThickness: 35,
        order: 1,
        datalabels: { display: false },
        borderSkipped: false,
        hoverBorderColor: "#ffffff",
        hoverBackgroundColor: "#F0EDE6",
      },
      {
        label: "",
        data: normalizedData,
        backgroundColor: (context: ScriptableContext<"bar">) => {
          const chart = context.chart;
          const { dataIndex } = context;
          if (!chart || !chart.ctx || dataIndex === undefined)
            return "rgba(0,0,0,0.1)";

          const itemColor = dataItems[dataIndex]?.color || "rgba(0,0,0,0.1)";
          if (patternCache.current[itemColor])
            return patternCache.current[itemColor];

          const pattern = createStripedPattern(itemColor, chart.ctx);
          patternCache.current[itemColor] = pattern;
          return pattern;
        },
        borderColor: "#ffffff",
        borderWidth: 2,
        borderRadius: {
          topLeft: 6,
          topRight: 6,
          bottomLeft: 6,
          bottomRight: 6,
        },
        barThickness: "flex",
        maxBarThickness: 35,
        order: 0,
        borderSkipped: false,
        hoverBorderColor: "#ffffff",
        hoverBackgroundColor: (context: ScriptableContext<"bar">) => {
          const chart = context.chart;
          const { dataIndex } = context;
          if (!chart || !chart.ctx || dataIndex === undefined)
            return "rgba(0,0,0,0.1)";

          const itemColor = dataItems[dataIndex]?.color || "rgba(0,0,0,0.1)";
          if (patternCache.current[itemColor])
            return patternCache.current[itemColor];

          const pattern = createStripedPattern(itemColor, chart.ctx);
          patternCache.current[itemColor] = pattern;
          return pattern;
        },
      },
    ],
  };

  const options: ChartOptions<"bar"> = {
    indexAxis: "y",
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: {
        filter: (tooltipItem) => tooltipItem.datasetIndex !== 0,
        callbacks: {
          label: (context) => {
            const value = context.raw as number;
            const item = dataItems[context.dataIndex];
            return item.symbol
              ? `${value.toLocaleString()}${item.symbol}`
              : `${value.toLocaleString()}`;
          },
        },
      },
      datalabels: {
        anchor: "end",
        align: "right",
        formatter: (value: number, context) => {
          const item = dataItems[context.dataIndex];
          return item.symbol
            ? `${value.toLocaleString()}${item.symbol}`
            : `${value.toLocaleString()}`;
        },
        color: "#58554B",
        font: {
          family: '"Work Sans", sans-serif',
          size: 12,
          weight: 600,
        },
      },
    },
    scales: {
      x: {
        stacked: true,
        min: 0,
        max: maxValue,
        ticks: {
          padding: 8,
          callback: (val) => {
            const formattedValue = ["k", "K"].includes(symbol)
              ? Number(val) / 1000
              : val;
            return `${formattedValue}${symbol}`;
          },
          color: "#58554B",
          font: {
            family: '"Work Sans", sans-serif',
            size: 12,
            weight: 500,
          },
        },
        grid: {
          drawOnChartArea: true,
          drawTicks: false,
          color: "rgba(175, 172, 131, 0.5)",
        },
        border: {
          dash: [4, 4],
          display: false,
        },
      },
      y: {
        stacked: true,
        ticks: {
          padding: 8,
          crossAlign: "far",
          color: "#37352F",
          font: {
            family: '"Work Sans", sans-serif',
            size: 14,
            weight: 500,
          },
          callback: function (value, index) {
            const label = dataItems[index]?.label || "";
            return label.length > 15 ? label.substring(0, 15) + "..." : label;
          },
        },
        grid: {
          display: false,
          drawTicks: false,
        },
        border: {
          display: false,
        },
      },
    },
  };

  useImperativeHandle(ref, () => ({
    getChart: () => chartRef.current,
    getChartData: () => ({
      labels: data.labels,
      datasets: data.datasets.map((d) => ({
        label: d.label,
        data: d.data,
      })),
    }),
  }));

  const handleChartMouseMove = (event: React.MouseEvent) => {
    const chart = chartRef.current;
    if (!chart) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;
    const yAxis = chart.scales.y;

    if (!yAxis) return;

    // Only process if mouse is over y-axis area (left side of chart)
    if (mouseX < 150) {
      const tickHeight = yAxis.height / dataItems.length;
      const tickIndex = Math.floor((mouseY - yAxis.top) / tickHeight);

      if (tickIndex >= 0 && tickIndex < dataItems.length) {
        const label = dataItems[tickIndex]?.label;
        if (label && label.length > 15) {
          // Calculate the exact position above the label
          const labelY =
            rect.top + yAxis.top + tickIndex * tickHeight + tickHeight / 2;
          const tooltipX = rect.left + 58;
          const tooltipY = labelY - 52;

          setLabelTooltip({
            show: true,
            text: label,
            x: tooltipX,
            y: tooltipY,
          });
          return;
        }
      }
    }

    // Only hide tooltip if it was previously shown
    if (labelTooltip.show) {
      setLabelTooltip({ show: false, text: "", x: 0, y: 0 });
    }
  };

  const handleChartMouseLeave = () => {
    // Only update state if tooltip is currently shown
    if (labelTooltip.show) {
      setLabelTooltip({ show: false, text: "", x: 0, y: 0 });
    }
  };

  return (
    <div
      className={twMerge(
        "relative w-full",
        withSecondaryValue && "pr-4",
        className,
      )}
    >
      <div
        className={twMerge(`h-[${height}px] w-full !tracking-[-0.3px]`)}
        onMouseMove={handleChartMouseMove}
        onMouseLeave={handleChartMouseLeave}
      >
        <Bar ref={chartRef} options={options} data={data} height={height} />
      </div>

      {/* Custom tooltip for labels */}
      {labelTooltip.show && (
        <div
          className="pointer-events-none fixed z-50 rounded border border-neutral-200 bg-white p-2 text-sm font-medium text-neutral-900 shadow-lg"
          style={{
            left: labelTooltip.x,
            top: labelTooltip.y,
            transform: "translateX(-50%)",
          }}
        >
          {labelTooltip.text}
          {/* Arrow pointing down */}
          <div
            className="absolute top-full left-1/2 h-0 w-0 border-t-4 border-r-4 border-l-4 border-transparent border-t-white"
            style={{ transform: "translateX(-50%)" }}
          />
        </div>
      )}

      {withSecondaryValue && (
        <div className="pointer-events-none absolute top-[35px] right-0 bottom-[44px] flex w-fit flex-col justify-between">
          {dataItems.map((item) => (
            <div
              key={item.id}
              className="flex h-[35px] items-center justify-end text-right text-xs font-semibold text-neutral-700"
            >
              {item.secondaryValue ? `$${item.secondaryValue.toFixed(1)}` : ""}
            </div>
          ))}
        </div>
      )}
    </div>
  );
});
export default BarChart;
