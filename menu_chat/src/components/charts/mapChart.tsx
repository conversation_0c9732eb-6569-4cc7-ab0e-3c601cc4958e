import React, { useState, useEffect, useMemo, useCallback } from "react";
import { AlbersUsa } from "@visx/geo";
import * as topojson from "topojson-client";
import titleCase from "voca/title_case";
import Tag from "@/components/base/tag";
import { IconLocation } from "@/components/icons";

interface DataItem {
  id: string;
  value: number;
  title?: string;
}

interface GeoAlbersUsaProps {
  title: string;
  data: DataItem[];
  width?: number;
  height?: number;
  isExternalLoading?: boolean;
}

interface MultiPolygon {
  type: "MultiPolygon";
  coordinates: [number, number][][][];
}

interface Polygon {
  type: "Polygon";
  coordinates: [number, number][][];
}

interface FeatureShape {
  type: "Feature";
  id: string;
  geometry: Polygon | MultiPolygon;
  properties: { name: string, title?: string, tooltip?: string };
}

interface GeoJsonCollection {
  type: "FeatureCollection";
  features: FeatureShape[];
}

const colors = ["#F3F3EC", "#E0E9BC", "#B4D87D", "#FFE066", "#FFCC00"];

// Memoized color range calculations
const useColorRange = (data: DataItem[]) => {
  return useMemo(() => {
    if (data.length === 0) return { minValue: 0, maxValue: 0, step: 0 };
    const values = data.map((item) => item.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const range = maxValue - minValue;
    const step = range / (colors.length - 1);
    return { minValue, maxValue, step };
  }, [data]);
};

const getColor = (
  val: string,
  data: DataItem[],
  colorRange: { minValue: number; maxValue: number; step: number },
) => {
  if (data.length === 0) return colors[0];
  const { minValue, maxValue, step } = colorRange;
  const num = data.find((item) => item.id === val)?.value || 0;

  if (num === 0 && minValue !== 0) return "#EAEAE4";
  if (maxValue === minValue) return colors[0];

  if (num < minValue + step) return colors[0];
  if (num < minValue + step * 2) return colors[1];
  if (num < minValue + step * 3) return colors[2];
  if (num <= maxValue) return colors[3];

  return colors[colors.length - 1];
};

const getValue = (val: string, data: DataItem[]) => {
  const item = data.find((item) => item.id === val);
  return item ? item.value : 0;
};

const MapChart = ({
  title,
  data,
  width = 600,
  height = 300,
  isExternalLoading = false
}: GeoAlbersUsaProps) => {
  const [features, setFeatures] = useState<FeatureShape[]>([]);
  const [tooltip, setTooltip] = useState<FeatureShape["properties"] | null>(null);
  const [selected, setSelected] = useState<number>(0);
  const [position, setPosition] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Get memoized color range calculations
  const colorRange = useColorRange(data);

  useEffect(() => {
    const loadData = async () => {
      try {
        const topoResponse = await fetch("/assets/usa-topo.json");
        const topology = await topoResponse.json();

        const geoJson = topojson.feature(
          topology,
          topology.objects.states,
        ) as unknown as GeoJsonCollection;

        setFeatures(geoJson.features);

        setIsLoading(false);
      } catch (error) {
        console.error("Error loading map data:", error);
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const enrichedFeatures = useMemo(() => {
    return features.map((feature) => {
      const match = data.find((d) => d.id === feature.id);
      return {
        ...feature,
        properties: {
          ...feature.properties,
          tooltip: match?.title || title,
        },
      };
    });
  }, [features, data, title]);

  // Memoize static calculations
  const { centerX, centerY, scale } = useMemo(
    () => ({
      centerX: width / 2,
      centerY: height / 2,
      scale: (width + height) / 1.55,
    }),
    [width, height],
  );

  // Memoize color mapping function with colorRange
  const getColorMemo = useCallback(
    (val: string) => getColor(val, data, colorRange),
    [data, colorRange],
  );
  const getValueMemo = useCallback(
    (val: string) => getValue(val, data),
    [data],
  );

  // Event handlers
  const handleMouseEnter = useCallback(
    (feature: FeatureShape) => {
      setTooltip(feature.properties);
      setSelected(getValueMemo(feature.id));
    },
    [getValueMemo],
  );

  // Debounced mouse move handler
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const x = e.clientX - 100;
    const y = e.clientY - 100;
    requestAnimationFrame(() => {
      setPosition({ x, y });
    });
  }, []);

  const handleMouseLeave = useCallback(() => {
    setTooltip(null);
  }, []);

  // Memoize map features
  const renderedFeatures = useMemo(() => {
    if (features.length === 0) return null;

    return (
      <AlbersUsa<FeatureShape>
        data={enrichedFeatures}
        scale={scale}
        translate={[centerX, centerY - 25]}
      >
        {({ features }) =>
          features.map(({ feature, path }, i) => (
            <path
              key={`map-feature-${i}`}
              d={path || ""}
              fill={getColorMemo(feature.id)}
              stroke="#fff"
              strokeWidth={0.5}
              strokeLinejoin="round"
              strokeLinecap="round"
              className="transition-all duration-200 ease-in-out hover:cursor-pointer hover:fill-yellow-600"
              onMouseEnter={() => handleMouseEnter(feature)}
              onMouseMove={handleMouseMove}
              onMouseLeave={handleMouseLeave}
            />
          ))
        }
      </AlbersUsa>
    );
  }, [
    features,
    enrichedFeatures,
    scale,
    centerX,
    centerY,
    getColorMemo,
    handleMouseEnter,
    handleMouseMove,
    handleMouseLeave,
  ]);

  if (isLoading || isExternalLoading) {
    return (
      <div className="h-10 w-10 animate-spin rounded-full border-2 border-gray-900 border-b-transparent"></div>
    );
  }

  if (features.length === 0 || width < 10) return null;

  return (
    <div className="relative">
      {tooltip && (
        <div
          className="fixed z-50 w-48 rounded-lg bg-white p-2.5 text-sm shadow-[0px_4px_8px_-2px_rgba(33,31,28,0.08)]"
          style={{
            left: position.x,
            top: position.y,
            pointerEvents: "none",
          }}
        >
          <Tag>{titleCase(tooltip.tooltip || title)}</Tag>
          <div className="mt-2 flex items-end justify-between">
            <span className="font-archivo text-2xl font-semibold">
              {selected}
            </span>
            <span className="text-neutral-00 inline-flex items-center gap-0.5">
              <IconLocation size={16} className="text-neutral-600" />
              {tooltip.name}
            </span>
          </div>
        </div>
      )}
      <div className="overflow-hidden">
        <svg width={width} height={height} className="relative top-6">
          {renderedFeatures}
        </svg>
      </div>
    </div>
  );
};

export default React.memo(MapChart);
