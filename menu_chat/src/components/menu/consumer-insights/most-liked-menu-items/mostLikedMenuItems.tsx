import { twMerge } from "tailwind-merge";
import titleCase from "voca/title_case";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMostLikedMenuItems } from "@/api/menu_items";

interface MostLikedMenuItemsProps {
  className?: string;
}

interface MostLikedMenuItem {
  name: string;
  menu_mentions: number;
  pen_rate: number;
  liked_by: number;
}

const MostLikedMenuItems = ({ className }: MostLikedMenuItemsProps) => {
  const { data: session } = useSession();
  const { data: mostLikedMenuItems } = useQuery({
    queryFn: async() => {
      const data = await fetchMostLikedMenuItems({ auth: session?.user.authorization as string });
      return data;
    },
    queryKey: ["most-liked-menu-items"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Liked Menu Items by Consumers</Heading>
          <Info
            tooltipId="most-liked-menu-items-tooltip"
            content="Top 5 most liked menu items based on the survey conducted in February 2025. (130,452 participants)"
          />
        </div>
        <Link href="/menu-items/most-liked-menu-items">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Menu Mentions</Th>
            <Th>Penetration</Th>
            <Th>Liked By</Th>
          </Tr>
        </Thead>
        <Tbody>
          {mostLikedMenuItems?.map((item: MostLikedMenuItem, key: number) => (
            <Tr key={`item-${key}`}>
              <Td>
                <Tag>{titleCase(item.name)}</Tag>
              </Td>
              <Td>{item.menu_mentions}</Td>
              <Td>{item.pen_rate.toFixed(2)}%</Td>
              <Td>{item.liked_by.toFixed(2)}%</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostLikedMenuItems;
