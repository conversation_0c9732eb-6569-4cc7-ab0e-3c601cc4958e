import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";

interface MostLikedMenuItemsProps {
  className?: string;
}

const MostLikedMenuItems = ({ className }: MostLikedMenuItemsProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Liked Menu Items by Consumers</Heading>
          <Info
            tooltipId="most-liked-menu-items-tooltip"
            content="Top 5 most liked menu items based on the survey conducted in February 2025. (130,452 participants)"
          />
        </div>
        <Link href="/menu-items/most-liked-menu-items">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Menu Mentions</Th>
            <Th>Penetration</Th>
            <Th>Liked By</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag>Frapuccino</Tag>
            </Td>
            <Td>142</Td>
            <Td>5.44%</Td>
            <Td>85,44%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Lamb shank</Tag>
            </Td>
            <Td>105</Td>
            <Td>42.52%</Td>
            <Td>87,25%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Frittata</Tag>
            </Td>
            <Td>92</Td>
            <Td>5.44%</Td>
            <Td>78,45%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Cauliflower rice</Tag>
            </Td>
            <Td>92</Td>
            <Td>5.44%</Td>
            <Td>12,44%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Raspberry tart</Tag>
            </Td>
            <Td>101</Td>
            <Td>5.44%</Td>
            <Td>67,44%</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostLikedMenuItems;
