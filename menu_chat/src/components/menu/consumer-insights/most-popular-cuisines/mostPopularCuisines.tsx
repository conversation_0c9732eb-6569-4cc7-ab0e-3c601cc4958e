import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconChefHat } from "@/components/icons";

interface MostPopularCuisinesProps {
  className?: string;
}

const MostPopularCuisines = ({ className }: MostPopularCuisinesProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular Cuisines</Heading>
          <Info
            tooltipId="most-popular-cuisines-tooltip"
            content="Most popular cuisines by cuisine share and people who like it"
          />
        </div>
        <Link href="/menu-items/most-popular-cuisines">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Cuisine Share</Th>
            <Th>Liked By</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconChefHat size={16} className="text-neutral-500" />
                French
              </Tag>
            </Td>
            <Td>12.35%</Td>
            <Td>34.43%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconChefHat size={16} className="text-neutral-500" />
                Latin American
              </Tag>
            </Td>
            <Td>17.35%</Td>
            <Td>45.89%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconChefHat size={16} className="text-neutral-500" />
                African
              </Tag>
            </Td>
            <Td>27.35%</Td>
            <Td>102.34%</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostPopularCuisines;
