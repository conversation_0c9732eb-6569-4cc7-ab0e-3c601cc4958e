import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconChefHat } from "@/components/icons";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMostPopularCuisines } from "@/api/menu_items";

interface MostPopularCuisinesProps {
  className?: string;
}

interface Cuisine {
  name: string;
  share: number;
  liked_by: number;
}

const MostPopularCuisines = ({ className }: MostPopularCuisinesProps) => {
  const { data: session } = useSession();
    const { data: mostPopularCuisines } = useQuery({
      queryFn: async() => {
        const data = await fetchMostPopularCuisines({ auth: session?.user.authorization as string });
        return data;
      },
      queryKey: ["most-popularCuisines"],
      enabled: !!session?.user?.authorization,
    });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular Cuisines</Heading>
          <Info
            tooltipId="most-popular-cuisines-tooltip"
            content="Most popular cuisines by cuisine share and people who like it"
          />
        </div>
        <Link href="/menu-items/most-popular-cuisines">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Cuisine Share</Th>
            <Th>Liked By</Th>
          </Tr>
        </Thead>
        <Tbody>
          {mostPopularCuisines?.map((cuisine: Cuisine) => (
            <Tr key={cuisine.name}>
              <Td>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  {cuisine.name}
                </Tag>
              </Td>
              <Td>{cuisine.share}</Td>
              <Td>{cuisine.liked_by}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostPopularCuisines;
