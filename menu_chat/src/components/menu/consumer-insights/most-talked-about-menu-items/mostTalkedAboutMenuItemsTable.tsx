import { useState } from "react";
import {
  IconGraph,
  IconIncrease,
  IconD<PERSON>rease,
  IconMenuAdoption,
} from "@/components/icons";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import MenuItemsFilters from "@/components/menu/menuItemsFilters";
import SentimentBar from "@/components/charts/sentimentBar";

const MostTalkedAboutMenuItemsTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="pt-3 pb-30">
      <MenuItemsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>Name</Th>
              <Th>Meal Type</Th>
              <Th>Sentiments</Th>
              <Th>Menu Mentions</Th>
              <Th>Penetrations</Th>
              <Th>Social Mentions</Th>
              <Th>Change</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Tag>Ratatouille</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Hot appetizer
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>150</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Sushi tacos</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Entree
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>75</Td>
              <Td>32.31%</Td>
              <Td>2,404</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Kugel</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Beverage
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 40,
                    "somewhat-dislike": 15,
                    neutral: 25,
                    "somewhat-like": 15,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>105</Td>
              <Td>22.31%</Td>
              <Td>54,950</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Ratatouille</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Hot appetizer
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>150</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Sushi tacos</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Entree
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>75</Td>
              <Td>32.31%</Td>
              <Td>2,404</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Kugel</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Beverage
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 40,
                    "somewhat-dislike": 15,
                    neutral: 25,
                    "somewhat-like": 15,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>105</Td>
              <Td>22.31%</Td>
              <Td>54,950</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Ratatouille</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Hot appetizer
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>150</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Sushi tacos</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Entree
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>75</Td>
              <Td>32.31%</Td>
              <Td>2,404</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Kugel</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Beverage
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 40,
                    "somewhat-dislike": 15,
                    neutral: 25,
                    "somewhat-like": 15,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>105</Td>
              <Td>22.31%</Td>
              <Td>54,950</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Ratatouille</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Hot appetizer
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 25,
                    neutral: 25,
                    "somewhat-like": 25,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>150</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Sushi tacos</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Entree
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "somewhat-dislike": 10,
                    neutral: 45,
                    "somewhat-like": 20,
                    "strongly-like": 25,
                  }}
                />
              </Td>
              <Td>75</Td>
              <Td>32.31%</Td>
              <Td>2,404</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Kugel</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Beverage
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 40,
                    "somewhat-dislike": 15,
                    neutral: 25,
                    "somewhat-like": 15,
                    "strongly-like": 15,
                  }}
                />
              </Td>
              <Td>105</Td>
              <Td>22.31%</Td>
              <Td>54,950</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default MostTalkedAboutMenuItemsTable;
