import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import titleCase from "voca/title_case";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import SentimentBar from "@/components/charts/sentimentBar";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMostTalkedAboutMenuItems } from "@/api/menu_items";

interface MostLikedMenuItemsProps {
  className?: string;
}

interface MostTalkedAboutMenuItem {
  name: string;
  sentiments: Record<string, number>;
  pen_rate: number;
}

const MostTalkedAboutMenuItems = ({ className }: MostLikedMenuItemsProps) => {
  const { data: session } = useSession();
  const { data: mostTalkedAboutMenuItems } = useQuery({
    queryFn: async () => {
      const data = await fetchMostTalkedAboutMenuItems({
        auth: session?.user.authorization as string,
      });
      return data;
    },
    queryKey: ["most-talked-about-menu-items"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Talked About Menu Items</Heading>
          <Info
            tooltipId="most-talked-about-menu-items-tooltip"
            content="Top 5 most talked about ingredients and their sentiments. Data gathered from survey conducted in February 2025. (130,452 participants)"
          />
        </div>
        <Link href="/menu-items/most-talked-about-menu-items">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Sentiment</Th>
            <Th>Penetration</Th>
          </Tr>
        </Thead>
        <Tbody>
          {(mostTalkedAboutMenuItems || []).map(
            (item: MostTalkedAboutMenuItem, idx: number) => (
              <Tr key={`${item.name}-${idx}`}>
                <Td>
                  <Tag>{titleCase(item.name)}</Tag>
                </Td>
                <Td>
                  <SentimentBar
                    className="max-w-[300px]"
                    segments={item.sentiments}
                  />
                </Td>
                <Td>{item.pen_rate.toFixed(2)}%</Td>
              </Tr>
            ),
          )}
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostTalkedAboutMenuItems;
