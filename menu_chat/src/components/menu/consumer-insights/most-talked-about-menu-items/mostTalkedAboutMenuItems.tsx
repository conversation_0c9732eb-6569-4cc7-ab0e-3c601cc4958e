import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import SentimentBar from "@/components/charts/sentimentBar";

interface MostLikedMenuItemsProps {
  className?: string;
}

const MostTalkedAboutMenuItems = ({ className }: MostLikedMenuItemsProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Talked About Menu Items</Heading>
          <Info
            tooltipId="most-talked-about-menu-items-tooltip"
            content="Top 5 most talked about ingredients and their sentiments. Data gathered from social media on February 2025."
          />
        </div>
        <Link href="/menu-items/most-talked-about-menu-items">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Sentiment</Th>
            <Th>Penetration</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag>Scallion pancak...</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  "strongly-dislike": 10,
                  "somewhat-dislike": 25,
                  neutral: 25,
                  "somewhat-like": 25,
                  "strongly-like": 15,
                }}
              />
            </Td>
            <Td>2.52%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>BBQ pulled chic...</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  "somewhat-dislike": 10,
                  neutral: 45,
                  "somewhat-like": 20,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>5.44%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Coconut macar...</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  neutral: 60,
                  "somewhat-like": 15,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>5.44%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Chocolate brow...</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  neutral: 55,
                  "somewhat-like": 30,
                  "strongly-like": 15,
                }}
              />
            </Td>
            <Td>5.44%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Stuffed jalapen...</Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[300px]"
                segments={{
                  "somewhat-dislike": 15,
                  neutral: 40,
                  "somewhat-like": 20,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>67.44%</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostTalkedAboutMenuItems;
