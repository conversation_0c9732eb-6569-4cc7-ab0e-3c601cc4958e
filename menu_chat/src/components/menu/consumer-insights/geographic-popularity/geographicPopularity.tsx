import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { <PERSON><PERSON>ead<PERSON> } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import Link from "next/link";
import dynamic from "next/dynamic";
const MapChart = dynamic(() => import("@/components/charts/mapChart"), {
  ssr: false,
});

interface GeographicPopularityProps {
  className?: string;
}

// Sample data for the map
const mapData = [
  { id: "01", value: 120 },
  { id: "02", value: 95 },
  { id: "04", value: 59 },
  { id: "05", value: 110 },
  { id: "06", value: 140 },
  { id: "08", value: 135 },
  { id: "09", value: 125 },
  { id: "10", value: 98 },
  { id: "12", value: 130 },
  { id: "13", value: 145 },
  { id: "15", value: 87 },
  { id: "16", value: 103 },
  { id: "17", value: 138 },
  { id: "18", value: 110 },
  { id: "19", value: 105 },
  { id: "20", value: 101 },
  { id: "21", value: 115 },
  { id: "22", value: 102 },
  { id: "23", value: 93 },
  { id: "24", value: 127 },
  { id: "25", value: 120 },
  { id: "26", value: 132 },
  { id: "27", value: 110 },
  { id: "28", value: 98 },
  { id: "29", value: 119 },
  { id: "30", value: 90 },
  { id: "31", value: 97 },
  { id: "32", value: 104 },
  { id: "33", value: 89 },
  { id: "34", value: 122 },
  { id: "35", value: 100 },
  { id: "36", value: 130 },
  { id: "37", value: 135 },
  { id: "38", value: 86 },
  { id: "39", value: 128 },
  { id: "40", value: 98 },
  { id: "41", value: 125 },
  { id: "42", value: 136 },
  { id: "44", value: 87 },
  { id: "45", value: 102 },
  { id: "46", value: 82 },
  { id: "47", value: 108 },
  { id: "48", value: 145 },
  { id: "49", value: 112 },
  { id: "50", value: 75 },
  { id: "51", value: 140 },
  { id: "53", value: 132 },
  { id: "54", value: 80 },
  { id: "55", value: 119 },
  { id: "56", value: 80 },
];

const GeographicPopularity = ({ className }: GeographicPopularityProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Geographic Popularity</Heading>
          <Info
            tooltipId="geographic-popularity-tooltip"
            content="Most popular menu item in specific geographic region."
          />
        </div>
        <div className="flex items-center">
          <Link href="/menu-items/geographic-popularity">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={MENU_ADOPTION_CURVE_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="flex w-full items-center justify-center overflow-x-auto px-4 pt-4 pb-2">
        <MapChart title="bokchoy" data={mapData} height={487} width={600} />
      </div>
    </Card>
  );
};

export default GeographicPopularity;
