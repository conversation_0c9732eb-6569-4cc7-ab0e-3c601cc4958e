import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Link from "next/link";
import dynamic from "next/dynamic";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchGeographicPopularity } from "@/api/menu_items";
const MapChart = dynamic(() => import("@/components/charts/mapChart"), {
  ssr: false,
});

interface GeographicPopularityProps {
  className?: string;
}

const GeographicPopularity = ({ className }: GeographicPopularityProps) => {
  const [selected, setSelected] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { data: session } = useSession();
  const { data: geographicPopularity } = useQuery({
    queryFn: async () => {
      const data = await fetchGeographicPopularity({
        auth: session?.user.authorization as string,
      });
      setIsLoading(false);
      return data;
    },
    queryKey: ["geographic-popularity"],
    enabled: !!session?.user?.authorization,
  });

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Geographic Popularity</Heading>
          <Info
            tooltipId="geographic-popularity-tooltip"
            content="Most popular menu item in specific geographic region."
          />
        </div>
        <div className="flex items-center">
          <Link href="/menu-items/geographic-popularity">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="flex w-full items-center justify-center overflow-x-auto px-4 pt-4 pb-2">
        <MapChart
          title="bokchoy"
          isExternalLoading={isLoading}
          data={geographicPopularity || []}
          height={487}
          width={600}
        />
      </div>
    </Card>
  );
};

export default GeographicPopularity;
