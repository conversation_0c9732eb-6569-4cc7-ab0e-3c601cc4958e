import { useState } from "react";
import {
  IconGraph,
  IconIncrease,
  IconD<PERSON>rease,
  IconMenuAdoption,
} from "@/components/icons";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import MenuItemsFilters from "@/components/menu/menuItemsFilters";

const GeographicPopularityTable = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 100; // Example total results, replace with actual data

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <div className="pt-3 pb-30">
      <MenuItemsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>Name</Th>
              <Th>Meal Type</Th>
              <Th>Location</Th>
              <Th>Penetrations</Th>
              <Th>Social Mentions</Th>
              <Th>Change</Th>
              <Th>Liked By</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Tag>Ratatouille</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Hot appetizer
                </Tag>
              </Td>
              <Td>Colorado</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>66.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Sushi tacos</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Entree
                </Tag>
              </Td>
              <Td>San Francisco</Td>
              <Td>32.31%</Td>
              <Td>2,404</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>56.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Kugel</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Beverage
                </Tag>
              </Td>
              <Td>Colorado</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>66.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Ratatouille</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Hot appetizer
                </Tag>
              </Td>
              <Td>Colorado</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>66.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Sushi tacos</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Entree
                </Tag>
              </Td>
              <Td>San Francisco</Td>
              <Td>32.31%</Td>
              <Td>2,404</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>56.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Kugel</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Beverage
                </Tag>
              </Td>
              <Td>Colorado</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>66.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Ratatouille</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Hot appetizer
                </Tag>
              </Td>
              <Td>Colorado</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>66.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Sushi tacos</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Entree
                </Tag>
              </Td>
              <Td>San Francisco</Td>
              <Td>32.31%</Td>
              <Td>2,404</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>56.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Kugel</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Beverage
                </Tag>
              </Td>
              <Td>Colorado</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>66.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Ratatouille</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Hot appetizer
                </Tag>
              </Td>
              <Td>Colorado</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="blueOutline">
                  <IconGraph size={16} />
                  0,00%
                </Tag>
              </Td>
              <Td>66.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Sushi tacos</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Entree
                </Tag>
              </Td>
              <Td>San Francisco</Td>
              <Td>32.31%</Td>
              <Td>2,404</Td>
              <Td>
                <Tag variant="redOutline">
                  <IconDecrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>56.23%</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag>Kugel</Tag>
              </Td>
              <Td>
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  Beverage
                </Tag>
              </Td>
              <Td>Colorado</Td>
              <Td>20.32%</Td>
              <Td>94,950</Td>
              <Td>
                <Tag variant="greenOutline">
                  <IconIncrease size={16} />
                  5,70%
                </Tag>
              </Td>
              <Td>66.23%</Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default GeographicPopularityTable;
