import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconMenuAdoption } from "@/components/icons";

interface MostPopularMealTypeProps {
  className?: string;
}

const MostPopularMealType = ({ className }: MostPopularMealTypeProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular Meal Types</Heading>
          <Info
            tooltipId="most-popular-meal-types-tooltip"
            content="Most popular cuisines by cuisine share and people who like it"
          />
        </div>
        <Link href="/menu-items/most-popular-meal-types">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Penetration</Th>
            <Th>Liked By</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Alcoholic beverages
              </Tag>
            </Td>
            <Td>12.35%</Td>
            <Td>34.43%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Appetizers
              </Tag>
            </Td>
            <Td>17.35%</Td>
            <Td>45.89%</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Desserts
              </Tag>
            </Td>
            <Td>27.35%</Td>
            <Td>102.34%</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostPopularMealType;
