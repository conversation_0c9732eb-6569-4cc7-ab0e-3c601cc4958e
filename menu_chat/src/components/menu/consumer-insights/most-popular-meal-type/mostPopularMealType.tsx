import { twMerge } from "tailwind-merge";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconMenuAdoption } from "@/components/icons";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchPopularMealTypes } from "@/api/menu_items";

interface MostPopularMealTypeProps {
  className?: string;
}

interface PopularMealType {
  name: string;
  pen_rate: number;
  liked_by: number;
}

const MostPopularMealType = ({ className }: MostPopularMealTypeProps) => {
  const { data: session } = useSession();
    const { data: popularMealTypes } = useQuery({
      queryFn: async() => {
        const data = await fetchPopularMealTypes({ auth: session?.user.authorization as string });
        return data;
      },
      queryKey: ["popular-meal-types"],
      enabled: !!session?.user?.authorization,
    });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular Meal Types</Heading>
          <Info
            tooltipId="most-popular-meal-types-tooltip"
            content="Most popular cuisines by cuisine share and people who like it"
          />
        </div>
        <Link href="/menu-items/most-popular-meal-types">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Penetration</Th>
            <Th>Liked By</Th>
          </Tr>
        </Thead>
        <Tbody>
          {
            popularMealTypes?.map((mealType: PopularMealType, idx: number) => (
              <Tr key={`meal-type-${idx}`}>
                <Td>
                  <Tag variant="white">
                    <IconMenuAdoption size={16} className="text-neutral-500" />
                    {mealType.name}
                  </Tag>
                </Td>
                <Td>{mealType.pen_rate?.toFixed(2)}%</Td>
                <Td>{mealType.liked_by?.toFixed(2)}%</Td>
              </Tr>
            ))
          }
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostPopularMealType;
