import { twMerge } from "tailwind-merge";
import { useState } from "react";
import Paragraph from "@/components/base/paragraph";
import Card from "@/components/base/card";
import Tag from "@/components/base/tag";
import { IconLocation, IconMenuAdoption } from "@/components/icons";
import Image from "next/image";
import DataSourcesReports from "@/components/shared/dataSourcesReports";
import MenuItemsFilters from "@/components/menu/menuItemsFilters";
import { useSession } from "next-auth/react";
import titleCase from "voca/title_case";
import { useQuery } from "@tanstack/react-query";
import { fetchMenuItemItems } from "@/api/menu_items";
import { useMenuItem } from "@/contexts/MenuItemContext";
import Pagination from "@/components/table/pagination";
import Link from "next/link";

enum RestaurantType {
  qsr = "QSR",
  fast_casual = "Fast Casual",
  mid_scale = "Mid Scale",
  fine_dining = "Fine Dining",
  other = "Other",
}

function truncateString(str: string, maxLength = 40) {
  if (str.length <= maxLength) return str;
  return str.slice(0, maxLength - 3) + "...";
}

const MenuItemsDetailsOnMenus = () => {
  const { data: session } = useSession();
  const { guid } = useMenuItem();
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);

  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");

  const { data: menu_item_items } = useQuery({
    queryFn: () => {
      return fetchMenuItemItems({
        auth: session?.user.authorization as string,
        guid: guid,
        page,
        pageSize,
        secondary_filter: search,
      });
    },
    queryKey: ["menu_item_items", guid, page, pageSize, search],
    enabled: !!session?.user?.authorization,
  });

  const itemsMap = menu_item_items?.data.map((item: MenuItem) => {
    const {
      id,
      name,
      price,
      description,
      business_name,
      restaurant_type,
      restaurant_guid,
      cuisine,
      city,
    } = item;
    let { image_url } = item;
    if (image_url === "[nil]" || image_url === null) {
      image_url = "";
    }

    return (
      <Card key={id} className="p-3">
        <Image
          src={image_url ? image_url.trim() : ""}
          alt={name}
          width={100}
          height={232}
          className="mb-1 h-[140px] w-full rounded-lg object-cover"
        />
        <div className="mb-2 rounded-lg border border-neutral-200 p-2">
          <div className="mb-2 flex items-center justify-between gap-2">
            <Tag className={'max-w-full-75px'}>
              <span className="inline-block truncate">
                {titleCase(name)}
              </span>
            </Tag>
            <Paragraph size="lg" className="font-semibold">
              {price > 0 && `$${price}`}
            </Paragraph>
          </div>
          <Paragraph size="xs" className="font-medium text-neutral-600">
            {titleCase(truncateString(description))}
          </Paragraph>
        </div>
        <Paragraph
          size="sm"
          className="mb-1 flex items-center gap-2 px-2 font-medium"
        >
          <IconLocation size={16} className="text-neutral-500" />
          <Link href={`/menu-items/restaurant/${restaurant_guid}`}>
            {titleCase(business_name)}
          </Link>
        </Paragraph>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
              Type
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {RestaurantType[restaurant_type as keyof typeof RestaurantType]}
            </Paragraph>
          </div>
        </div>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
              Cuisine
            </Paragraph>
            <Tag variant="white">
              <IconMenuAdoption size={16} className="text-neutral-500" />
              {cuisine.length > 0 ? titleCase(cuisine[0]) : ""}
            </Tag>
          </div>
        </div>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
              Location
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {titleCase(city)}
            </Paragraph>
          </div>
        </div>
      </Card>
    );
  });

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPageSize(pageSize);
  };

  return (
    <section className="mt-[60px] py-4">
      <MenuItemsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div
        className={twMerge("flex flex-col gap-5", filters.length > 0 && "mt-3")}
      >
        <div className="grid grid-cols-4 gap-5">{itemsMap}</div>
        <Pagination
          currentPage={page}
          perPage={pageSize}
          totalResults={menu_item_items?.total_records}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
        <DataSourcesReports />
      </div>
    </section>
  );
};

/*<Card className="p-3">
  <Image
    src={"/assets/images/<EMAIL>"}
    alt="chocolates"
    width={100}
    height={232}
    className="mb-1 h-[140px] w-full rounded-lg object-cover"
  />
  <div className="mb-2 rounded-lg border border-neutral-200 p-2">
    <div className="mb-2 flex items-center justify-between gap-2">
      <Tag>Espadon* (Boucho...</Tag>
      <Paragraph size="lg" className="font-semibold">
        $12
      </Paragraph>
    </div>
    <Paragraph size="xs" className="font-medium text-neutral-600">
      Pan-seared swordfish with ratatouille...
    </Paragraph>
  </div>
  <Paragraph
    size="sm"
    className="mb-1 flex items-center gap-2 px-2 font-medium"
  >
    <IconLocation size={16} className="text-neutral-500" />
    Savor & Sage
  </Paragraph>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Type
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        Fast Casual
      </Paragraph>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Cuisine
      </Paragraph>
      <Tag variant="white">
        <IconMenuAdoption size={16} className="text-neutral-500" />
        Japanese
      </Tag>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Location
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        New York
      </Paragraph>
    </div>
  </div>
</Card>
<Card className="p-3">
  <Image
    src={"/assets/images/<EMAIL>"}
    alt="chocolates"
    width={100}
    height={232}
    className="mb-1 h-[140px] w-full rounded-lg object-cover"
  />
  <div className="mb-2 rounded-lg border border-neutral-200 p-2">
    <div className="mb-2 flex items-center justify-between gap-2">
      <Tag>Espadon* (Boucho...</Tag>
      <Paragraph size="lg" className="font-semibold">
        $12
      </Paragraph>
    </div>
    <Paragraph size="xs" className="font-medium text-neutral-600">
      Pan-seared swordfish with ratatouille...
    </Paragraph>
  </div>
  <Paragraph
    size="sm"
    className="mb-1 flex items-center gap-2 px-2 font-medium"
  >
    <IconLocation size={16} className="text-neutral-500" />
    Savor & Sage
  </Paragraph>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Type
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        Fast Casual
      </Paragraph>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Cuisine
      </Paragraph>
      <Tag variant="white">
        <IconMenuAdoption size={16} className="text-neutral-500" />
        Japanese
      </Tag>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Location
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        New York
      </Paragraph>
    </div>
  </div>
</Card>
<Card className="p-3">
  <Image
    src={"/assets/images/<EMAIL>"}
    alt="chocolates"
    width={100}
    height={232}
    className="mb-1 h-[140px] w-full rounded-lg object-cover"
  />
  <div className="mb-2 rounded-lg border border-neutral-200 p-2">
    <div className="mb-2 flex items-center justify-between gap-2">
      <Tag>Espadon* (Boucho...</Tag>
      <Paragraph size="lg" className="font-semibold">
        $12
      </Paragraph>
    </div>
    <Paragraph size="xs" className="font-medium text-neutral-600">
      Pan-seared swordfish with ratatouille...
    </Paragraph>
  </div>
  <Paragraph
    size="sm"
    className="mb-1 flex items-center gap-2 px-2 font-medium"
  >
    <IconLocation size={16} className="text-neutral-500" />
    Savor & Sage
  </Paragraph>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Type
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        Fast Casual
      </Paragraph>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Cuisine
      </Paragraph>
      <Tag variant="white">
        <IconMenuAdoption size={16} className="text-neutral-500" />
        Japanese
      </Tag>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Location
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        New York
      </Paragraph>
    </div>
  </div>
</Card>
<Card className="p-3">
  <Image
    src={"/assets/images/<EMAIL>"}
    alt="chocolates"
    width={100}
    height={232}
    className="mb-1 h-[140px] w-full rounded-lg object-cover"
  />
  <div className="mb-2 rounded-lg border border-neutral-200 p-2">
    <div className="mb-2 flex items-center justify-between gap-2">
      <Tag>Espadon* (Boucho...</Tag>
      <Paragraph size="lg" className="font-semibold">
        $12
      </Paragraph>
    </div>
    <Paragraph size="xs" className="font-medium text-neutral-600">
      Pan-seared swordfish with ratatouille...
    </Paragraph>
  </div>
  <Paragraph
    size="sm"
    className="mb-1 flex items-center gap-2 px-2 font-medium"
  >
    <IconLocation size={16} className="text-neutral-500" />
    Savor & Sage
  </Paragraph>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Type
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        Fast Casual
      </Paragraph>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Cuisine
      </Paragraph>
      <Tag variant="white">
        <IconMenuAdoption size={16} className="text-neutral-500" />
        Japanese
      </Tag>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Location
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        New York
      </Paragraph>
    </div>
  </div>
</Card>
<Card className="p-3">
  <Image
    src={"/assets/images/<EMAIL>"}
    alt="chocolates"
    width={100}
    height={232}
    className="mb-1 h-[140px] w-full rounded-lg object-cover"
  />
  <div className="mb-2 rounded-lg border border-neutral-200 p-2">
    <div className="mb-2 flex items-center justify-between gap-2">
      <Tag>Espadon* (Boucho...</Tag>
      <Paragraph size="lg" className="font-semibold">
        $12
      </Paragraph>
    </div>
    <Paragraph size="xs" className="font-medium text-neutral-600">
      Pan-seared swordfish with ratatouille...
    </Paragraph>
  </div>
  <Paragraph
    size="sm"
    className="mb-1 flex items-center gap-2 px-2 font-medium"
  >
    <IconLocation size={16} className="text-neutral-500" />
    Savor & Sage
  </Paragraph>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Type
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        Fast Casual
      </Paragraph>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Cuisine
      </Paragraph>
      <Tag variant="white">
        <IconMenuAdoption size={16} className="text-neutral-500" />
        Japanese
      </Tag>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Location
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        New York
      </Paragraph>
    </div>
  </div>
</Card>
<Card className="p-3">
  <Image
    src={"/assets/images/<EMAIL>"}
    alt="chocolates"
    width={100}
    height={232}
    className="mb-1 h-[140px] w-full rounded-lg object-cover"
  />
  <div className="mb-2 rounded-lg border border-neutral-200 p-2">
    <div className="mb-2 flex items-center justify-between gap-2">
      <Tag>Espadon* (Boucho...</Tag>
      <Paragraph size="lg" className="font-semibold">
        $12
      </Paragraph>
    </div>
    <Paragraph size="xs" className="font-medium text-neutral-600">
      Pan-seared swordfish with ratatouille...
    </Paragraph>
  </div>
  <Paragraph
    size="sm"
    className="mb-1 flex items-center gap-2 px-2 font-medium"
  >
    <IconLocation size={16} className="text-neutral-500" />
    Savor & Sage
  </Paragraph>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Type
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        Fast Casual
      </Paragraph>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Cuisine
      </Paragraph>
      <Tag variant="white">
        <IconMenuAdoption size={16} className="text-neutral-500" />
        Japanese
      </Tag>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Location
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        New York
      </Paragraph>
    </div>
  </div>
</Card>
<Card className="p-3">
  <Image
    src={"/assets/images/<EMAIL>"}
    alt="chocolates"
    width={100}
    height={232}
    className="mb-1 h-[140px] w-full rounded-lg object-cover"
  />
  <div className="mb-2 rounded-lg border border-neutral-200 p-2">
    <div className="mb-2 flex items-center justify-between gap-2">
      <Tag>Espadon* (Boucho...</Tag>
      <Paragraph size="lg" className="font-semibold">
        $12
      </Paragraph>
    </div>
    <Paragraph size="xs" className="font-medium text-neutral-600">
      Pan-seared swordfish with ratatouille...
    </Paragraph>
  </div>
  <Paragraph
    size="sm"
    className="mb-1 flex items-center gap-2 px-2 font-medium"
  >
    <IconLocation size={16} className="text-neutral-500" />
    Savor & Sage
  </Paragraph>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Type
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        Fast Casual
      </Paragraph>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Cuisine
      </Paragraph>
      <Tag variant="white">
        <IconMenuAdoption size={16} className="text-neutral-500" />
        Japanese
      </Tag>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Location
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        New York
      </Paragraph>
    </div>
  </div>
</Card>
<Card className="p-3">
  <Image
    src={"/assets/images/<EMAIL>"}
    alt="chocolates"
    width={100}
    height={232}
    className="mb-1 h-[140px] w-full rounded-lg object-cover"
  />
  <div className="mb-2 rounded-lg border border-neutral-200 p-2">
    <div className="mb-2 flex items-center justify-between gap-2">
      <Tag>Espadon* (Boucho...</Tag>
      <Paragraph size="lg" className="font-semibold">
        $12
      </Paragraph>
    </div>
    <Paragraph size="xs" className="font-medium text-neutral-600">
      Pan-seared swordfish with ratatouille...
    </Paragraph>
  </div>
  <Paragraph
    size="sm"
    className="mb-1 flex items-center gap-2 px-2 font-medium"
  >
    <IconLocation size={16} className="text-neutral-500" />
    Savor & Sage
  </Paragraph>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Type
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        Fast Casual
      </Paragraph>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Cuisine
      </Paragraph>
      <Tag variant="white">
        <IconMenuAdoption size={16} className="text-neutral-500" />
        Japanese
      </Tag>
    </div>
  </div>
  <div className="flex w-full flex-col">
    <div className="flex items-center justify-between gap-2 px-3 py-1">
      <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
        Location
      </Paragraph>
      <Paragraph size="sm" className="font-medium">
        New York
      </Paragraph>
    </div>
  </div>
</Card>*/

export default MenuItemsDetailsOnMenus;
