import { useState } from "react";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";

import { fetchRegionsDistro } from "@/api/menu_items";

import RegionMapChart from "@/components/charts/regionMapChart";
import { useSession } from "next-auth/react";

import { useQuery } from "@tanstack/react-query";

import { useMenuItem } from "@/contexts/MenuItemContext";

//
// // Sample data for the map
// const mapData = [
//   { id: "01", value: 1200 },
//   { id: "02", value: 950 },
//   { id: "04", value: 599 },
//   { id: "05", value: 1100 },
//   { id: "06", value: 1400 },
//   { id: "08", value: 1350 },
//   { id: "09", value: 1250 },
//   { id: "10", value: 980 },
//   { id: "12", value: 1300 },
//   { id: "13", value: 1450 },
//   { id: "15", value: 870 },
//   { id: "16", value: 1030 },
//   { id: "17", value: 1380 },
//   { id: "18", value: 1100 },
//   { id: "19", value: 1050 },
//   { id: "20", value: 1010 },
//   { id: "21", value: 1150 },
//   { id: "22", value: 1020 },
//   { id: "23", value: 930 },
//   { id: "24", value: 1270 },
//   { id: "25", value: 1200 },
//   { id: "26", value: 1320 },
//   { id: "27", value: 1100 },
//   { id: "28", value: 980 },
//   { id: "29", value: 1190 },
//   { id: "30", value: 900 },
//   { id: "31", value: 970 },
//   { id: "32", value: 1040 },
//   { id: "33", value: 890 },
//   { id: "34", value: 1220 },
//   { id: "35", value: 1000 },
//   { id: "36", value: 1300 },
//   { id: "37", value: 1350 },
//   { id: "38", value: 860 },
//   { id: "39", value: 1280 },
//   { id: "40", value: 980 },
//   { id: "41", value: 1250 },
//   { id: "42", value: 1360 },
//   { id: "44", value: 870 },
//   { id: "45", value: 1020 },
//   { id: "46", value: 820 },
//   { id: "47", value: 1080 },
//   { id: "48", value: 1450 },
//   { id: "49", value: 1120 },
//   { id: "50", value: 750 },
//   { id: "51", value: 1400 },
//   { id: "53", value: 1320 },
//   { id: "54", value: 870 },
//   { id: "55", value: 1190 },
//   { id: "56", value: 800 },
// ];

const GeographicPopularity = () => {
  const { data: session } = useSession();

  const {  guid } = useMenuItem();

  const { data: menu_items_regions_distro } = useQuery({
    queryFn: () => {
      return fetchRegionsDistro({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["menu_items_regions_distro", guid],
    enabled: !!session?.user?.authorization,
  });


  const mapData = menu_items_regions_distro?.map((item: {id: string, pen_rate: number, region: string}) => {
    return {
      id: item.id,
      value: item.pen_rate,
      region: item.region
    }
  }) || []

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className="p-4">
      <div className="flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center">
          <Heading level={5}>Geographic Popularity</Heading>
          <Info
            tooltipId="geographic-popularity"
            content="Penetration Rates Across Regions in the US."
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
      <div className="flex w-full items-center justify-center overflow-x-auto">
        {/*<MapChart title="bokchoy" data={mapData} height={520} />*/}
        <RegionMapChart title={`Pen Rate`} data={mapData} height={520} />
      </div>
    </Card>
  );
};

export default GeographicPopularity;
