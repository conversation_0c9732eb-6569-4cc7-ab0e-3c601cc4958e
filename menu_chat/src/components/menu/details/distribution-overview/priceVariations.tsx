import { useState } from "react";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import {
  IconMore,
  IconDecrease,
  IconIncrease,
  IconGraph,
} from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import PriceVariationsCard from "./priceVariationsCard";
import { useSession } from "next-auth/react";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { useQuery } from "@tanstack/react-query";
import { fetchPriceVariation } from "@/api/menu_items";
import titleCase from "voca/title_case";

const generateRateTagWithIconDollar = (rate: number) => {
  if(rate > 0){
    return <Tag variant="greenOutline">
      <IconIncrease size={16} />
      ${rate?.toFixed(2)}
    </Tag>
  }
  else if (rate == 0){
    return <Tag variant="blueOutline">
      <IconGraph size={16} />
      ${rate?.toFixed(2)}
    </Tag>
  } else {
    return <Tag variant="redOutline">
      <IconDecrease size={16} />
      ${rate?.toFixed(2)}
    </Tag>
  }
}

interface PriceVariationItem {
  "name": string,
  "median_price": number,
  "median_price_diff_dollar": number,
  "median_price_diff_percent": number,
}

const generateRateTagWithIconPercent = (rate: number) => {
  if(rate > 0){
    return <Tag variant="greenOutline">
      <IconIncrease size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  }
  else if (rate == 0){
    return <Tag variant="blueOutline">
      <IconGraph size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  } else {
    return <Tag variant="redOutline">
      <IconDecrease size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  }
}

const PriceVariations = () => {
  const { data: session } = useSession();
  const {  name, guid } = useMenuItem();

  const { data: price_variatons_response } = useQuery({
    queryFn: () => {
      return fetchPriceVariation({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["price_variatons", guid],
    enabled: !!session?.user?.authorization,
  });

  const rowsMap = price_variatons_response?.price_variation_chart.map((item: PriceVariationItem)=>{
    return (<Tr key={item.name}>
      <Td>
        <Tag>{titleCase(item.name)}</Tag>
      </Td>
      <Td>${item.median_price?.toFixed(2)}</Td>
      <Td>
        {generateRateTagWithIconDollar(item.median_price_diff_dollar)}
      </Td>
      <Td>
        {generateRateTagWithIconPercent(item.median_price_diff_percent)}
      </Td>
    </Tr>
    )
  })

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={5}>
            Price Variations for Top Additional Ingredients
          </Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="flex gap-8 p-4 pb-1">
        <div className="w-[27%]">
          <PriceVariationsCard
            name={titleCase(name) || ""}
            medianPrice={`$${price_variatons_response?.median_price?.toFixed(2) || ''}` || ""}
            ingredients={[]}
          />
        </div>
        <div className="w-[73%]">
          <Table>
            <Thead>
              <Tr>
                <Th>Name</Th>
                <Th>Median Price</Th>
                <Th>Difference ($)</Th>
                <Th>Difference (%)</Th>
              </Tr>
            </Thead>
            <Tbody>
              {
                rowsMap
              }
              {/*<Tr>
                <Td>
                  <Tag>Bacon</Tag>
                </Td>
                <Td>$16,29</Td>
                <Td>10.35%</Td>
                <Td>10.35%</Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    $4.94
                  </Tag>
                </Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    43.52%
                  </Tag>
                </Td>
              </Tr>
              <Tr>
                <Td>
                  <Tag>Ham</Tag>
                </Td>
                <Td>$14,84</Td>
                <Td>8.49%</Td>
                <Td>8.49%</Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    $3.49
                  </Tag>
                </Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    30.75%
                  </Tag>
                </Td>
              </Tr>
              <Tr>
                <Td>
                  <Tag>Scrambled Egg</Tag>
                </Td>
                <Td>$14,99</Td>
                <Td>9.69%</Td>
                <Td>9.69%</Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    $3.64
                  </Tag>
                </Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    32.07%
                  </Tag>
                </Td>
              </Tr>
              <Tr>
                <Td>
                  <Tag>Butter</Tag>
                </Td>
                <Td>$12,59</Td>
                <Td>12.45%</Td>
                <Td>12.45%</Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    $1.24
                  </Tag>
                </Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    10.93%
                  </Tag>
                </Td>
              </Tr>
              <Tr>
                <Td>
                  <Tag>Mayonnaise</Tag>
                </Td>
                <Td>$12,19</Td>
                <Td>7.28%</Td>
                <Td>7.28%</Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    $0.84
                  </Tag>
                </Td>
                <Td>
                  <Tag variant="redOutline">
                    <IconDecrease size={16} />
                    7.40%
                  </Tag>
                </Td>
              </Tr>*/}
            </Tbody>
          </Table>
        </div>
      </div>
    </Card>
  );
};

export default PriceVariations;
