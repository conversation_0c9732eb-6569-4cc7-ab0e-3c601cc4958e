import { useState, useEffect } from "react";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import {
  IconMore,
  IconWorkflow,
  IconGraph,
  IconIncrease,
  IconDecrease,
} from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { useSession } from "next-auth/react";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { useQuery } from "@tanstack/react-query";
import { fetchTopIngredients } from "@/api/menu_items";
import titleCase from "voca/title_case";


interface IngredientsResponse {
  gmi_ingredient_id: number,
  category: string,
  name: string,
  distribution: number,
  growth_rate: number,
}

function truncateString(str: string, maxLength = 40) {
  if (str.length <= maxLength) return str;
  return str.slice(0, maxLength - 3) + "...";
}


const generateRateTagWithIcon = (rate: number) => {
  if(rate > 0){
    return <Tag variant="greenOutline">
      <IconIncrease size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  }
  else if (rate == 0){
    return <Tag variant="blueOutline">
      <IconGraph size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  } else {
    return <Tag variant="redOutline">
      <IconDecrease size={16} />
      {rate?.toFixed(2)}%
    </Tag>
  }
}

const TopIngredients = () => {
  const [selected, setSelected] = useState<string>("");

  const { data: session } = useSession();
  const {  guid } = useMenuItem();

  const { data: top_ingredients } = useQuery({
    queryFn: () => {
      return fetchTopIngredients({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["top_ingredients", guid],
    enabled: !!session?.user?.authorization,
  });

  const [ingredientsMap, setIngredientsMap] = useState([]);

  useEffect(()=>{
    const rowsMap = top_ingredients?.map((item: IngredientsResponse)=>{
      return (<Tr key={item.gmi_ingredient_id}>
        <Td>
          <Tag>{titleCase(item.name)}</Tag>
        </Td>
        <Td>
          <Tag variant="white">
            <IconWorkflow size={16} className="text-neutral-500" />
            {truncateString(titleCase(item.category), 30)}
          </Tag>
        </Td>
        <Td>
          {(item.distribution * 100).toFixed(2)}%
        </Td>
        <Td>
          {generateRateTagWithIcon(item.growth_rate)}
        </Td>
      </Tr>)
    }) || []
    setIngredientsMap(rowsMap)
  }, [top_ingredients, guid])



  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={5}>Top Ingredients</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Category</Th>
            <Th>Distribution</Th>
            <Th>Growth</Th>
          </Tr>
        </Thead>
        <Tbody>
          {ingredientsMap}
          {/*<Tr>
            <Td>
              <Tag>Eggplant</Tag>
            </Td>
            <Td>
              <Tag variant="white">
                <IconWorkflow size={16} className="text-neutral-500" />
                Vegetables
              </Tag>
            </Td>
            <Td>56.35%</Td>
            <Td>
              <Tag variant="greenOutline">
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Zucchini</Tag>
            </Td>
            <Td>
              <Tag variant="white">
                <IconWorkflow size={16} className="text-neutral-500" />
                Vegetables
              </Tag>
            </Td>
            <Td>2.52%</Td>
            <Td>
              <Tag variant="blueOutline">
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Bell Peppers</Tag>
            </Td>
            <Td>
              <Tag variant="white">
                <IconWorkflow size={16} className="text-neutral-500" />
                Vegetables
              </Tag>
            </Td>
            <Td>2.52%</Td>
            <Td>
              <Tag variant="redOutline">
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Tomato</Tag>
            </Td>
            <Td>
              <Tag variant="white">
                <IconWorkflow size={16} className="text-neutral-500" />
                Vegetables
              </Tag>
            </Td>
            <Td>56.35%</Td>
            <Td>
              <Tag variant="blueOutline">
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Onion</Tag>
            </Td>
            <Td>
              <Tag variant="white">
                <IconWorkflow size={16} className="text-neutral-500" />
                Vegetables
              </Tag>
            </Td>
            <Td>2.52%</Td>
            <Td>
              <Tag variant="redOutline">
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>*/}
        </Tbody>
      </Table>
    </Card>
  );
};

export default TopIngredients;
