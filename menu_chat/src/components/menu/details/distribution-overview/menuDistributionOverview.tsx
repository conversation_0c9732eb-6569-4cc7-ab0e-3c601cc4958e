import TopIngredients from "./topIngredients";
import TopRestaurantTypesDistribution from "./topRestaurantTypesDistribution";
import GeographicPopularity from "./geographicPopularity";
import TopCuisinesDistribution from "./topCuisinesDistribution";
import ChainTypesDistribution from "./chainTypesDistribution";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const MenuDistributionOverview = () => {
  return (
    <section className="mt-[60px] py-4">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-2 items-start gap-5">
          <TopIngredients />
          <TopRestaurantTypesDistribution />
        </div>
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-7">
            <GeographicPopularity />
          </div>
          <div className="col-span-5">
            <div className="flex flex-col gap-5">
              <TopCuisinesDistribution />
              <ChainTypesDistribution />
            </div>
          </div>
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default MenuDistributionOverview;
