import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import DonutChart from "@/components/charts/donutChart";

const chartData = [
  { label: "Mediterranean", value: 28, color: "#FFBE05" },
  { label: "Vegan", value: 32, color: "#8BC539" },
  { label: "French", value: 33, color: "#D273D8" },
  { label: "Italian", value: 12, color: "#2A9DCB" },
];

interface PieChartData {
  label: string;
  value: number;
  color: string;
}

const TopCuisinesDistribution = () => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={5}>Top Cuisines Distribution</Heading>
          <Info
            tooltipId="most-popular-meal-type-info"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
              Adipisci rem sit molestias officia mollitia"
          />
        </div>
      </CardHeader>
      <div className="p-4">
        <div className="border-muted-100 flex min-h-[230px] items-center justify-between gap-5 rounded-lg border px-3 py-6">
          <DonutChart data={chartData} size={170} showLabel={false} />
          <div className="flex w-full flex-1 flex-col gap-1 text-xs font-medium">
            {chartData.map((item: PieChartData, index) => (
              <div
                key={index}
                className="border-muted-500/30 flex items-center justify-between border-b px-1 py-1.5"
              >
                <span className="flex items-center gap-1">
                  <span
                    className="h-3 w-1 rounded-full"
                    style={{ backgroundColor: item.color }}
                  ></span>
                  {item.label}
                </span>
                <span className="text-neutral-600">{item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default TopCuisinesDistribution;
