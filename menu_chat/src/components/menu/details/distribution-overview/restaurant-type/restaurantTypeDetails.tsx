import { useState } from "react";
import { useRouter } from "next/router";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import RestaurantTypeDetailsFilters from "@/components/menu/details/distribution-overview/restaurant-type/restaurantTypeDetailsFilters";
import SentimentBar from "@/components/charts/sentimentBar";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { useRestaurantTypeNavigation } from "@/contexts/RestaurantTypeNavigationContext";

const RestaurantTypeDetails = () => {
  const router = useRouter();
  const { guid: menuItemGuid, name: menuItemName } = useMenuItem();
  const { restaurantType } = router.query;
  const { setRestaurantTypeNavigation } = useRestaurantTypeNavigation();
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const totalResults = 2000; // Total restaurant brands

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const handleRestaurantClick = (restaurantName: string) => {
    // TODO: Replace with actual restaurant ID/guid from API data
    // For now, using a placeholder ID that should be replaced with real data
    // restaurantName can be used to look up the actual ID from your data

    const restaurantId = "021a8c9c-f6a3-4672-9afe-f811b09095a3"; // Temporary working ID

    // Set navigation context data instead of using URL parameters
    setRestaurantTypeNavigation(
      menuItemName,
      menuItemGuid,
      restaurantType as string,
      restaurantName,
      "menu-items",
    );

    // Navigate to restaurant page - context data is now available
    router.push(`/menu-items/restaurant/${restaurantId}`);
  };

  return (
    <div className="py-3">
      <RestaurantTypeDetailsFilters
        search={search}
        onSearch={setSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <Table>
          <Thead>
            <Tr>
              <Th>NAME</Th>
              <Th>SENTIMENT</Th>
              <Th>LOCATIONS NUMBER</Th>
              <Th>MENU ITEMS NUMBER</Th>
              <Th>SOCIAL MENTIONS</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("McDonald's")}
                >
                  McDonald&apos;s
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 0,
                    "somewhat-dislike": 0,
                    neutral: 20,
                    "somewhat-like": 50,
                    "strongly-like": 30,
                  }}
                />
              </Td>
              <Td>40,439</Td>
              <Td>68</Td>
              <Td>94,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Subway")}
                >
                  Subway
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 30,
                    neutral: 20,
                    "somewhat-like": 10,
                    "strongly-like": 30,
                  }}
                />
              </Td>
              <Td>37,438</Td>
              <Td>39</Td>
              <Td>52,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Starbucks")}
                >
                  Starbucks
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 60,
                    "somewhat-dislike": 35,
                    neutral: 5,
                    "somewhat-like": 0,
                    "strongly-like": 0,
                  }}
                />
              </Td>
              <Td>38,342</Td>
              <Td>82</Td>
              <Td>34,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("KFC")}
                >
                  KFC
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 0,
                    "somewhat-dislike": 50,
                    neutral: 30,
                    "somewhat-like": 0,
                    "strongly-like": 20,
                  }}
                />
              </Td>
              <Td>27,842</Td>
              <Td>73</Td>
              <Td>24,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Burger King")}
                >
                  Burger King
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 30,
                    neutral: 20,
                    "somewhat-like": 10,
                    "strongly-like": 30,
                  }}
                />
              </Td>
              <Td>19,345</Td>
              <Td>62</Td>
              <Td>74,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Domino's Pizza")}
                >
                  Domino&apos;s Pizza
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 30,
                    neutral: 20,
                    "somewhat-like": 10,
                    "strongly-like": 30,
                  }}
                />
              </Td>
              <Td>20,832</Td>
              <Td>36</Td>
              <Td>74,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Chick-fil-A")}
                >
                  Chick-fil-A
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 40,
                    "somewhat-dislike": 60,
                    neutral: 0,
                    "somewhat-like": 0,
                    "strongly-like": 0,
                  }}
                />
              </Td>
              <Td>18,934</Td>
              <Td>29</Td>
              <Td>94,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Tim Hortons")}
                >
                  Tim Hortons
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 60,
                    "somewhat-dislike": 35,
                    neutral: 5,
                    "somewhat-like": 0,
                    "strongly-like": 0,
                  }}
                />
              </Td>
              <Td>3,945</Td>
              <Td>38</Td>
              <Td>14,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Popeye's")}
                >
                  Popeye&apos;s
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 10,
                    "somewhat-dislike": 30,
                    neutral: 20,
                    "somewhat-like": 10,
                    "strongly-like": 30,
                  }}
                />
              </Td>
              <Td>5,284</Td>
              <Td>24</Td>
              <Td>54,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Jollibee")}
                >
                  Jollibee
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 40,
                    "somewhat-dislike": 60,
                    neutral: 0,
                    "somewhat-like": 0,
                    "strongly-like": 0,
                  }}
                />
              </Td>
              <Td>4,239</Td>
              <Td>36</Td>
              <Td>94,265</Td>
            </Tr>
            <Tr>
              <Td>
                <Tag
                  variant="restaurantSm"
                  className="text-md"
                  onClick={() => handleRestaurantClick("Dunkin'")}
                >
                  Dunkin&apos;
                </Tag>
              </Td>
              <Td>
                <SentimentBar
                  className="max-w-[300px]"
                  segments={{
                    "strongly-dislike": 60,
                    "somewhat-dislike": 35,
                    neutral: 5,
                    "somewhat-like": 0,
                    "strongly-like": 0,
                  }}
                />
              </Td>
              <Td>1,583</Td>
              <Td>51</Td>
              <Td>14,265</Td>
            </Tr>
          </Tbody>
        </Table>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={setPage}
          onPerPageChange={setPerPage}
        />
      </div>
    </div>
  );
};

export default RestaurantTypeDetails;
