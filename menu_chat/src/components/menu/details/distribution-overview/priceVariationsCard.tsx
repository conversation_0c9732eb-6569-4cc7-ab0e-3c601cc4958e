import Heading from "@/components/base/heading";
// import Tag from "@/components/base/tag";
import Paragraph from "@/components/base/paragraph";
// import Tooltip from "@/components/base/tooltip";
// import { IconDecrease, IconGraph, IconIncrease } from "@/components/icons";

interface PriceVariationsCardProps {
  name: string;
  medianPrice: string;
  ingredients: string[];
  maxDisplayed?: number;
}



const PriceVariationsCard = ({
  name,
  medianPrice,
  // ingredients,
  // maxDisplayed = 7,
}: PriceVariationsCardProps) => {
  // const displayedIngredients = ingredients.slice(0, maxDisplayed);
  // const remainingIngredients = ingredients.slice(maxDisplayed);
  // const remainingCount = remainingIngredients.length;

  return (
    <div className="border-muted-500/30 bg-muted-50 h-full rounded-lg border-[0.5px] p-2 pl-4">
      <Heading level={5} className="mb-2 font-semibold text-neutral-900">
        {name}
      </Heading>
      <div className="mb-2">
        <Paragraph size="xs" className="mb-1 font-semibold text-neutral-600">
          MEDIAN PRICE
        </Paragraph>
        <Heading level={2} className="font-bold text-neutral-900">
          {medianPrice}
        </Heading>
      </div>
      <div>
{/*        <Paragraph size="xs" className="mb-2 font-semibold text-neutral-600">
          COMMON INGREDIENTS
        </Paragraph>
        <div className="flex flex-wrap gap-2">
          {displayedIngredients.map((ingredient, index) => (
            <Tag key={index}>{ingredient}</Tag>
          ))}
          {remainingCount > 0 && (
            <Tooltip
              content=""
              className="!-mt-1 !bg-white"
              place="top"
              withArrow={true}
              contentNode={
                <div className="flex max-w-48 flex-wrap gap-1">
                  {remainingIngredients.map((ingredient, index) => (
                    <Tag key={index}>{ingredient}</Tag>
                  ))}
                </div>
              }
            >
              <Tag>+{remainingCount}</Tag>
            </Tooltip>
          )}
        </div>*/}
      </div>
    </div>
  );
};

export default PriceVariationsCard;
