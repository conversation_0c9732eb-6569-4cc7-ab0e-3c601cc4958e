import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import { IconIncrease } from "@/components/icons";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { fetchSocialConversations } from "@/api/menu_items";

const formatter = new Intl.NumberFormat('en-US', {
  notation: 'compact',
  compactDisplay: 'short',
  maximumFractionDigits: 1
});

const SocialConversations = () => {
  const defaultValues = { conversations_count: 0, rate: 0 };

  const { data: session } = useSession();
  const { guid } = useMenuItem();
  const { data: socialConversations = defaultValues } = useQuery({
    queryFn: async() => {
      const data = await fetchSocialConversations({ auth: session?.user.authorization as string, guid });
      return data;
    },
    queryKey: ["social-conversations"],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className="p-4">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <Heading level={5}>Social Conversations</Heading>
      </div>
      <div className="flex items-end justify-between">
        <div className="flex items-end gap-2">
          <Heading level={2} className="flex items-end gap-1.5">
            <span className="text-4">+</span>{formatter.format(socialConversations.conversations_count)}
          </Heading>
          <Paragraph size="xs" className="relative -top-0.5 text-neutral-600">
            conversation on social media
          </Paragraph>
        </div>
        <Tag variant="greenOutline" className="px-1">
          <IconIncrease size={16} /> {(socialConversations.rate || 0).toFixed(3)} %
        </Tag>
      </div>
    </Card>
  );
};

export default SocialConversations;
