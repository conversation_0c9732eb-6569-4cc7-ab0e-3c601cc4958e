import { useState } from "react";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import SentimentBar from "@/components/charts/sentimentBar";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { fetchSocialSentiments } from "@/api/menu_items";

const SocialSentiments = () => {
  const [selected, setSelected] = useState<string>("");

  const defaultValues = {
    "strongly-dislike": 0,
    "somewhat-dislike": 0,
    neutral: 0,
    "somewhat-like": 0,
    "strongly-like": 0,
  };

  const { data: session } = useSession();
  const { guid } = useMenuItem();
  const { data: socialSentiments = defaultValues } = useQuery({
    queryFn: async () => {
      const data = await fetchSocialSentiments({
        auth: session?.user.authorization as string,
        guid,
      });
      return data;
    },
    queryKey: ["social-sentiments"],
    enabled: !!session?.user?.authorization,
  });

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className="p-4">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center">
          <Heading level={5}>Social Sentiments</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
      <SentimentBar height={36} showLabel={true} segments={socialSentiments} />
      <div className="mt-1 flex items-center justify-between">
        <Paragraph size="xs" className="font-medium text-neutral-600">
          Negative
        </Paragraph>
        <Paragraph size="xs" className="font-medium text-neutral-600">
          in %
        </Paragraph>
        <Paragraph size="xs" className="font-medium text-neutral-600">
          Positive
        </Paragraph>
      </div>
    </Card>
  );
};

export default SocialSentiments;
