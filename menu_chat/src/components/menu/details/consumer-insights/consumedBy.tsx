import { useState } from "react";
import Card from "@/components/base/card";
import Button from "@/components/base/button";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import Bar<PERSON>hart, { BarChartItem } from "@/components/charts/barChart";
import Tabs, { TabContent } from "@/components/base/tabs";
import { fetchConsumedByGeneration } from "@/api/menu_items";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { CHART_DROPDOWN } from "@/data/ingredients";

const ConsumedBy = () => {
  const { data: session } = useSession();

  const { guid } = useMenuItem();

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const { data: consumed_by_generation } = useQuery({
    queryFn: () => {
      return fetchConsumedByGeneration({
        auth: session?.user.authorization as string,
        guid,
      });
    },
    queryKey: ["consumed_by_generation", guid],
    enabled: !!session?.user?.authorization,
  });

  const ageChartData: BarChartItem[] = Object.keys(
    consumed_by_generation?.age_percentages || {},
  ).map((key, index) => ({
    id: index + 1,
    label: key,
    color:
      consumed_by_generation.age_percentages[key] ===
      Math.max(
        ...(Object.values(
          consumed_by_generation?.age_percentages || {},
        ) as number[]),
      )
        ? "#A2D161"
        : "#D2D0BC",
    value: consumed_by_generation.age_percentages[key],
    symbol: "%",
  }));

  const ethnicityChartData: BarChartItem[] = Object.keys(
    consumed_by_generation?.ethnicity_percentages || {},
  ).map((key, index) => ({
    id: index + 1,
    label: key,
    color:
      consumed_by_generation.ethnicity_percentages[key] ===
      Math.max(
        ...(Object.values(
          consumed_by_generation?.ethnicity_percentages || {},
        ) as number[]),
      )
        ? "#A2D161"
        : "#D2D0BC",
    value: consumed_by_generation.ethnicity_percentages[key],
    symbol: "%",
  }));

  return (
    <Card className="p-4">
      <div className="relative flex flex-wrap items-center justify-between gap-2">
        <Tabs navigation={["Consumed by Generation", "Consumed by Ethnicity"]}>
          <TabContent>
            <div className="border-muted-100 rounded-lg border px-4 py-2">
              <BarChart
                dataItems={ageChartData}
                symbol="%"
                maxValue={100}
                height={160}
              />
            </div>
          </TabContent>
          <TabContent>
            <div className="border-muted-100 rounded-lg border px-4 py-2">
              <BarChart
                dataItems={ethnicityChartData}
                symbol="%"
                maxValue={100}
                height={160}
              />
            </div>
          </TabContent>
        </Tabs>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
          className="absolute top-0 right-0"
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
    </Card>
  );
};

export default ConsumedBy;
