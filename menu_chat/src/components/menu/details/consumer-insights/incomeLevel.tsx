import { useState } from "react";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import SingleBar from "@/components/charts/singleBar";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { fetchIncomeLevel } from "@/api/menu_items";

const DEFAULT_VALUES = {
  "low-income": {
    percentage: 0,
    value: 0,
    maxValue: 100,
  },
  "middle-income": {
    percentage: 0,
    value: 0,
    maxValue: 100,
  },
  "upper-middle-income": {
    percentage: 0,
    value: 0,
    maxValue: 100,
  },
  "high-income": {
    percentage: 0,
    value: 0,
    maxValue: 100,
  },
};

const IncomeLevel = () => {
  const [selected, setSelected] = useState<string>("");

  const { data: session } = useSession();
  const { guid } = useMenuItem();
  const { data: incomeLevelData = DEFAULT_VALUES } = useQuery({
    queryFn: async () => {
      const data = await fetchIncomeLevel({
        auth: session?.user.authorization as string,
        guid,
      });
      return data;
    },
    queryKey: ["income-level"],
    enabled: !!session?.user?.authorization,
  });

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const maxValue = Math.max(
    incomeLevelData["low-income"].value,
    incomeLevelData["middle-income"].value,
    incomeLevelData["upper-middle-income"].value,
    incomeLevelData["high-income"].value,
  );

  return (
    <Card className="p-4">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center">
          <Heading level={5}>
            Income Level <span className="text-neutral-600">(yearly)</span>
          </Heading>
          <Info
            tooltipId="geographic-popularity"
            content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Adipisci rem sit molestias officia mollitia"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </div>
      <div className="grid grid-cols-4 gap-4 text-center text-neutral-700">
        <div>
          <Heading level={3}>
            {incomeLevelData["low-income"].percentage}%
          </Heading>
          <SingleBar
            value={incomeLevelData["low-income"].value}
            maxValue={incomeLevelData["low-income"].maxValue}
            color={
              maxValue === incomeLevelData["low-income"].value
                ? "#A2D161"
                : undefined
            }
            pattern="diagonal"
            className="my-2"
          />
          <Paragraph size="xs" className="font-medium text-neutral-600">
            &lt; $40,000
          </Paragraph>
        </div>
        <div>
          <Heading level={3}>
            {incomeLevelData["middle-income"].percentage}%
          </Heading>
          <SingleBar
            value={incomeLevelData["middle-income"].value}
            maxValue={incomeLevelData["middle-income"].maxValue}
            color={
              maxValue === incomeLevelData["middle-income"].value
                ? "#A2D161"
                : undefined
            }
            pattern="diagonal"
            className="my-2"
          />
          <Paragraph size="xs" className="font-medium text-neutral-600">
            $40,000 - $80,000
          </Paragraph>
        </div>
        <div>
          <Heading level={3}>
            {incomeLevelData["upper-middle-income"].percentage}%
          </Heading>
          <SingleBar
            value={incomeLevelData["upper-middle-income"].value}
            maxValue={incomeLevelData["upper-middle-income"].maxValue}
            color={
              maxValue === incomeLevelData["upper-middle-income"].value
                ? "#A2D161"
                : undefined
            }
            pattern="diagonal"
            className="my-2"
          />
          <Paragraph size="xs" className="font-medium text-neutral-600">
            $80,000 - $100,000
          </Paragraph>
        </div>
        <div>
          <Heading level={3}>
            {incomeLevelData["high-income"].percentage}%
          </Heading>
          <SingleBar
            value={incomeLevelData["high-income"].value}
            maxValue={incomeLevelData["high-income"].maxValue}
            color={
              maxValue === incomeLevelData["high-income"].value
                ? "#A2D161"
                : undefined
            }
            pattern="diagonal"
            className="my-2"
          />
          <Paragraph size="xs" className="font-medium text-neutral-600">
            &gt; $100,000
          </Paragraph>
        </div>
      </div>
    </Card>
  );
};

export default IncomeLevel;
