import SocialConversations from "./socialConversation";
import SocialSentiments from "./socialSentiments";
import ConsumerExperience from "./consumerExperience";
import GeographicPopularity from "./geographicPopularity";
import ConsumedBy from "./consumedBy";
import IncomeLevel from "./incomeLevel";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const MenuItemsDetailsConsumerInsights = () => {
  return (
    <section className="mt-[60px] py-4">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-2 gap-5">
          <div className="flex flex-col gap-5">
            <SocialConversations />
            <SocialSentiments />
            <ConsumerExperience />
          </div>
          <div className="flex flex-col gap-5">
            <GeographicPopularity />
            <ConsumedBy />
            <IncomeLevel />
          </div>
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default MenuItemsDetailsConsumerInsights;
