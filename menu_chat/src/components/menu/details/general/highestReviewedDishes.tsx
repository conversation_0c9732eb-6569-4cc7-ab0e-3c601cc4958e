import { useState } from "react";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";

const HighestReviewedDishes = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Highest Reviewed Dishes</Heading>
          <Info
            tooltipId="pairing-trends-tooltip"
            content="Most popular (used) pairing ingredients trends in last quarter."
          />
        </div>
        <Dropdown
          options={MENU_ADOPTION_CURVE_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Dish</Th>
            <Th>Restaurant Name</Th>
            <Th>Rating</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag>Espadon* (Boucho...</Tag>
            </Td>
            <Td>Savor & Sage</Td>
            <Td>4.7</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Ratatouille a la nic...</Tag>
            </Td>
            <Td>The Roaming Spoon</Td>
            <Td>3.7</Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Ratatouille omelet...</Tag>
            </Td>
            <Td>Lush Biten</Td>
            <Td>4.6</Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default HighestReviewedDishes;
