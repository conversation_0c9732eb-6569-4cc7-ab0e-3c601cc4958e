import { useState } from "react";
import Card, { CardHeader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { useSession } from "next-auth/react";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { useQuery } from "@tanstack/react-query";
import { menuItemsInnovation } from "@/api/menu_items";
import titleCase from "voca/title_case";
import Link from "next/link";

const HighestReviewedDishes = () => {
  const { data: session } = useSession();

  const { guid } = useMenuItem();

  const [pagination] = useState({ pageIndex: 1, pageSize: 3 });

  const { data: highest_reviewed_dishes } = useQuery({
    queryFn: () => {
      return menuItemsInnovation({
        auth: session?.user.authorization as string,
        guid: guid,
        pagination,
      });
    },
    queryKey: ["highest_reviewed_dishes", guid],
    enabled: !!session?.user?.authorization,
  });

  const dataMap =
    highest_reviewed_dishes?.data.map((data: MenuItem) => {
      const { id, business_name, name, price } = data;
      return (
        <Tr key={id}>
          <Td>
            <Tag>{titleCase(name)}</Tag>
          </Td>
          <Td>{titleCase(business_name)}</Td>
          <Td>{price > 0 && `$${price}`}</Td>
        </Tr>
      );
    }) || [];

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Innovative Menu Items</Heading>
          <Info
            tooltipId="pairing-trends-tooltip"
            content="Most popular (used) pairing ingredients trends in last quarter."
          />
        </div>
        <div className="flex items-center">
          <Link href={`/menu-items/details/${guid}/on-menus`}>
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <Table className="p-4 pb-0">
        <Thead>
          <Tr>
            <Th>Dish</Th>
            <Th>Restaurant Name</Th>
            <Th>Price</Th>
          </Tr>
        </Thead>
        <Tbody>{dataMap}</Tbody>
      </Table>
    </Card>
  );
};

export default HighestReviewedDishes;
