import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Card from "@/components/base/card";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import Tabs, { TabContent } from "@/components/base/tabs";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import CuisineDistribution from "./distributions/cuisine";
import MealTypeDistribution from "./distributions/mealType";

interface MenuItemsDistributionProps {
  className?: string;
}

const MenuItemsDistribution = ({ className }: MenuItemsDistributionProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge("relative", className)}>
      <Dropdown
        options={MENU_ADOPTION_CURVE_DROPDOWN}
        selectedValue={selected}
        onSelect={(value) => handleSelect(value)}
        className="absolute top-6 right-4 z-10"
      >
        <Button variant="ghost" size="xs" className="w-6">
          <IconMore size={16} />
        </Button>
      </Dropdown>
      <div className="p-4">
        <Tabs navigation={["Cuisine Distribution", "Meal Type Distribution"]}>
          <TabContent>
            <CuisineDistribution />
          </TabContent>
          <TabContent>
            <MealTypeDistribution />
          </TabContent>
        </Tabs>
      </div>
    </Card>
  );
};

export default MenuItemsDistribution;
