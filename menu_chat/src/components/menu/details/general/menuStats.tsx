import { useState, useEffect } from "react";
import Card, { <PERSON>Header } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Info from "@/components/base/info";
import Tag from "@/components/base/tag";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { IconCircleUpright, IconIncrease, IconMore } from "@/components/icons";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { useQuery } from "@tanstack/react-query";
import { fetchPenRateOverTime } from "@/api/menu_items";
import { useSession } from "next-auth/react";
import titleCase from "voca/title_case";

const MenuStats = () => {
  const { data: session } = useSession();

  const {
    name,
    guid,
    pen_rate,
    google_trend,
    foodservice_growth,
    foodservice_prediction,
  } = useMenuItem();
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const [lineChartData, setLineChartData] = useState<LineSeriesItem[]>([]);
  const [lineChartLabels, setLineChartLabels] = useState([]);

  const { data: pen_rate_over_time } = useQuery({
    queryFn: () => {
      return fetchPenRateOverTime({
        auth: session?.user.authorization as string,
        guid: guid,
      });
    },
    queryKey: ["pen_rate_over_time", guid],
    enabled: !!session?.user?.authorization,
  });

  useEffect(() => {
    const pen_rate_over_time_map =
      pen_rate_over_time?.map((q: Quarter) => q.pen_rate) || [];
    const labels =
      pen_rate_over_time?.map((q: Quarter) => `Q${q.quarter} ${q.year}`) || [];
    setLineChartData([
      {
        id: 1,
        label: titleCase(name),
        color: "#FF6D56",
        data: pen_rate_over_time_map,
      },
    ]);
    setLineChartLabels(labels);
  }, [pen_rate_over_time, setLineChartData, setLineChartLabels, name, guid]);

  return (
    <>
      <div className="grid grid-cols-2 gap-5">
        <Card className="p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <Heading level={5}>Menu Penetration</Heading>
          </div>
          <div className="justify-betweens mb-1 flex items-end">
            <Heading level={2} className="flex items-end gap-1.5">
              <span className="text-4">+</span>
              {pen_rate}%
            </Heading>
            <Tag variant="greenOutline" className="px-1">
              <IconIncrease size={16} /> 3.56 %
            </Tag>
          </div>
          <Paragraph size="xs" className="font-medium text-neutral-600">
            total of 10,256 menu mentions
          </Paragraph>
        </Card>
        <Card className="p-4">
          <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
            <Heading level={5}>Google Trends</Heading>
            <IconCircleUpright size={24} className="text-yellow-800" />
          </div>
          <Heading level={2} className="flex items-end gap-1.5">
            <span className="text-4">+</span>
            {google_trend}
          </Heading>
          <Paragraph size="xs" className="font-medium text-neutral-600">
            search of term popularity
          </Paragraph>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <Heading level={4}>Menu Penetration Change Over Time</Heading>
            <Info
              tooltipId="foodservice-growth-tooltip"
              content="Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Adipisci rem sit molestias officia mollitia"
            />
          </div>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </CardHeader>
        <div className="p-4">
          <div className="mb-5 grid grid-cols-2 gap-5">
            <Card className="p-4">
              <div className="mb-2">
                <Heading level={5}>Compared</Heading>
                <Paragraph size="xs" className="font-medium text-neutral-600">
                  to Previous Period
                </Paragraph>
              </div>
              <div className="flex items-end justify-between">
                <Heading level={2} className="flex items-end gap-1.5">
                  {foodservice_growth > 0 && <span className="text-4">+</span>}
                  {foodservice_growth}%
                </Heading>
                <Tag variant="greenOutline" className="px-1">
                  <IconIncrease size={16} />
                </Tag>
              </div>
            </Card>
            <Card className="p-4">
              <div className="mb-2">
                <Heading level={5}>Prediction</Heading>
                <Paragraph size="xs" className="font-medium text-neutral-600">
                  For Upcoming Period
                </Paragraph>
              </div>
              <div className="flex items-end justify-between">
                <Heading level={2} className="flex items-end gap-1.5">
                  {foodservice_prediction > 0 && (
                    <span className="text-4">+</span>
                  )}
                  {foodservice_prediction?.toFixed(2)}%
                </Heading>
                <Tag variant="greenOutline" className="px-1">
                  <IconIncrease size={16} />
                </Tag>
              </div>
            </Card>
          </div>
          <div className="border-muted-100 rounded-lg border p-3">
            <LineChart
              dataItems={lineChartData}
              labels={lineChartLabels}
              showLegend={false}
              symbol=""
              height={255}
              yTickCallback={(val) => {
                if (typeof val === "number") {
                  val = val.toFixed(2);
                }
                return `${val}%`;
              }}
            />
          </div>
        </div>
      </Card>
    </>
  );
};

export default MenuStats;
