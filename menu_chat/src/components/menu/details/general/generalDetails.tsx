import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import ImageSlider from "@/components/base/imageSlider";
import {
  IconCircleFilled,
  IconAroma,
  IconMenuAdoption,
  IconChefHat,
  IconGlutenfree,
} from "@/components/icons";
import { useMenuItem } from "@/contexts/MenuItemContext";
import titleCase from "voca/title_case";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMenuItemItems } from "@/api/menu_items";
import { useState } from "react";
import CollapseCard from "@/components/base/collapseCard";

const GeneralDetails = () => {
  const { data: session } = useSession();

  const {
    guid,
    name,
    description,
    aroma,
    texture,
    category,
    subcategory,
    dietary_tags,
    cuisine_type,
  } = useMenuItem();
  const [page] = useState<number>(1);
  const [pageSize] = useState<number>(5);
  const { data: menu_item_items } = useQuery({
    queryFn: () => {
      return fetchMenuItemItems({
        auth: session?.user.authorization as string,
        guid: guid,
        page,
        pageSize,
      });
    },
    queryKey: ["menu_item_items", guid, page, pageSize],
    enabled: !!session?.user?.authorization,
  });

  const imagesMap =
    menu_item_items?.data?.map((m: MenuItem) => {
      if (m.image_url.match(/\[/)) {
        try {
          return JSON.parse(m.image_url)[0];
        } catch (error) {
          console.log(error);
        }
        return "";
      }
      if (m.image_url === null) {
        return "";
      }
      return m.image_url;
    }) || [];

  const dietaryTagsMap =
    dietary_tags.map((dietary_tag) => {
      return (
        <Tag key={dietary_tag} variant="white">
          <IconGlutenfree size={16} className="text-muted-500" />
          {titleCase(dietary_tag)}
        </Tag>
      );
    }) || [];

  const textureMap =
    texture.map((texture) => {
      return (
        <Tag key={texture} variant="white">
          <IconCircleFilled size={16} className="text-muted-500" />
          {titleCase(texture)}
        </Tag>
      );
    }) || [];

  const aromaMap =
    aroma.map((aroma) => {
      return (
        <Tag key={aroma} variant="white">
          <IconAroma size={16} className="text-muted-500" />
          {titleCase(aroma)}
        </Tag>
      );
    }) || [];

  return (
    <Card className="p-4">
      <div className="flex items-start justify-between gap-2">
        <div className="flex w-1/2 gap-2">
          <div className="w-1/2">
            <Heading level={3} className="mb-3">
              {titleCase(name)}
            </Heading>
            <div className="flex flex-wrap gap-1">
              {category && (
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  {titleCase(category)}
                </Tag>
              )}
              {subcategory && (
                <Tag variant="white">
                  <IconMenuAdoption size={16} className="text-neutral-500" />
                  {titleCase(subcategory)}
                </Tag>
              )}
              {cuisine_type.length > 0 && (
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  {cuisine_type.map((c) => titleCase(c)).join(",")}
                </Tag>
              )}
            </div>
          </div>
          <div className="flex w-1/2 flex-col gap-2">
            <div className="flex flex-col gap-2 rounded-lg border border-neutral-100 p-3">
              <div className="flex items-center gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Healthy
                </Paragraph>
                <div className="flex flex-wrap items-start gap-2">
                  {dietaryTagsMap}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Texture
                </Paragraph>
                <div className="flex flex-wrap items-start gap-2">
                  {textureMap}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Aroma
                </Paragraph>
                <div className="flex flex-wrap items-start gap-2">
                  {aromaMap}
                </div>
              </div>
            </div>
            <CollapseCard text={description} />
          </div>
        </div>
        <ImageSlider
          className="w-1/2"
          thumbnailPosition="right"
          images={imagesMap}
        />
      </div>
    </Card>
  );
};

export default GeneralDetails;
