import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import ImageSlider from "@/components/base/imageSlider";
import {
  IconCircleUpright,
  IconCircleFilled,
  IconAroma,
  IconMenuAdoption,
  IconChefHat,
  IconGlutenfree,
} from "@/components/icons";

import { useMenuItem } from "@/contexts/MenuItemContext";
import titleCase from "voca/title_case";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMenuItemItems } from "@/api/menu_items";
import { useState } from "react";


const GeneralDetails = () => {
  const { data: session } = useSession();

  const {guid, name, description, aroma, texture, category} = useMenuItem()
  const [page] = useState<number>(1);
  const [pageSize] = useState<number>(5);
  const { data: menu_item_items } = useQuery({
    queryFn: () => {
      return fetchMenuItemItems({
        auth: session?.user.authorization as string,
        guid: guid,
        page,
        pageSize,
      });
    },
    queryKey: ["menu_item_items", guid, page, pageSize],
    enabled: !!session?.user?.authorization,
  });


  const imagesMap = menu_item_items?.data?.map((m: MenuItem) => {
    if (m.image_url.match(/\[/)){
      try {
        return JSON.parse(m.image_url)[0]
      }
      catch(error){
        console.log(error)
      }
      return ""
    }
    return m.image_url
  }) || []

  return (
    <Card className="p-4">
      <div className="flex items-start justify-between gap-2">
        <div className="flex w-1/2 gap-2">
          <div className="w-1/2">
            <Heading level={3} className="mb-3">
              {titleCase(name)}
            </Heading>
            <div className="flex flex-wrap gap-1">
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                { titleCase(category) }
              </Tag>
              <Tag variant="white">
                <IconChefHat size={16} className="text-neutral-500" />
                {/*  todo: cuisine */}
              </Tag>
            </div>
          </div>
          <div className="flex w-1/2 flex-col gap-2">
            <div className="flex flex-col gap-2 rounded-lg border border-neutral-100 p-3">
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Healthy
                </Paragraph>
                <Tag variant="white">
                  <IconGlutenfree size={16} className="text-muted-500" />
                  {/*  todo: Healthy Tag */}
                </Tag>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Texture
                </Paragraph>
                <Tag variant="white">
                  <IconCircleFilled size={16} className="text-muted-500" />
                  {titleCase(texture)}
                </Tag>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Aroma
                </Paragraph>
                <div className="flex items-center gap-2">
                  <Tag variant="white">
                    <IconAroma size={16} className="text-muted-500" />
                    {titleCase(aroma)}
                  </Tag>
                </div>
              </div>
            </div>
            <div className="bg-muted-100 rounded-lg p-3">
              <Paragraph
                size="sm"
                className="relative pr-5 font-medium text-neutral-800"
              >
                {description}
                <IconCircleUpright
                  size={16}
                  className="absolute top-0 right-0 text-yellow-800"
                />
              </Paragraph>
            </div>
          </div>
        </div>
        <ImageSlider
          className="w-1/2"
          thumbnailPosition="right"
          images={imagesMap}
        />
      </div>
    </Card>
  );
};

export default GeneralDetails;
