import DonutChart from "@/components/charts/donutChart";

const chartData = [
  {
    label: "Italian",
    value: 45,
    color: "#8BC539",
  },
  { label: "Mediterranean", value: 20, color: "#FFBE05" },
  { label: "French", value: 35, color: "#FF6D56" },
];

const CuisineDistribution = () => {
  return (
    <div className="border-muted-100 flex items-center justify-between gap-5 rounded-lg border px-3 py-6">
      <DonutChart data={chartData} size={235} showLabel={false} />
      <div className="flex w-full flex-1 flex-col gap-1 text-xs font-medium">
        {chartData.map((item, index) => (
          <div
            key={index}
            className="border-muted-500/30 flex items-center justify-between border-b px-1 py-1.5"
          >
            <span className="flex items-center gap-1">
              <span
                className="h-3 w-1 rounded-full"
                style={{ backgroundColor: item.color }}
              ></span>
              {item.label}
            </span>
            <span className="text-neutral-600">{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CuisineDistribution;
