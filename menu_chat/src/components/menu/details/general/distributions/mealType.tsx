import DonutChart from "@/components/charts/donutChart";
import { useSession } from "next-auth/react";
import { useMenuItem } from "@/contexts/MenuItemContext";
import ColorGenerator from "@/components/utils/color-generator";
import { useQuery } from "@tanstack/react-query";
import { fetchDayPartDistro } from "@/api/menu_items";
import { useEffect, useState } from "react";
import titleCase from "voca/title_case";

// const chartData = [
//   {
//     label: "Italian",
//     value: 15,
//     color: "#8BC539",
//   },
//   { label: "Mediterranean", value: 50, color: "#FFBE05" },
//   { label: "French", value: 35, color: "#FF6D56" },
// ];

interface PieChartData {
  label: string;
  value: number;
  color: string
}

interface CuisineDistroResponse {
  name: string;
  items_count: number;
  total_items_count: number;
  percentage: number;
}

const MealTypeDistribution = () => {
  const { data: session } = useSession();

  const {guid} = useMenuItem();

  const { data: meal_types }  = useQuery({
    queryFn: () => {
      return fetchDayPartDistro({auth: session?.user.authorization as string, guid: guid, limit: 5})
    },
    queryKey: ["meal_types", guid],
    enabled: !!(session?.user?.authorization),
  });


  const [chartData, setChartData] = useState([]);


  useEffect(()=>{
    const colorGen = new ColorGenerator(`meal-types-${guid}`);

    const newChartData = meal_types?.map((data: CuisineDistroResponse)=>{
      const {name, percentage} = data;
      return {
        label: titleCase(name),
        value: percentage,
        color: colorGen.getColor(name),
      }
    }) || []


    setChartData(newChartData)

  },[setChartData, meal_types, guid])

  return (
    <div className="border-muted-100 flex items-center justify-between gap-5 rounded-lg border px-3 py-6">
      <DonutChart cutout={0} data={chartData} size={235} showLabel={false} />
      <div className="flex w-full flex-1 flex-col gap-1 text-xs font-medium">
        {chartData.map((item: PieChartData, index) => (
          <div
            key={index}
            className="border-muted-500/30 flex items-center justify-between border-b px-1 py-1.5"
          >
            <span className="flex items-center gap-1">
              <span
                className="h-3 w-1 rounded-full"
                style={{ backgroundColor: item.color }}
              ></span>
              {item.label}
            </span>
            <span className="text-neutral-600">{item.value}%</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MealTypeDistribution;
