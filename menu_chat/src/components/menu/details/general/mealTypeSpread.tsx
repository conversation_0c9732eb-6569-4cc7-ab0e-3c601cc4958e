import { useEffect, useState } from "react";
import Card, { <PERSON>Header } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import { IconMore } from "@/components/icons";
import Dropdown from "@/components/base/dropdown";
import { CHART_DROPDOWN } from "@/data/ingredients";
import DonutChart from "@/components/charts/donutChart";
import { useSession } from "next-auth/react";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { fetchDayPartDistro } from "@/api/menu_items";
import { useQuery } from "@tanstack/react-query";

enum DayParts {
  breakfastbrunch = 'Breakfast / Brunch',
  lunch = 'Lunch',
  dinner = 'Dinner',
  allday = 'All Day',
}

type DayPartsKeys = keyof typeof DayParts;

const DayPartHash = {
  breakfastbrunch: {
    color: "#FFBE05"
  },
  lunch: {
    color: "#FF6D56"
  },
  dinner: {
    color: "#8BC539"
  },
  allday: {
    color: "#2A9DCB"
  }
}


// const chartData = [
//   { label: "Brunch", value: 28, color: "#FF6D56" },
//   { label: "Breakfast", value: 28, color: "#FFBE05" },
//   { label: "Lunch", value: 45, color: "#8BC539" },
// ];


interface DayPartsResponse {
  id: number,
  menu_item_id: number,
  day_part: string,
  percent: number,
  quarter: number,
  year: number,
  created_at: string,
  updated_at: string
}

interface PieChartData {
  label: string;
  value: number;
  color: string;
}

const MealTypeSpread = () => {
  const { data: session } = useSession();

  const {  guid } = useMenuItem();

  const { data: meal_types }  = useQuery({
    queryFn: () => {
      return fetchDayPartDistro({auth: session?.user.authorization as string, guid: guid})
    },
    queryKey: ["meal_types", guid],
    enabled: !!(session?.user?.authorization),
  });

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const [chartData, setChartData] = useState([]);


  useEffect(()=>{
    const newChartData = meal_types?.map((item: DayPartsResponse)=>{
      const {day_part, percent} = item;
      return {
        label: DayParts[day_part as DayPartsKeys],
        value: Math.round(percent * 100),
        color: DayPartHash[day_part as DayPartsKeys]?.color || "#FFBE05",
      }
    }) || []

    setChartData(newChartData)

  },[setChartData, meal_types, guid])


  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Day Part</Heading>
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="p-4">
        <div className="border-muted-100 flex min-h-[230px] items-center justify-between gap-5 rounded-lg border px-3 py-6">
          <DonutChart data={chartData} size={240} showLabel={false} />
          <div className="flex w-full flex-1 flex-col gap-1 text-xs font-medium">
            {chartData.map((item: PieChartData, index) => (
              <div
                key={index}
                className="border-muted-500/30 flex items-center justify-between border-b px-1 py-1.5"
              >
                <span className="flex items-center gap-1">
                  <span
                    className="h-3 w-1 rounded-full"
                    style={{ backgroundColor: item.color }}
                  ></span>
                  {item.label}
                </span>
                <span className="text-neutral-600">{item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MealTypeSpread;