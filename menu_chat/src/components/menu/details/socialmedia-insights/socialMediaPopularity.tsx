import Card, { <PERSON><PERSON>eader } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import {
  IconFacebook,
  IconInstagram,
  IconTiktok,
  IconLinkedin,
  IconYoutube,
  IconGraph,
  IconIncrease,
  IconDecrease,
} from "@/components/icons";
import { fetchSocialMediaPopularity } from "@/api/menu_items";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

const SocialMediaPopularity = () => {
  const { data: session } = useSession();
  const { guid } = useMenuItem();

  const { data } = useQuery({
    queryFn: () => {
      return fetchSocialMediaPopularity({
        auth: session?.user.authorization as string,
        guid
      });
    },
    queryKey: ["menu_item_social_media_popularity", guid],
    enabled: !!session?.user?.authorization,
  });

  type SocialMediaName = "Instagram" | "TikTok" | "Facebook" | "LinkedIn" | "YouTube";

  const SOCIAL_COMPONENTS: Record<SocialMediaName, React.FC<{ size: number; className?: string }>> = {
    Instagram: IconInstagram,
    TikTok: IconTiktok,
    Facebook: IconFacebook,
    LinkedIn: IconLinkedin,
    YouTube: IconYoutube,
  };

  type SocialMediaItem = {
    name: SocialMediaName;
    avg_engagement: number;
    post_number: number;
    growth: number;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Social Media Popularity</Heading>
          <Info
            tooltipId="social-media-popularity-tooltip"
            content="Ingredient popularity on social media by average engagement"
          />
        </div>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Avg. Engagement</Th>
            <Th>Post Number</Th>
            <Th>Growth</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data?.map((item: SocialMediaItem) => {
            const IconComponent = SOCIAL_COMPONENTS[item.name] || IconGraph;
            return (
              <Tr key={item.name}>
                <Td>
                  <Tag variant="ghost">
                    <IconComponent size={16} className="text-neutral-500" />
                    <span>{item.name}</span>
                  </Tag>
                </Td>
                <Td>{item.avg_engagement.toLocaleString()}</Td>
                <Td>{item.post_number.toLocaleString()}</Td>
                <Td>
                  <Tag
                    variant={
                      item.growth > 0
                        ? "greenOutline"
                        : item.growth < 0
                        ? "redOutline"
                        : "blueOutline"
                    }
                  >
                    {item.growth > 0 ? (
                      <IconIncrease size={16} />
                    ) : item.growth < 0 ? (
                      <IconDecrease size={16} />
                    ) : (
                      <IconGraph size={16} />
                    )}
                    {Math.abs(item.growth)}%
                  </Tag>
                </Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>
    </Card>
  );
};

export default SocialMediaPopularity;
