import { useState } from "react";
import titleCase from "voca/title_case";
import Card, { <PERSON>Header } from "@/components/base/card";
import Heading from "@/components/base/heading";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import TagCloudChart from "@/components/charts/tagCloudChart";
import { fetchSocialMediaWords } from "@/api/menu_items";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

const COLORS = [
  "#DF9DE4",
  "#FF9985",
  "#BDA4CB",
  "#FFC933",
  "#A2D161",
  "#FAAB61",
  "#78C5E3",
  "#FF6D56",
  "#6BCB77",
  "#4D96FF",
  "#FFB6C1",
  "#FF8C94",
  "#FFB74D",
  "#FF6F61",
  "#6A5ACD",
  "#FF6347"
];

const WordCloud = () => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const { data: session } = useSession();
  const { guid } = useMenuItem();

  const { data } = useQuery({
    queryFn: () => {
      return fetchSocialMediaWords({
        auth: session?.user.authorization as string,
        guid
      });
    },
    queryKey: ["menu_item_social_media_words", guid],
    enabled: !!session?.user?.authorization,
  });

  const wordCloudData = data?.map((item: { text: string; count: number }, index: number) => ({
    value: titleCase(item.text),
    count: item.count,
    color: COLORS[index % COLORS.length],
  })) || [];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Word Cloud</Heading>
          <Info
            tooltipId="word-cloud-tooltip"
            content="Most frequently used words associated with this ingredient"
          />
        </div>
        <Dropdown
          options={CHART_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="border-muted-100 m-3 min-h-[500px] rounded-lg border p-3">
        <div className="mx-auto flex min-h-[500px] max-w-[400px] flex-wrap items-center justify-center text-center">
          <TagCloudChart data={wordCloudData} minSize={12} maxSize={28} />
        </div>
      </div>
    </Card>
  );
};

export default WordCloud;
