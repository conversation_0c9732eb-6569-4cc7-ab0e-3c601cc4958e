import React from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Image from "next/image";
import Paragraph from "@/components/base/paragraph";
import { IconInstagram, IconFacebook, IconTiktok } from "@/components/icons";
import { fetchSocialMediaPosts } from "@/api/menu_items";
import { useMenuItem } from "@/contexts/MenuItemContext";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

interface MostEngagedSocialMediaPostsProps {
  className?: string;
}

interface IconProps {
  className?: string;
  size: number;
}

const ICON_MAP = {
  'instagram.com': ({ className, size }: IconProps) => <IconInstagram className={className} size={size} />,
  'tiktok.com': ({ className, size }: IconProps) => <IconTiktok className={className} size={size} />,
  'facebook.com': ({ className, size }: IconProps) => <IconFacebook className={className} size={size} />,
} as const;

type KnownSite = keyof typeof ICON_MAP;

const isKnownSite = (site: string): site is KnownSite => {
  return site in ICON_MAP;
};

interface SocialMediaPost {
  site: string;
  username: string;
  total_engagements: number;
  url: string;
}

const MostEngagedSocialMediaPosts = ({
  className,
}: MostEngagedSocialMediaPostsProps) => {
  const { data: session } = useSession();
  const { guid } = useMenuItem();

  const { data } = useQuery<SocialMediaPost[]>({
    queryFn: () =>
      fetchSocialMediaPosts({
        auth: session?.user.authorization as string,
        guid,
      }),
    queryKey: ["menu_item_social_media_posts", guid],
    enabled: !!session?.user?.authorization,
  });

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Engaged Social Media Posts</Heading>
          <Info
            tooltipId="most-engaged-social-media-posts"
            content="Social Media Posts with highest number of engagements/views"
          />
        </div>
      </CardHeader>

      <div className="grid grid-cols-4 gap-2 p-4">
        {data?.slice(0, 4).map((post, index) => (
          <div
            className="border-muted-100 flex flex-col gap-2 rounded-lg border p-2  cursor-pointer"
            key={`post-${index}`}
            onClick={(e) => {
              e.stopPropagation();
              window.open(post.url, "_blank");
            }}
          >
            <Image
              src={`${post.url}media`}
              onError={(e) => {
                e.preventDefault();
                (e.target as HTMLImageElement).remove();
              }}
              alt="chocolates"
              width={100}
              height={140}
              className="h-[140px] w-full rounded-lg object-cover post-image"
            />
            <Image
              src="/assets/images/<EMAIL>"
              alt="chocolates"
              width={100}
              height={140}
              className="h-[140px] w-full rounded-lg object-cover"
            />
            <div className="px-2 py-1">
              <Paragraph
                size="sm"
                className="flex items-center gap-1 font-semibold"
              >
                {isKnownSite(post.site) ? (
                  React.createElement(ICON_MAP[post.site], {
                    size: 20,
                    className: "min-w-5 text-neutral-500",
                  })
                ) : (
                  <IconInstagram
                    size={20}
                    className="min-w-5 text-neutral-500"
                  />
                )}
                <span className="inline-block max-w-48 truncate">
                  {post.username}
                </span>
              </Paragraph>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Media
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  {post.site
                    ? post.site.split(".")[0].charAt(0).toUpperCase() +
                      post.site.split(".")[0].slice(1)
                    : "Unknown"}
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Engagement & Views
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  {post.total_engagements
                    ? post.total_engagements.toLocaleString()
                    : "N/A"}
                </Paragraph>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default MostEngagedSocialMediaPosts;
