import SocialMediaPopularity from "./socialMediaPopularity";
import SocialConversations from "./socialConversations";
import SocialSentiments from "./socialSentiments";
import WordCloud from "./wordCloud";
import MostEngagedSocialMediaPosts from "./mostEngagedSocialMediaPosts";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const MenuItemsDetailsSocialMediaInsights = () => {
  return (
    <section className="mt-[60px] py-4">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-2 items-start gap-5">
          <div className="flex flex-col gap-5">
            <SocialMediaPopularity />
            <SocialConversations />
          </div>
          <div className="flex flex-col gap-5">
            <SocialSentiments />
            <WordCloud />
          </div>
        </div>
        <MostEngagedSocialMediaPosts />
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default MenuItemsDetailsSocialMediaInsights;
