import FastestGrowingDish from "./fastest-growing-dish/fastestGrowingDish";
import MenuItemsInnovation from "./menu-items-innovation/menuItemsInnovation";
import MostPopularLimitedTime from "./most-popular-limited-time/mostPopularLimitedTime";
import FastestGrowingMenuItems from "./fastest-growing-menu-items/fastestGrowingMenuItems";
import HighestPenetrationCompared from "./highest-penetration-compared/highestPenetrationCompared";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const IndustryInsights = () => {
  return (
    <section className="py-4">
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-7">
            <FastestGrowingDish />
          </div>
          <div className="col-span-5 flex flex-col gap-5">
            <MenuItemsInnovation />
            <MostPopularLimitedTime />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-5">
          <FastestGrowingMenuItems />
          <HighestPenetrationCompared />
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default IndustryInsights;
