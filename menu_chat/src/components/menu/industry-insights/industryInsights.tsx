import FastestGrowingDish from "./fastest-growing-dish/fastestGrowingDish";
//import MenuItemsInnovation from "./menu-items-innovation/menuItemsInnovation";
import MostPopularLimitedTime from "./most-popular-limited-time/mostPopularLimitedTime";
//import FastestGrowingMenuItems from "./fastest-growing-menu-items/fastestGrowingMenuItems";
import HighestPenetrationCompared from "./highest-penetration-compared/highestPenetrationCompared";
import DataSourcesReports from "@/components/shared/dataSourcesReports";
import MenuAdoptionCurve from "./menu-adoption-curve/menuAdoptionCurve";

const IndustryInsights = () => {
  return (
    <section className="py-4">
      <div className="flex flex-col gap-5">
        <MenuAdoptionCurve />
        <div className="grid grid-cols-2 items-start gap-5">
          {/* <FastestGrowingMenuItems /> */}
          <div className="col-span-2">
            <MostPopularLimitedTime />
          </div>
          <FastestGrowingDish />
          <HighestPenetrationCompared />
          {/* <MenuItemsInnovation /> */}

        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default IndustryInsights;
