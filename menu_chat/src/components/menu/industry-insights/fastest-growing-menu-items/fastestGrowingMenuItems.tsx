import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Tag from "@/components/base/tag";

interface FastestGrowingMenuItemsProps {
  className?: string;
}


import { ColumnDef, getCoreRowModel, SortingState, useReactTable } from "@tanstack/react-table";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import { useMemo, useState } from "react";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fastestGrowingMenuItems } from "@/api/menu_items";
import titleCase from "voca/title_case";
import { getVariantForRate } from "@/components/utils/helpers";

const FastestGrowingMenuItems = ({
  className,
}: FastestGrowingMenuItemsProps) => {

  const { data: session } = useSession();
  const [sorting] = useState<SortingState>([{id: 'foodservice_growth', desc: true}]);

  const { data: fastest_growing_menu_items, isLoading }  = useQuery({
    queryFn: () => fastestGrowingMenuItems({auth: session?.user.authorization as string, sorting}),
    queryKey: ["fastest_growing_menu_items", sorting],
    enabled: !!session?.user?.authorization,
  });



  const columns = useMemo<ColumnDef<FastestGrowingIngredient>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: info => {
        const {guid} = info.row.original
        return (
          <>
            <Link key={guid} href={`/menu-items/details/${guid}`}>
              <Tag>{titleCase(info.getValue<string>())}</Tag>
            </Link>
          </>
        )
      },
    },
    {
      accessorKey: 'pen_rate',
      header: 'Penetration',
      cell: info => {
        return (`${info.getValue<number>()}%`)
      },
    },
    {
      accessorKey: 'foodservice_growth',
      header: 'Change',
      cell: info => {
        const change = info.getValue<number>();
        const {type, icon} = getVariantForRate(change)
        return (
          <Tag variant={type}>
            {icon}
            {change?.toFixed(2)}%
          </Tag>)
      },
    },
    {
      accessorKey: 'foodservice_prediction',
      header: 'Prediction 1Y',
      cell: info => {
        const change = info.getValue<number>();
        const {type, icon} = getVariantForRate(change)
        return (
          <Tag variant={type}>
            {icon}
            {change?.toFixed(2)}%
          </Tag>)
      },
    }
  ], []);


  const data = useMemo(() => {
    if (!fastest_growing_menu_items?.data) return [];
    return fastest_growing_menu_items.data?.slice(0, 6) ?? [];
  }, [fastest_growing_menu_items]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Disable features we don't need in this view
    enableSorting: false,
    enableFilters: false,
    enableColumnResizing: false,
  });

  const emptyState = (
    <div className="text-center py-4">
      <p className="text-gray-500">No LTOs found.</p>
    </div>
  );

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Fastest Growing Menu Items</Heading>
          <Info
            tooltipId="fastest-growing-menu-items-tooltip"
            content="Menu items with highest penetration and menu mentions in the chosen period."
          />
        </div>
        <Link href="/menu-items/fastest-growing-menu-items">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="px-4 pt-4 pb-2">
        <EnhancedTable
          table={table}
          isLoading={isLoading}
          emptyState={emptyState}
        />
      </div>
    </Card>
  );
};

/*
<Table className="px-4 pt-4 pb-2">
  <Thead>
    <Tr>
      <Th>Name</Th>
      <Th>Penetration</Th>
      <Th>Change</Th>
      <Th>Prediction 1y</Th>
    </Tr>
  </Thead>
  <Tbody>
    <Tr>
      <Td>
        <Tag>Steak</Tag>
      </Td>
      <Td>0.24%</Td>
      <Td>
        <Tag variant="blueOutline">
          <IconGraph size={16} />
          0,00%
        </Tag>
      </Td>
      <Td>
        <Tag variant="greenOutline">
          <IconIncrease size={16} />
          3,25%
        </Tag>
      </Td>
    </Tr>
    <Tr>
      <Td>
        <Tag>Pizza</Tag>
      </Td>
      <Td>0.52%</Td>
      <Td>
        <Tag variant="redOutline">
          <IconDecrease size={16} />
          5,70%
        </Tag>
      </Td>
      <Td>
        <Tag variant="blueOutline">
          <IconGraph size={16} />
          0,00%
        </Tag>
      </Td>
    </Tr>
    <Tr>
      <Td>
        <Tag>Guacamole</Tag>
      </Td>
      <Td>0.44%</Td>
      <Td>
        <Tag variant="greenOutline">
          <IconIncrease size={16} />
          3,25%
        </Tag>
      </Td>
      <Td>
        <Tag variant="redOutline">
          <IconDecrease size={16} />
          5,70%
        </Tag>
      </Td>
    </Tr>
    <Tr>
      <Td>
        <Tag>French fries</Tag>
      </Td>
      <Td>0.24%</Td>
      <Td>
        <Tag variant="blueOutline">
          <IconGraph size={16} />
          0,00%
        </Tag>
      </Td>
      <Td>
        <Tag variant="greenOutline">
          <IconIncrease size={16} />
          3,25%
        </Tag>
      </Td>
    </Tr>
    <Tr>
      <Td>
        <Tag>Espresso</Tag>
      </Td>
      <Td>0.52%</Td>
      <Td>
        <Tag variant="redOutline">
          <IconDecrease size={16} />
          5,70%
        </Tag>
      </Td>
      <Td>
        <Tag variant="blueOutline">
          <IconGraph size={16} />
          0,00%
        </Tag>
      </Td>
    </Tr>
    <Tr>
      <Td>
        <Tag>Espresso</Tag>
      </Td>
      <Td>0.52%</Td>
      <Td>
        <Tag variant="redOutline">
          <IconDecrease size={16} />
          5,70%
        </Tag>
      </Td>
      <Td>
        <Tag variant="blueOutline">
          <IconGraph size={16} />
          0,00%
        </Tag>
      </Td>
    </Tr>
  </Tbody>
</Table>*/

export default FastestGrowingMenuItems;
