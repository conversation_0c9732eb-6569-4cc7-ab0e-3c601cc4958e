import { useState, useEffect } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { MENU_ADOPTION_CURVE_DROPDOWN } from "@/data/ingredients";
import BarChartVertical, {BarChartVerticalItem } from "@/components/charts/barChartVertical";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { highestPenetrationCompared } from "@/api/menu_items";
import titleCase from "voca/title_case";
import { SortingState } from "@tanstack/react-table";

interface HighestPenetrationComparedProps {
  className?: string;
}
//
// // TODO: get actual data from API
// const chartData: BarChartVerticalItem[] = [
//   {
//     id: 1,
//     label: "Mushroom Pizza",
//     color: ["#D2D0BC", "#A2D161"],
//     values: [302, 490],
//     seriesLabels: ["Previous period", "Current period"],
//   },
//   {
//     id: 2,
//     label: "Frittata",
//     color: ["#D2D0BC", "#A2D161"],
//     values: [201, 300],
//     seriesLabels: ["Previous period", "Current period"],
//   },
//   {
//     id: 3,
//     label: "Frapuccino",
//     color: ["#D2D0BC", "#A2D161"],
//     values: [242, 390],
//     seriesLabels: ["Previous period", "Current period"],
//   },
//   {
//     id: 4,
//     label: "Peppermint",
//     color: ["#D2D0BC", "#A2D161"],
//     values: [453, 480],
//     seriesLabels: ["Previous period", "Current period"],
//   },
//   {
//     id: 5,
//     label: "Cappuccino",
//     color: ["#D2D0BC", "#A2D161"],
//     values: [150, 210],
//     seriesLabels: ["Previous period", "Current period"],
//   },
// ];

interface HighestPenetrationMenuItem {
  guid: string;
  name: string;
  pen_rate: number;
  foodservice_growth: number;
  foodservice_prediction: number;
  quarters: Quarter[]
}

const HighestPenetrationCompared = ({
  className,
}: HighestPenetrationComparedProps) => {

  const { data: session } = useSession();
  const [pagination] = useState({
    pageIndex: 0, //initial page index
    pageSize: 5, //default page size
  });

  const [sorting] = useState<SortingState>([{id: 'foodservice_growth', desc: true}]);


  const { data: highest_penetration }  = useQuery({
    queryFn: () => highestPenetrationCompared({auth: session?.user.authorization as string, pagination, sorting}),
    queryKey: ["highest_penetration"],
    enabled: !!session?.user?.authorization,
  });


  const [selected, setSelected] = useState<string>("");
  const [chartData, setChartData] = useState([]);
  const [maxValue, setMaxValue] = useState<number>(100);

  useEffect(()=>{
    const highestPenetrationMap = highest_penetration?.data.map((data: HighestPenetrationMenuItem)=>{
      const {guid, name, quarters} = data
      const seriesData = quarters.map((q)=> q.pen_rate)
      return {
        id: guid,
        label: titleCase(name),
        color: ["#D2D0BC", "#A2D161"],
        values: seriesData,
        seriesLabels: ["Previous period", "Current period"],
      }
    })

    const values = highestPenetrationMap?.map((menuItem: BarChartVerticalItem)=> menuItem.values ).flat() || []

    setMaxValue(Math.max(...values))
    setChartData(highestPenetrationMap)

  }, [highest_penetration, setChartData, setMaxValue])



  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            Highest Penetration Compared to Previous Period
          </Heading>
          <Info
            tooltipId="highest-penetration-compared"
            content="Top 5 menu items with the highest penetration change compared to the period before."
          />
        </div>
        <Dropdown
          options={MENU_ADOPTION_CURVE_DROPDOWN}
          selectedValue={selected}
          onSelect={(value) => handleSelect(value)}
        >
          <Button variant="ghost" size="xs" className="w-6">
            <IconMore size={16} />
          </Button>
        </Dropdown>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="border-muted-100 rounded-lg border p-4">
          {
            chartData &&
            <BarChartVertical
              dataItems={chartData}
              symbol=""
              height={330}
              maxValue={maxValue}
            />
          }

        </div>
      </div>
    </Card>
  );
};

export default HighestPenetrationCompared;
