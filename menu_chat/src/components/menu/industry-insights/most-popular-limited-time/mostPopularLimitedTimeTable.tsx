import { useState } from "react";
import Paragraph from "@/components/base/paragraph";
import Card from "@/components/base/card";
import Tag from "@/components/base/tag";
import { IconMenuAdoption, IconChefHat } from "@/components/icons";
import DataSourcesReports from "@/components/shared/dataSourcesReports";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { limitedTimeOffers } from "@/api/menu_items";
import titleCase from "voca/title_case";
import Link from "next/link";
import Pagination from "@/components/table/pagination";
import { SortingState } from "@tanstack/react-table";
import MenuItemsFilters from "@/components/menu/menuItemsFilters";
import { LTO_FILTER } from "@/data/menu-items";
import { SafeImage } from "@/components/utils/helpers";

function truncateString(str: string, maxLength = 40) {
  if (str.length <= maxLength) return str;
  return str.slice(0, maxLength - 3) + "...";
}

const MostPopularLimitedTimeTable = () => {
  const { data: session } = useSession();
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort] = useState<string>("");

  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [pagination, setPagination] = useState({
    pageIndex: page - 1, //initial page index
    pageSize: perPage, //default page size
  });

  const [sorting] = useState<SortingState>([{
    desc: true,
    id: "date"
  }]);


  const { data: limited_time_offers } = useQuery({
    queryFn: () =>
      limitedTimeOffers({ auth: session?.user.authorization as string, pagination, sorting, search, filters }),
    queryKey: ["limited_time_offers", pagination, search, filters],
    enabled: !!session?.user?.authorization,
  });

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setPagination({
      pageIndex: newPage,
      pageSize: perPage,
    })
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPerPage(pageSize);
    setPagination({
      pageIndex: page,
      pageSize: pageSize,
    })
  };

  const limitedTimeOffersMap = limited_time_offers?.data?.map((lto: LimitedTimeOffer)=>{
    const {guid, name, description, img_url, date, cuisine, offer_url} = lto
    return (
      <Card key={guid} className="p-3">
        <Link href={offer_url}>
          <SafeImage
            src={img_url}
            alt={name}
            width={100}
            height={232}
            className="mb-1 h-[140px] w-full rounded-lg object-cover"
          />
        </Link>
        <div className="mb-2 rounded-lg border border-neutral-200 p-2">
          <div className="mb-2 flex items-center justify-between gap-2">
            <Tag>{titleCase(truncateString(name || ''))}</Tag>
            <Tag variant="white">
              <IconMenuAdoption size={16} className="text-neutral-500" />
              {/*Japanese*/}
            </Tag>
          </div>
          <Paragraph size="xs" className="ml-1 font-medium text-neutral-600">
            {titleCase(truncateString(description || ''))}
          </Paragraph>
        </div>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
              Cuisine Type
            </Paragraph>
            <Tag variant="white">
              <IconChefHat size={16} className="text-neutral-500" />
              {titleCase(truncateString(cuisine || ''))}
            </Tag>
          </div>
        </div>
        {/*<div className="flex w-full flex-col">*/}
        {/*  <div className="flex items-center justify-between gap-2 px-3 py-1">*/}
        {/*    <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">*/}
        {/*      Social Mentions*/}
        {/*    </Paragraph>*/}
        {/*    <Paragraph size="sm" className="font-medium">*/}
        {/*      94,950*/}
        {/*    </Paragraph>*/}
        {/*  </div>*/}
        {/*</div>*/}
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
              First Appearance
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {date}
            </Paragraph>
          </div>
        </div>
      </Card>

    )
  }) || []


  const handleSearch = (value: string) => {
    setPage(1)
    setPagination({
      pageIndex: page - 1,
      pageSize: perPage,
    })
    setSearch(value);
  };

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(() => newFilters);
  };

  const handleSortChange = (newSort: string) => {
    console.log('handleSortChange', newSort)
  };

  return (
    <div className="py-4">
      <MenuItemsFilters
        search={search}
        onSearch={handleSearch}
        filterOptions={LTO_FILTER}
        filters={filters}
        onFilter={handleFilterChange}
        sortOptions={[]}
        sort={sort}
        onSort={handleSortChange}
      />

      <div className="flex flex-col gap-5 mt-5">
        <div className="grid grid-cols-4 gap-5">
          {limitedTimeOffersMap}
        </div>
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={limited_time_offers?.total_records}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
        <DataSourcesReports />
      </div>
    </div>
  );
};

export default MostPopularLimitedTimeTable;
