import { useState } from "react";
import Paragraph from "@/components/base/paragraph";
import Card from "@/components/base/card";
import Tag from "@/components/base/tag";
import { IconMenuAdoption, IconChefHat, IconSearch } from "@/components/icons";
import Image from "next/image";
import Input from "@/components/base/input";
import DataSourcesReports from "@/components/shared/dataSourcesReports";

const MostPopularLimitedTimeTable = () => {
  const [search, setSearch] = useState<string>("");

  const handleSearch = (value: string) => {
    setSearch(value);
  };

  return (
    <div className="py-4">
      <div className="flex justify-end pt-3 pb-6">
        <Input
          value={search}
          placeholder="Search"
          inputSize="sm"
          className="w-52"
          icon={<IconSearch size={20} />}
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-4 gap-5">
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="mb-2 flex items-center justify-between gap-2 rounded-lg border border-neutral-200 p-2">
              <Tag>Crispy Bok Choy</Tag>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Japanese
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine Type
                </Paragraph>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  Deli/Sandwich
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Mentions
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  94,950
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Dec 2024
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="mb-2 flex items-center justify-between gap-2 rounded-lg border border-neutral-200 p-2">
              <Tag>Crispy Bok Choy</Tag>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Japanese
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine Type
                </Paragraph>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  Deli/Sandwich
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Mentions
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  94,950
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Dec 2024
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="mb-2 flex items-center justify-between gap-2 rounded-lg border border-neutral-200 p-2">
              <Tag>Crispy Bok Choy</Tag>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Japanese
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine Type
                </Paragraph>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  Deli/Sandwich
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Mentions
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  94,950
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Dec 2024
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="mb-2 flex items-center justify-between gap-2 rounded-lg border border-neutral-200 p-2">
              <Tag>Crispy Bok Choy</Tag>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Japanese
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine Type
                </Paragraph>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  Deli/Sandwich
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Mentions
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  94,950
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Dec 2024
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="mb-2 flex items-center justify-between gap-2 rounded-lg border border-neutral-200 p-2">
              <Tag>Crispy Bok Choy</Tag>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Japanese
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine Type
                </Paragraph>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  Deli/Sandwich
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Mentions
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  94,950
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Dec 2024
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="mb-2 flex items-center justify-between gap-2 rounded-lg border border-neutral-200 p-2">
              <Tag>Crispy Bok Choy</Tag>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Japanese
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine Type
                </Paragraph>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  Deli/Sandwich
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Mentions
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  94,950
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Dec 2024
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="mb-2 flex items-center justify-between gap-2 rounded-lg border border-neutral-200 p-2">
              <Tag>Crispy Bok Choy</Tag>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Japanese
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine Type
                </Paragraph>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  Deli/Sandwich
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Mentions
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  94,950
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Dec 2024
                </Paragraph>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <Image
              src={"/assets/images/<EMAIL>"}
              alt="chocolates"
              width={100}
              height={232}
              className="mb-1 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="mb-2 flex items-center justify-between gap-2 rounded-lg border border-neutral-200 p-2">
              <Tag>Crispy Bok Choy</Tag>
              <Tag variant="white">
                <IconMenuAdoption size={16} className="text-neutral-500" />
                Japanese
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine Type
                </Paragraph>
                <Tag variant="white">
                  <IconChefHat size={16} className="text-neutral-500" />
                  Deli/Sandwich
                </Tag>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Social Mentions
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  94,950
                </Paragraph>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  First Appearance
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  Dec 2024
                </Paragraph>
              </div>
            </div>
          </Card>
        </div>
        <DataSourcesReports />
      </div>
    </div>
  );
};

export default MostPopularLimitedTimeTable;
