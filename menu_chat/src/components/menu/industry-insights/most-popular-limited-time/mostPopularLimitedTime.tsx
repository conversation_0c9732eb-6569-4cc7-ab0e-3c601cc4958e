import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Tag from "@/components/base/tag";
import Image from "next/image";

import { ColumnDef, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import { useMemo, useState } from "react";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { limitedTimeOffers } from "@/api/menu_items";
import titleCase from "voca/title_case";


interface MostPopularLimitedTimeProps {
  className?: string;
}

const MostPopularLimitedTime = ({ className }: MostPopularLimitedTimeProps) => {
  const { data: session } = useSession();
  const { data: limited_time_offers, isLoading }  = useQuery({
    queryFn: () => limitedTimeOffers({auth: session?.user.authorization as string}),
    queryKey: ["limited_time_offers"],
    enabled: !!session?.user?.authorization,
  });

  const columns = useMemo<ColumnDef<LimitedTimeOffer>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: info => {
        const {img_url} = info.row.original

        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [imageError, setImageError] = useState(false);
        return (
          <>
            <span className="flex items-center gap-1">
              {(img_url && !imageError) &&
                <Image
                  src={img_url}
                  width={28}
                  height={28}
                  alt="image"
                  className="h-7 w-7 rounded-sm object-cover object-center"
                  onError={()=> setImageError(true)}
                />
              }
                <Tag>{titleCase(info.getValue<string>())}</Tag>
              </span>
          </>
        )
      },
    },
    {
      accessorKey: 'date',
      header: 'Date'
    }
  ], []);

  const data = useMemo(() => {
    if (!limited_time_offers?.data) return [];
    return limited_time_offers.data?.slice(0, 3) ?? [];
  }, [limited_time_offers]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Disable features we don't need in this view
    enableSorting: false,
    enableFilters: false,
    enableColumnResizing: false,
  });

  const emptyState = (
    <div className="text-center py-4">
      <p className="text-gray-500">No LTOs found.</p>
    </div>
  );

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular Limited Time Offers</Heading>
          <Info
            tooltipId="most-popular-limited-time"
            content="Most Popular Limited Time Offers based on the menu penetration."
          />
        </div>
        <Link href="/menu-items/most-popular-limited-time">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="px-4 pt-4 pb-2">
        <EnhancedTable
          table={table}
          isLoading={isLoading}
          emptyState={emptyState}
        />
      </div>

    </Card>
  );
};

/*<Table className="px-4 pt-4 pb-2">
  <Thead>
    <Tr>
      <Th>Name</Th>
      <Th>Penetration</Th>
    </Tr>
  </Thead>
  <Tbody>
    <Tr>
      <Td>
              <span className="flex items-center gap-1">
                <Image
                  src="/assets/images/<EMAIL>"
                  width={28}
                  height={28}
                  alt="image"
                  className="h-7 w-7 rounded-sm object-cover object-center"
                />
                <Tag>BBQ sauce</Tag>
              </span>
      </Td>
      <Td>0.24%</Td>
    </Tr>
    <Tr>
      <Td>
              <span className="flex items-center gap-1">
                <Image
                  src="/assets/images/<EMAIL>"
                  width={28}
                  height={28}
                  alt="image"
                  className="h-7 w-7 rounded-sm object-cover object-center"
                />
                <Tag>Atole</Tag>
              </span>
      </Td>
      <Td>0.52%</Td>
    </Tr>
    <Tr>
      <Td>
              <span className="flex items-center gap-1">
                <Image
                  src="/assets/images/<EMAIL>"
                  width={28}
                  height={28}
                  alt="image"
                  className="h-7 w-7 rounded-sm object-cover object-center"
                />
                <Tag>Clafoutis</Tag>
              </span>
      </Td>
      <Td>0.44%</Td>
    </Tr>
  </Tbody>
</Table>*/

export default MostPopularLimitedTime;
