import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Tag from "@/components/base/tag";
import Image from "next/image";
import Paragraph from "@/components/base/paragraph";
import { useMemo, useState } from "react";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { limitedTimeOffers } from "@/api/menu_items";
import titleCase from "voca/title_case";
import { SortingState } from "@tanstack/react-table";

interface MostPopularLimitedTimeProps {
  className?: string;
}



const MostPopularLimitedTime = ({ className }: MostPopularLimitedTimeProps) => {
  const { data: session } = useSession();
  // todo: Fix Broken Links First
  // const [sorting] = useState<SortingState>([{
  //   desc: true,
  //   id: "date"
  // }]);
  const [sorting] = useState<SortingState>([]);

  const { data: limited_time_offers } = useQuery({
    queryFn: () =>
      limitedTimeOffers({ auth: session?.user.authorization as string, sorting }),
    queryKey: ["limited_time_offers", sorting],
    enabled: !!session?.user?.authorization,
  });

  const data = useMemo(() => {
    if (!limited_time_offers?.data) return [];
    return limited_time_offers.data?.slice(0, 4) ?? [];
  }, [limited_time_offers]);


  // const emptyState = (
  //   <div className="p-4 text-center">
  //     <p className="text-gray-500">No LTOs found.</p>
  //   </div>
  // );

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Popular Limited Time Offers</Heading>
          <Info
            tooltipId="most-popular-limited-time"
            content="Most Popular Limited Time Offers based on the menu penetration."
          />
        </div>
        <Link href="/menu-items/most-popular-limited-time">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="grid grid-cols-4 gap-2 p-4">
        {data.map((item: LimitedTimeOffer, index: number) => (
          <div
            key={index}
            className="border-muted-100 flex flex-col gap-2 rounded-lg border p-2"
          >
            <Image
              src={item?.img_url || "/assets/images/<EMAIL>"}
              alt={item.name}
              width={100}
              height={140}
              className="bg-muted-50 h-[140px] w-full rounded-lg object-cover"
            />
            <div className="px-2 py-1">
              <Tag>
                <span className="inline-block max-w-48 truncate">
                  {titleCase(item.name)}
                </span>
              </Tag>
            </div>
            <div className="flex w-full flex-col">
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Penetration
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  8,6%
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2 px-3 py-1">
                <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                  Price
                </Paragraph>
                <Paragraph size="sm" className="font-medium">
                  $11,29
                </Paragraph>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default MostPopularLimitedTime;
