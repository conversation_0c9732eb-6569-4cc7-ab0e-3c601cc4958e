import { useState, useEffect } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import LineChart from "@/components/charts/lineChart";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import Link from "next/link";

import { useQuery } from "@tanstack/react-query";
import { fastestGrowingDashCategoriesChart } from "@/api/menu_items";
import { useSession } from "next-auth/react";
import titleCase from "voca/title_case";
import ColorGenerator from "@/components/utils/color-generator";

interface FastestGrowingDishProps {
  className?: string;
}

// const chartData: LineSeriesItem[] = [
//   {
//     id: 1,
//     label: "Salads",
//     color: "#FFBE05",
//     data: [0.4, 0.4, 0.3, 2.8, 6.5, 10.2, 9.8, 10.0, 9.8, 10.2, 11.5],
//   },
//   {
//     id: 3,
//     label: "Protein focused",
//     color: "#8BC539",
//     data: [0.0, 0.1, 0.3, 1.0, 2.2, 3.5, 4.5, 5.5, 6.3, 7.0, 7.8],
//   },
//   {
//     id: 4,
//     label: "Soups",
//     color: "#D273D8",
//     data: [0.1, 0.3, 0.4, 2.5, 3.7, 4.0, 4.3, 5.5, 6.5, 7.2, 8.1],
//   },
// ];

const colorGen = new ColorGenerator("123145");

interface MenuItemCategory {
  name: string;
  foodservice_growth: number;
  quarters: Quarter[];
}

const FastestGrowingDish = ({ className }: FastestGrowingDishProps) => {
  const { data: session } = useSession();

  const { data: menu_item_categories } = useQuery({
    queryFn: () =>
      fastestGrowingDashCategoriesChart({
        auth: session?.user.authorization as string,
      }),
    queryKey: ["menu_item_categories"],
    enabled: !!session?.user?.authorization,
  });

  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const [chartData, setChartData] = useState([]);
  const [chartLabels, setChartLabels] = useState([]);

  useEffect(() => {
    if (menu_item_categories?.length > 0) {
      const categoriesMap = menu_item_categories?.map(
        (menuItemCategory: MenuItemCategory) => {
          const { name, quarters } = menuItemCategory;
          return {
            label: titleCase(name),
            color: colorGen.getColor(name),
            data: quarters.map((q) => q.pen_rate.toFixed(2)),
          };
        },
      );
      setChartData(categoriesMap);
    }

    if (menu_item_categories?.length > 0) {
      const category = menu_item_categories[0];
      const { quarters } = category;
      const labels = quarters?.map((q: Quarter) => `Q${q.quarter} ${q.year}`);
      setChartLabels(labels);
    }
  }, [menu_item_categories, setChartData, setChartLabels]);

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Fastest Growing Dish Categories</Heading>
          <Info
            tooltipId="fastest-growing-ingredients"
            content="Dish categories with highest menu mentions growths."
          />
        </div>
        <div>
          <Link href="/menu-items/fastest-growing-dish-categories">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="border-muted-100 rounded-lg border p-4">
          <LineChart
            dataItems={chartData}
            labels={chartLabels}
            symbol="%"
            height={330}
          />
        </div>
      </div>
    </Card>
  );
};

export default FastestGrowingDish;
