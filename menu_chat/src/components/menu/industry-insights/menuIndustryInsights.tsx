import { useState } from "react";
import MenuItemsMenu from "../menuItemsMenu";
import IndustryInsights from "./industryInsights";
import ScrollContainer from "@/components/layout/scrollContainer";
import { IconClose } from "@/components/icons";
import Button from "@/components/base/button";

const MenuIndustryInsights = () => {
  const [filters, setFilters] = useState<string[]>([]);

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  return (
    <>
      <MenuItemsMenu filters={filters} onFilter={handleFilterChange} />
      <ScrollContainer>
        <div className="mt-[60px]">
          {filters.length > 0 && (
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-1.5">
                {filters.map((filter) => (
                  <span
                    key={filter}
                    className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
                  >
                    {filter}
                    <button
                      type="button"
                      className="flex size-4 items-center justify-center"
                      onClick={() =>
                        handleFilterChange(filters.filter((f) => f !== filter))
                      }
                    >
                      <IconClose
                        size={16}
                        className="text-muted-900 cursor-pointer"
                      />
                    </button>
                  </span>
                ))}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange([])}
              >
                Reset
              </Button>
            </div>
          )}
          <IndustryInsights />
        </div>
      </ScrollContainer>
    </>
  );
};

export default MenuIndustryInsights;
