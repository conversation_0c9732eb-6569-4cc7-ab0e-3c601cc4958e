import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconClose, IconMore } from "@/components/icons";
import MenuAdoptionCurveItem from "./menuAdoptionCurveItem";
import { CHART_DROPDOWN } from "@/data/ingredients";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchByMenuAdoption } from "@/api/menu_items";
import FilterDropdown from "@/components/base/filterDropdown";
import { MENUITEMS_FILTER } from "@/data/menu-items";


interface MenuAdoptionCurveProps {
  className?: string;
}

interface MenuAdoptionItemResponse {
  guid: string,
  name: string,
  menu_adoption: string,
  pen_rate: number,
  rn: number,
  id: number,
  cover_url: string
}

const MenuAdoptionCurve = ({ className }: MenuAdoptionCurveProps) => {
  const [selected, setSelected] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const { data: session } = useSession();

  const { data: menu_items_by_adoption }  = useQuery({
    queryFn: () => {
      return fetchByMenuAdoption({auth: session?.user.authorization as string, filters,})
    },
    queryKey: ["menu_items_by_adoption", filters],
    enabled: !!session?.user?.authorization,
  });

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(() => newFilters);
  };

  const items = [
    {
      type: "emergence" as const,
      data: menu_items_by_adoption?.emergence?.map((item: MenuAdoptionItemResponse) => {
        return {
          name: item.name,
          guid: item.guid,
          penetration: item.pen_rate,
          change: 0,
          image_url: item.cover_url,
          klass: "MenuItem"
        }
      }) || [],
    },
    {
      type: "growth" as const,
      data: menu_items_by_adoption?.growth?.map((item: MenuAdoptionItemResponse) => {
        return {
          name: item.name,
          guid: item.guid,
          penetration: item.pen_rate,
          change: 0,
          image_url: item.cover_url,
          klass: "MenuItem"
        }
      }) || [],
    },
    {
      type: "mainstream" as const,
      data: menu_items_by_adoption?.mainstream?.map((item: MenuAdoptionItemResponse) => {
        return {
          name: item.name,
          guid: item.guid,
          penetration: item.pen_rate,
          change: 0,
          image_url: item.cover_url,
          klass: "MenuItem"
        }
      }) || [],
    },
    {
      type: "mature" as const,
      data: menu_items_by_adoption?.mature?.map((item: MenuAdoptionItemResponse) => {
        return {
          name: item.name,
          guid: item.guid,
          penetration: item.pen_rate,
          change: 0,
          image_url: item.cover_url,
          klass: "MenuItem"
        }
      }) || [],
    },
  ];

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>
            Menu Adoption Curve for Fastest Growing Menu Items in each Category
          </Heading>
          <Info
            tooltipId="menu-adoption-tooltip"
            content="Menu items with highest penetration and menu mentions in the chosen period."
          />
          {filters.length > 0 && (
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-1.5">
                <span>—</span>
                {filters.map((filter) => (
                  <span
                    key={filter}
                    className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
                  >
                    {filter}
                    <button
                      type="button"
                      className="flex size-4 items-center justify-center"
                      onClick={() =>
                        handleFilterChange(filters.filter((f) => f !== filter))
                      }
                    >
                      <IconClose
                        size={16}
                        className="text-muted-900 cursor-pointer"
                      />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
        <div>
          <FilterDropdown
            selectedValue={filters}
            options={MENUITEMS_FILTER}
            onSelect={(value) => handleFilterChange(value)}
            showReset={true}
          />
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="overflow-x-auto">
        <div className="grid min-w-[1116px] grid-cols-4 gap-4 p-4">
          {items.map((item, key) => (
            <MenuAdoptionCurveItem
              key={`item-${key}-${item.data.length}`}
              type={item.type}
              items={item.data}
            />
          ))}
        </div>
      </div>
    </Card>
  );
};

export default MenuAdoptionCurve;
