import { useEffect, useRef } from "react";
import { twMerge } from "tailwind-merge";
import Tag from "@/components/base/tag";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import {
  IconCircleUpright,
  IconCircle,
  IconCircleFilled,
  IconCircleRight,
  IconIncrease,
} from "@/components/icons";
import HoverCard from "@/components/base/hoverCard";
import Image from "next/image";
import titleCase from "voca/title_case";
import { isValidUrl } from "@/components/utils/helpers";
import Link from "next/link";

interface MenuAdoptionCurveItemProps {
  className?: string;
  type: "emergence" | "growth" | "mature" | "mainstream";
  items: Array<{
    name: string;
    guid: string;
    penetration: number;
    change: number;
    image_url?: string;
    klass?: string
  }>;
}

const typeConfig = {
  emergence: {
    variant: "yellow",
    icon: IconCircle,
    label: "Emergence",
    description: "fine dining, mixology, earliest stage",
    height: 120,
    curveBias: 30,
  },
  growth: {
    variant: "green",
    icon: IconCircleUpright,
    label: "Growth",
    description: "trendy restaurants & specialty groceries",
    height: 160,
    curveBias: 100,
  },
  mainstream: {
    variant: "violet",
    icon: IconCircleFilled,
    label: "Mainstream",
    description: "chain restaurants & mainstream groceries",
    height: 320,
    curveBias: 100,
  },
  mature: {
    variant: "blue",
    icon: IconCircleRight,
    label: "Mature",
    description: "find it just about everywhere",
    height: 420,
    curveBias: -30,
  },
} as const;

const MenuAdoptionCurveItem = ({
  className,
  type,
  items,
}: MenuAdoptionCurveItemProps) => {
  const config = typeConfig[type];
  const containerRef = useRef<HTMLDivElement>(null);

  // randomly place tags in the container
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const tags = Array.from(container.children) as HTMLElement[];

    const placed: { x: number; y: number; width: number; height: number }[] =
      [];
    const minGap = 8;

    const curveBiasFactor = config.curveBias;

    let currentX = minGap;
    let currentY = minGap;
    let rowHeight = 0;

    tags.forEach((tag) => {
      const tagWidth = tag.offsetWidth;
      const tagHeight = tag.offsetHeight;

      if (currentX + tagWidth + minGap > containerRect.width) {
        currentX = minGap;
        currentY += rowHeight + minGap;
        rowHeight = 0;
      }

      const normalizedX = currentX / containerRect.width;
      const curveBias = normalizedX * curveBiasFactor;
      const adjustedY = currentY - curveBias;

      const willOverflow = adjustedY + tagHeight > containerRect.height;

      if (willOverflow) {
        tag.style.display = "none";
      } else {
        tag.style.position = "absolute";
        tag.style.transition = "transform 0.5s ease";
        tag.style.transform = `translate(${currentX}px, ${adjustedY}px)`;

        placed.push({
          x: currentX,
          y: adjustedY,
          width: tagWidth,
          height: tagHeight,
        });

        currentX += tagWidth + minGap;
        rowHeight = Math.max(rowHeight, tagHeight);
      }
    });
  }, [config.curveBias]);

  return (
    <div className={twMerge("flex flex-col items-start gap-4", className)}>
      <div className="border-muted-200 oveflow-hidden relative flex h-[465px] w-full flex-col justify-end rounded-lg border p-1">
        {type === "emergence" && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={259}
            height={465}
            viewBox="0 0 259 465"
            fill="none"
            className="absolute inset-0 h-full w-full rounded-lg"
            preserveAspectRatio="none"
          >
            <g clipPath="url(#ingredients_a)">
              <path
                fill="#FFF3D2"
                d="m54 324.205-64 3.5v137.43h1149l-4.5-397.43C1037 28.76 883.667-9.34 756 28.76 628.333 65.454 500.667 191.67 373 253.14c-127.667 62.406-244 65.564-319 71.064Z"
              />
              <path
                stroke="#FFC933"
                strokeWidth={2}
                d="m-9 327.704 65-3.5c85.5-6 189.333-9.069 317-71.39 127.667-61.387 255.333-187.43 383-224.075 127.667-38.046 291 0 382 41.965"
              />
            </g>
            <defs>
              <clipPath id="ingredients_a">
                <path fill="#fff" d="M0 0h259v465H0z" />
              </clipPath>
            </defs>
          </svg>
        )}
        {type === "growth" && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={259}
            height={465}
            viewBox="0 0 259 465"
            fill="none"
            className="absolute inset-0 h-full w-full rounded-lg"
            preserveAspectRatio="none"
          >
            <g clipPath="url(#ingredients_a)">
              <path
                fill="#E8F3D7"
                d="m-219 324.205-64 3.5v137.43H866l-4.5-397.43C764 28.76 610.667-9.34 483 28.76 355.333 65.454 227.667 191.67 100 253.14c-127.667 62.406-244 65.564-319 71.064Z"
              />
              <path
                stroke="#A2D161"
                strokeWidth={2}
                d="m-282 327.704 65-3.5c85.5-6 189.333-9.069 317-71.39 127.667-61.387 255.333-187.43 383-224.075 127.667-38.046 291 0 382 41.965"
              />
            </g>
            <defs>
              <clipPath id="ingredients_a">
                <path fill="#fff" d="M0 0h259v465H0z" />
              </clipPath>
            </defs>
          </svg>
        )}
        {type === "mainstream" && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={259}
            height={465}
            viewBox="0 0 259 465"
            fill="none"
            className="absolute inset-0 h-full w-full rounded-lg"
            preserveAspectRatio="none"
          >
            <g clipPath="url(#ingredients_a)">
              <path
                fill="#F2EFFF"
                d="m-491 324.205-64 3.5v137.43H594l-4.5-397.43C492 28.76 338.667-9.34 211 28.76 83.333 65.454-44.333 191.67-172 253.14c-127.667 62.406-244 65.564-319 71.064Z"
              />
              <path
                stroke="#BDA4CB"
                strokeWidth={2}
                d="m-554 327.704 65-3.5c85.5-6 189.333-9.069 317-71.39C-44.333 191.427 83.333 65.384 211 28.739c127.667-38.046 291 0 382 41.965"
              />
            </g>
            <defs>
              <clipPath id="ingredients_a">
                <path fill="#fff" d="M0 0h259v465H0z" />
              </clipPath>
            </defs>
          </svg>
        )}
        {type === "mature" && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={259}
            height={465}
            viewBox="0 0 259 465"
            fill="none"
            className="absolute inset-0 h-full w-full rounded-lg"
            preserveAspectRatio="none"
          >
            <g clipPath="url(#ingredients_a)">
              <path
                fill="#E2F3F9"
                d="m-780 327.205-64 3.5v137.43H305l-4.5-397.43C203 31.76 49.667-6.34-78 31.76-205.667 68.454-333.333 194.67-461 256.14c-127.667 62.406-244 65.564-319 71.064Z"
              />
              <path
                stroke="#78C5E3"
                strokeWidth={2}
                d="m-843 329.704 65-3.5c85.5-6 189.333-9.069 317-71.39 127.667-61.387 255.333-187.43 383-224.075 127.667-38.046 291 0 382 41.965"
              />
            </g>
            <defs>
              <clipPath id="ingredients_a">
                <path fill="#fff" d="M0 0h259v468H0z" />
              </clipPath>
            </defs>
          </svg>
        )}
        <div
          ref={containerRef}
          className={twMerge(
            type === "emergence" && `h-[120px]`,
            type === "growth" && `h-[160px]`,
            type === "mainstream" && `h-[320px]`,
            type === "mature" && `h-[420px]`,
          )}
        >
          {(items ?? []).map((item, index) => {
            // todo: Should prob wrap this into a function
            let link = `/ingredients/details/${item.guid}`
            if (item?.klass == "MenuItem"){
              link = `/menu-items/details/${item.guid}`
            }
            return (
              <HoverCard
                key={index}
                content={
                  // TODO: Add hover card content
                  <div className="flex min-w-44 flex-col gap-2">
                    <div className="flex items-center justify-start gap-2">
                      {item.image_url && isValidUrl(item.image_url) ? (
                        <Image
                          src={item.image_url}
                          alt={item.name}
                          width={36}
                          height={36}
                          className="size-9 rounded-sm object-cover"
                        />
                      ) : (
                        <div className="flex size-9 items-center justify-center rounded-sm bg-gray-200">
                          <span className="text-xs text-gray-500">N/A</span>
                        </div>
                      )}
                      <Tag size="sm">
                        {titleCase(item.name.slice(0, 20))}
                        {item.name.length > 20 && <span>...</span>}
                      </Tag>
                    </div>
                    <div className="flex items-end justify-between gap-2">
                      <div className="flex flex-col gap-1">
                        <Heading level={3}>
                          {item.penetration?.toFixed(2)}%
                        </Heading>
                        <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                          Penetration
                        </Paragraph>
                      </div>
                      <div className="flex flex-col gap-1 text-right">
                        <Tag variant="greenOutline">
                          <IconIncrease size={16} />
                          {item.change?.toFixed(2)}%
                        </Tag>
                        <Paragraph className="text-[11px] font-semibold text-neutral-600 uppercase">
                          change
                        </Paragraph>
                      </div>
                    </div>
                  </div>
                }
              >
                <Link href={link}>
                  <Tag
                    variant={config.variant}
                    size="sm"
                    className={twMerge(
                      "cursor-pointer border",
                      config.variant === "yellow" && "border-[#FFC933]",
                      config.variant === "green" && "border-[#A2D161]",
                      config.variant === "violet" && "border-[#BDA4CB]",
                      config.variant === "blue" && "border-[#78C5E3]",
                    )}
                  >
                    {titleCase(item.name.slice(0, 15))}
                    {item.name.length > 15 && <span>...</span>}
                  </Tag>
                </Link>
              </HoverCard>
            )
          })}
        </div>
      </div>
      {config && (
        <div>
          <Tag variant={config.variant} className="mb-1">
            <config.icon size={16} className="text-muted-900" />
            {config.label}
          </Tag>
          <Paragraph size="xs" className="text-neutral-600">
            {titleCase(config.description)}
          </Paragraph>
        </div>
      )}
    </div>
  );
};

export default MenuAdoptionCurveItem;
