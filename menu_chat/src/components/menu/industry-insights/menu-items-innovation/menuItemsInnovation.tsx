import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Button from "@/components/base/button";
import Link from "next/link";
import Tag from "@/components/base/tag";
import Image from "next/image";

import { menuItemsInnovation } from "@/api/menu_items";
import { useSession } from "next-auth/react";
import titleCase from "voca/title_case";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { ColumnDef, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import { getVariantForRate } from "@/components/utils/helpers";


interface MenuItemsInnovationProps {
  className?: string;
}

interface InnovationMenuItem {
  name: string
  description: string
  innovation_score: number
  image_url: string
}

const MenuItemsInnovation = ({ className }: MenuItemsInnovationProps) => {
  const { data: session } = useSession();
  const { data: innovative_items, isLoading }  = useQuery({
    queryFn: () => menuItemsInnovation({auth: session?.user.authorization as string}),
    queryKey: ["innovative_items"],
    enabled: !!session?.user?.authorization,
  });


  const columns = useMemo<ColumnDef<InnovationMenuItem>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: info => {
        const {image_url} = info.row.original
        return (
          <>
            <span className="flex items-center gap-1">
                {image_url && <Image
                  src={image_url}
                  width={28}
                  height={28}
                  alt="image"
                  className="h-7 w-7 rounded-sm object-cover object-center"
                />}
                <Tag>{titleCase(info.getValue<string>())}</Tag>
              </span>
          </>
        )
      },
    },
    {
      accessorKey: 'innovation_score',
      header: 'Score',
      cell: info => {
        const change = info.getValue<number>() * 100;
        const {type, icon} = getVariantForRate(change)
        return (
          <Tag variant={type}>
            {icon}
            {change?.toFixed(2)}%
          </Tag>)
      },
    }
  ], []);

  const data = useMemo(() => {
    if (!innovative_items?.data) return [];
    return innovative_items.data?.slice(0, 3) ?? [];
  }, [innovative_items]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Disable features we don't need in this view
    enableSorting: false,
    enableFilters: false,
    enableColumnResizing: false,
  });

  const emptyState = (
    <div className="text-center py-4">
      <p className="text-gray-500">No innovative items found.</p>
    </div>
  );



  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Menu Items Innovation</Heading>
          <Info
            tooltipId="innovation"
            content="New menu items appeared on menus in the last quarter."
          />
        </div>
        <Link href="/menu-items/menu-items-innovation">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="px-4 pt-4 pb-2">
        <EnhancedTable
          table={table}
          isLoading={isLoading}
          emptyState={emptyState}
        />
      </div>
    </Card>
  );
};

/*
<Table className="px-4 pt-4 pb-2">
  <Thead>
    <Tr>
      <Th>Name</Th>
      <Th>Penetration</Th>
      <Th>Prediction</Th>
    </Tr>
  </Thead>
  <Tbody>
    <Tr>
      <Td>
              <span className="flex items-center gap-1">
                <Image
                  src="/assets/images/<EMAIL>"
                  width={28}
                  height={28}
                  alt="image"
                  className="h-7 w-7 rounded-sm object-cover object-center"
                />
                <Tag>BBQ sauce</Tag>
              </span>
      </Td>
      <Td>0.24%</Td>
      <Td>
        <Tag variant="blueOutline">
          <IconGraph size={16} />
          0,00%
        </Tag>
      </Td>
    </Tr>
    <Tr>
      <Td>
              <span className="flex items-center gap-1">
                <Image
                  src="/assets/images/<EMAIL>"
                  width={28}
                  height={28}
                  alt="image"
                  className="h-7 w-7 rounded-sm object-cover object-center"
                />
                <Tag>Atole</Tag>
              </span>
      </Td>
      <Td>0.52%</Td>
      <Td>
        <Tag variant="redOutline">
          <IconDecrease size={16} />
          5,70%
        </Tag>
      </Td>
    </Tr>
    <Tr>
      <Td>
              <span className="flex items-center gap-1">
                <Image
                  src="/assets/images/<EMAIL>"
                  width={28}
                  height={28}
                  alt="image"
                  className="h-7 w-7 rounded-sm object-cover object-center"
                />
                <Tag>Clafoutis</Tag>
              </span>
      </Td>
      <Td>0.44%</Td>
      <Td>
        <Tag variant="greenOutline">
          <IconIncrease size={16} />
          3,25%
        </Tag>
      </Td>
    </Tr>
  </Tbody>
</Table>*/

export default MenuItemsInnovation;
