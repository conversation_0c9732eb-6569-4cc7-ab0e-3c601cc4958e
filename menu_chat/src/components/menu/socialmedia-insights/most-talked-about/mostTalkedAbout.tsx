import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import {
  IconTaste2,
  IconGraph,
  IconIncrease,
  IconDecrease,
} from "@/components/icons";
import SentimentBar from "@/components/charts/sentimentBar";

interface MostTalkedAboutMenuProps {
  className?: string;
}

const MostTalkedAboutMenu = ({ className }: MostTalkedAboutMenuProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Social Media Flavor Trends</Heading>
          <Info
            tooltipId="social-media-flavor-trends"
            content="Social media flavor trends for menu items based on the posts number"
          />
        </div>
        <Link href="/menu-items/social-media-flavor-trends">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Sentiment</Th>
            <Th>Post Number</Th>
            <Th>Growth</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconTaste2 size={16} className="text-neutral-500" />
                Sweet
              </Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[150px]"
                segments={{
                  "strongly-dislike": 10,
                  "somewhat-dislike": 25,
                  neutral: 25,
                  "somewhat-like": 25,
                  "strongly-like": 15,
                }}
              />
            </Td>
            <Td>32,451</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconTaste2 size={16} className="text-neutral-500" />
                Sweet
              </Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[150px]"
                segments={{
                  "strongly-dislike": 15,
                  "somewhat-dislike": 35,
                  neutral: 15,
                  "somewhat-like": 10,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>22,467</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                2,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconTaste2 size={16} className="text-neutral-500" />
                Sweet
              </Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[150px]"
                segments={{
                  "strongly-dislike": 10,
                  "somewhat-dislike": 25,
                  neutral: 25,
                  "somewhat-like": 25,
                  "strongly-like": 15,
                }}
              />
            </Td>
            <Td>19,451</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                1,90%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconTaste2 size={16} className="text-neutral-500" />
                Sweet
              </Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[150px]"
                segments={{
                  "somewhat-dislike": 15,
                  neutral: 40,
                  "somewhat-like": 20,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>5,432</Td>
            <Td>
              <Tag
                size="xs"
                variant="redOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconTaste2 size={16} className="text-neutral-500" />
                Sweet
              </Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[150px]"
                segments={{
                  "strongly-dislike": 15,
                  "somewhat-dislike": 35,
                  neutral: 15,
                  "somewhat-like": 10,
                  "strongly-like": 25,
                }}
              />
            </Td>
            <Td>10,478</Td>
            <Td>
              <Tag
                size="xs"
                variant="blueOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag variant="white">
                <IconTaste2 size={16} className="text-neutral-500" />
                Sweet
              </Tag>
            </Td>
            <Td>
              <SentimentBar
                className="max-w-[150px]"
                segments={{
                  "strongly-dislike": 25,
                  "somewhat-dislike": 15,
                  neutral: 10,
                  "somewhat-like": 15,
                  "strongly-like": 20,
                }}
              />
            </Td>
            <Td>22,467</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                2,70%
              </Tag>
            </Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostTalkedAboutMenu;
