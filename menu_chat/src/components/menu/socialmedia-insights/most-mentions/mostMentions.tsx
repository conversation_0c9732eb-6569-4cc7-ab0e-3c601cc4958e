import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Table, { Thead, Tbody, Tr, Th, Td } from "@/components/table/table";
import Tag from "@/components/base/tag";
import { IconGraph, IconIncrease, IconDecrease } from "@/components/icons";

interface MostSocialMediaMentionsProps {
  className?: string;
}

const MostSocialMediaMentions = ({
  className,
}: MostSocialMediaMentionsProps) => {
  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Social Media Mentions</Heading>
          <Info
            tooltipId="most-social-media-mentions"
            content="Top 5 most mentioned menu items on the social media"
          />
        </div>
        <Link href="/menu-items/social-media-trends">
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <Table className="px-4 pt-4 pb-2">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Engagements</Th>
            <Th>Post Number</Th>
            <Th>Growth</Th>
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            <Td>
              <Tag>Scallion pancakes</Tag>
            </Td>
            <Td>32,451</Td>
            <Td>22,321</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>BBQ pulled chicken</Tag>
            </Td>
            <Td>32,451</Td>
            <Td>22,321</Td>
            <Td>
              <Tag
                size="xs"
                variant="blueOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconGraph size={16} />
                0,00%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Stuffed jalapenos</Tag>
            </Td>
            <Td>32,451</Td>
            <Td>22,321</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Coconut macaroons</Tag>
            </Td>
            <Td>32,451</Td>
            <Td>22,321</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Chocolate brownies</Tag>
            </Td>
            <Td>32,451</Td>
            <Td>22,321</Td>
            <Td>
              <Tag
                size="xs"
                variant="greenOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconIncrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
          <Tr>
            <Td>
              <Tag>Stuffed jalapenos</Tag>
            </Td>
            <Td>32,451</Td>
            <Td>22,321</Td>
            <Td>
              <Tag
                size="xs"
                variant="redOutline"
                className="ml-auto flex w-fit items-center"
              >
                <IconDecrease size={16} />
                5,70%
              </Tag>
            </Td>
          </Tr>
        </Tbody>
      </Table>
    </Card>
  );
};

export default MostSocialMediaMentions;
