import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Info from "@/components/base/info";
import Link from "next/link";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconMore } from "@/components/icons";
import { CHART_DROPDOWN } from "@/data/ingredients";
import LineChart, { LineSeriesItem } from "@/components/charts/lineChart";

interface SocialMediaMenuTrendsProps {
  className?: string;
}

const SocialMediaMenuTrends = ({ className }: SocialMediaMenuTrendsProps) => {
  const [selected, setSelected] = useState<string>("");

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  const chartData: LineSeriesItem[] = [
    {
      id: 1,
      label: "<PERSON><PERSON><PERSON><PERSON>",
      color: "#8BC539",
      data: [0.0, 0.2, 0.5, 1.0, 1.6, 2.5, 2.3, 3.0, 3.8, 4.5, 5.9],
    },
    {
      id: 2,
      label: "Sushi tacos",
      color: "#FFBE05",
      data: [0.1, 0.3, 0.7, 1.5, 2.7, 4.1, 4.0, 5.3, 6.5, 7.4, 8.9],
    },
    {
      id: 3,
      label: "Kugel",
      color: "#D273D8",
      data: [0.0, 0.2, 0.5, 1.2, 2.1, 3.0, 2.8, 3.6, 4.1, 4.9, 5.7],
    },
    {
      id: 4,
      label: "Linzer Cookies",
      color: "#9571AD",
      data: [0.1, 0.3, 0.6, 1.4, 2.0, 2.9, 3.5, 4.9, 5.2, 6.3, 7.5],
    },
    {
      id: 5,
      label: "Vegan bites",
      color: "#FF6D56",
      data: [0.0, 0.1, 0.3, 0.8, 1.6, 1.9, 2.7, 2.6, 3.5, 4.2, 5.0],
    },
  ];

  return (
    <Card className={twMerge(className)}>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Menu Items Social Media Trends</Heading>
          <Info
            tooltipId="social-media-menu-trends"
            content="Menu items social media trends by most engaged/viewed content."
          />
        </div>
        <div className="flex items-center gap-1">
          <Link href="/menu-items/social-media-trends">
            <Button variant="ghost" size="xs">
              View all
            </Button>
          </Link>
          <Dropdown
            options={CHART_DROPDOWN}
            selectedValue={selected}
            onSelect={(value) => handleSelect(value)}
          >
            <Button variant="ghost" size="xs" className="w-6">
              <IconMore size={16} />
            </Button>
          </Dropdown>
        </div>
      </CardHeader>
      <div className="px-4 py-3">
        <div className="border-muted-100 rounded-lg border p-4">
          <LineChart
            dataItems={chartData}
            labels={[
              "Feb 5",
              "Feb 12",
              "Feb 19",
              "Mar 5",
              "Mar 12",
              "Mar 19",
              "Mar 26",
              "Apr 2",
              "Apr 9",
              "Apr 16",
              "Apr 23",
            ]}
            symbol="$"
            height={320}
          />
        </div>
      </div>
    </Card>
  );
};

export default SocialMediaMenuTrends;
