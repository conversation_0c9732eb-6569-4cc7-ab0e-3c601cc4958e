import BarChart, { BarChartItem } from "@/components/charts/barChart";

// TODO: get actual data from API
const chartData: BarChartItem[] = [
  {
    id: 1,
    label: "Health & Wellness",
    color: "#78C5E3",
    value: 55,
    symbol: "%",
  },
  { id: 2, label: "Global Flavors", color: "#FAAB61", value: 25, symbol: "%" },
  {
    id: 3,
    label: "Pricing and Inflation",
    color: "#DF9DE4",
    value: 60,
    symbol: "%",
  },
  { id: 4, label: "Life Quality", color: "#FF9985", value: 20, symbol: "%" },
  { id: 5, label: "School", color: "#BDA4CB", value: 35, symbol: "%" },
];

const BabyBoomers = () => {
  return (
    <div className="border-muted-200 rounded-lg border p-2">
      <BarChart dataItems={chartData} symbol="%" maxValue={100} height={270} />
    </div>
  );
};

export default BabyBoomers;
