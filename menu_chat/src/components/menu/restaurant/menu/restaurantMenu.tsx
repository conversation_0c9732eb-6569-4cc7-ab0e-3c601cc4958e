import { useState } from "react";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Input from "@/components/base/input";
import { IconDownload, IconClose, IconSearch } from "@/components/icons";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { useRestaurant } from "@/contexts/RestaurantContext";
import { fetchRestaurantItems } from "@/api/restaurants";
import titleCase from "voca/title_case";
import slugify from "voca/slugify";
import { exportToCSV } from "@/lib/exportCsv";

interface MenuItem {
  id: number;
  name: string;
  price: number;
  description: string;
  category: string;
}

const RestaurantMenu = () => {
  const { data: session } = useSession();
  const { id, guid, business_name } = useRestaurant();

  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  // const [sort, setSort] = useState<string>("");

  const { data: restaurant_items } = useQuery({
    queryFn: () => {
      return fetchRestaurantItems({
        auth: session?.user.authorization as string,
        guid: guid || id,
        secondary_filter: search,
        filters,
      });
    },
    queryKey: ["restaurant_items", guid, search, filters],
    enabled: !!session?.user?.authorization,
  });
  const categories = [
    ...new Set(restaurant_items?.data.map((item: MenuItem) => item.category)),
  ] as string[];

  const itemsMapFn = (items: MenuItem[]) => {
    return items.map((item: MenuItem) => {
      const { id, name, price, description } = item;
      return (
        <div
          key={id}
          className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4"
        >
          <Paragraph size="sm" className="font-semibold">
            {titleCase(name)}
            {price && <span className="text-neutral-600"> – ${price}</span>}
          </Paragraph>
          <Paragraph size="sm" className="text-neutral-700">
            {description?.charAt(0).toUpperCase() + description?.slice(1)}
          </Paragraph>
        </div>
      );
    });
  };

  const categoriesMap = categories.map((category) => {
    const category_items = restaurant_items?.data.filter(
      (item: MenuItem) => item.category === category,
    );

    return (
      <div key={category} className="mx-4 flex flex-col gap-4 py-4">
        <Heading level={4}>{titleCase(category)}</Heading>
        {itemsMapFn(category_items)}
      </div>
    );
  });

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(() => newFilters);
  };

  const handleSearch = (value: string) => {
    setSearch(value);
  };

  const onExport = () => {
    const filename = `${slugify(business_name || "restaurant")}_menu.csv`;
    const dataToExport = (restaurant_items?.data || []).map(
      ({ name, price, description, category }: MenuItem) => ({
        Name: titleCase(name),
        Price: price ? `$${price}` : "",
        Description:
          description?.charAt(0).toUpperCase() + description?.slice(1),
        Category: titleCase(category),
      }),
    );

    exportToCSV(filename, dataToExport);
  };

  return (
    <>
      <div className="flex items-center justify-between gap-3 pt-3">
        <Button variant="primary" size="sm" onClick={onExport}>
          <IconDownload size={20} />
          Export
        </Button>
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
      </div>
      {filters.length > 0 && (
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1.5">
            {filters.map((filter) => (
              <span
                key={filter}
                className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
              >
                {filter}
                <button
                  type="button"
                  className="flex size-4 items-center justify-center"
                  onClick={() =>
                    handleFilterChange(filters.filter((f) => f !== filter))
                  }
                >
                  <IconClose
                    size={16}
                    className="text-muted-900 cursor-pointer"
                  />
                </button>
              </span>
            ))}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleFilterChange([])}
          >
            Reset
          </Button>
        </div>
      )}
      <Card>
        <div className="p-1 text-neutral-900">
          <div className="border-b border-neutral-500/15 px-4 py-3">
            <Heading level={3}>Menu</Heading>
          </div>
          {categoriesMap}
        </div>
      </Card>
    </>
  );
};

export default RestaurantMenu;
