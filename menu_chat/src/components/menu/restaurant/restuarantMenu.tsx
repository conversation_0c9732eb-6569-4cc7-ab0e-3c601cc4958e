import { useState } from "react";
import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import Input from "@/components/base/input";
import { IconDownload, IconClose, IconSearch } from "@/components/icons";
import FilterDropdown from "@/components/base/filterDropdown";
import { MENUITEMS_FILTER } from "@/data/menu-items";

const RestaurantMenu = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    console.log("Search value:", value);
  };

  return (
    <>
      <div className="flex items-center justify-between gap-3 pt-3">
        <Button variant="primary" size="sm">
          <IconDownload size={20} />
          Export
        </Button>
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => handleSearch(e.target.value)}
          />
          <FilterDropdown
            selectedValue={filters}
            options={MENUITEMS_FILTER}
            onSelect={handleFilterChange}
          />
        </div>
      </div>
      {filters.length > 0 && (
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1.5">
            {filters.map((filter) => (
              <span
                key={filter}
                className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
              >
                {filter}
                <button
                  type="button"
                  className="flex size-4 items-center justify-center"
                  onClick={() =>
                    handleFilterChange(filters.filter((f) => f !== filter))
                  }
                >
                  <IconClose
                    size={16}
                    className="text-muted-900 cursor-pointer"
                  />
                </button>
              </span>
            ))}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleFilterChange([])}
          >
            Reset
          </Button>
        </div>
      )}
      <Card>
        <div className="p-1 text-neutral-900">
          <div className="border-b border-neutral-500/15 px-4 py-3">
            <Heading level={3}>Menu</Heading>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Cold Appetizers</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Heirloom Tomato Consommé{" "}
                <span className="text-neutral-600">– $18.45</span>
              </Paragraph>
              <Paragraph size="sm" className="text-neutral-700">
                Fresh heirloom tomatoes, basil oil, and brioche croutons
                (heirloom tomatoes, basil, garlic, brioche, olive oil, salt,
                pepper).
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Seared Scallops with Citrus-Ginger Emulsion{" "}
                <span className="text-neutral-600">– $30</span>
              </Paragraph>
              <Paragraph size="sm" className="text-neutral-700">
                Fresh heirloom tomatoes, basil oil, and brioche croutons
                (heirloom tomatoes, basil, garlic, brioche, olive oil, salt,
                pepper).
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Caviar and Avocado Tartine{" "}
                <span className="text-neutral-600">– $30</span>
              </Paragraph>
              <Paragraph size="sm" className="text-neutral-700">
                Fresh heirloom tomatoes, basil oil, and brioche croutons
                (heirloom tomatoes, basil, garlic, brioche, olive oil, salt,
                pepper).
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Hot Appetizers</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Wild Mushroom Risotto Croquettes{" "}
                <span className="text-neutral-600">– $18.45</span>
              </Paragraph>
              <Paragraph size="sm" className="text-neutral-700">
                Crispy risotto balls filled with wild mushrooms, served with
                truffle aioli (arborio rice, wild mushrooms, truffle oil,
                parmesan cheese, eggs, breadcrumbs, olive oil).
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Pan-Seared Foie Gras{" "}
                <span className="text-neutral-600">– $30</span>
              </Paragraph>
              <Paragraph size="sm" className="text-neutral-700">
                Served with huckleberry jam and toasted baguette slices (foie
                gras, huckleberry jam, baguette, fleur de sel, pickled pink
                peppercorns).
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Grilled Octopus with Smoked Paprika Aioli{" "}
                <span className="text-neutral-600">– $30</span>
              </Paragraph>
              <Paragraph size="sm" className="text-neutral-700">
                Char-grilled octopus, smoked paprika aioli, pickled red peppers
                (octopus, smoked paprika, garlic, lemon juice, olive oil,
                pickled red peppers, salt, pepper).
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Main Courses</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Grilled Wagyu Ribeye{" "}
                <span className="text-neutral-600">– $45</span>
              </Paragraph>
              <Paragraph size="sm">
                Served with roasted garlic mashed potatoes and broccolini (wagyu
                beef ribeye, potatoes, garlic, butter, broccolini, olive oil,
                salt, pepper).
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Pan-Seared Halibut with Lemon-Dill Sauce{" "}
                <span className="text-neutral-600">– $38</span>
              </Paragraph>
              <Paragraph size="sm">
                Fresh halibut fillet, lemon-dill sauce, sautéed spinach
                (halibut, lemon juice, fresh dill, garlic, olive oil, spinach,
                salt, pepper).
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Vegetarian Stuffed Portobello Mushrooms{" "}
                <span className="text-neutral-600">– $38</span>
              </Paragraph>
              <Paragraph size="sm">
                Mushrooms filled with goat cheese, herbs, and sun-dried
                tomatoes, served with roasted vegetables (portobello mushrooms,
                goat cheese, sun-dried tomatoes, fresh herbs, olive oil, salt,
                pepper).
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Duck Breast with Cherry Compote{" "}
                <span className="text-neutral-600">– $40</span>
              </Paragraph>
              <Paragraph size="sm">
                Seared duck breast, cherry compote, wild rice pilaf (duck
                breast, cherries, port wine, shallots, wild rice, olive oil,
                salt, pepper)
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Desserts</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Dark Chocolate Soufflé{" "}
                <span className="text-neutral-600">– $15</span>
              </Paragraph>
              <Paragraph size="sm">
                Warm dark chocolate soufflé, vanilla ice cream (dark chocolate,
                eggs, sugar, flour, vanilla ice cream).
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Lemon Lavender Crème Brûlée{" "}
                <span className="text-neutral-600">– $14</span>
              </Paragraph>
              <Paragraph size="sm">
                Lemon and lavender-infused custard base topped with caramelized
                sugar (lemons, lavender, cream, sugar, eggs, vanilla,
                caramelized sugar)
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Seasonal Fruit Tart{" "}
                <span className="text-neutral-600">– $14</span>
              </Paragraph>
              <Paragraph size="sm">
                Fresh seasonal fruits arranged on a bed of almond cream, pastry
                crust (seasonal fruits, almond cream, pastry dough, sugar, eggs)
              </Paragraph>
            </div>
          </div>
        </div>
        <div className="p-1 text-neutral-900">
          <div className="border-b border-neutral-500/15 px-4 py-3">
            <Heading level={3}>Wine List</Heading>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Sparkling Wines</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Prosecco Bellussi (Italy){" "}
                <span className="text-neutral-600">– $45</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Roederer Estate Sparkling Brut (USA){" "}
                <span className="text-neutral-600">– $35</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Scharffenberger Sparkling Brutt (USA){" "}
                <span className="text-neutral-600">– $25</span>
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>White Wines</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Sauvignon Blanc (France){" "}
                <span className="text-neutral-600">– $45</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Pinot Grigio (USA){" "}
                <span className="text-neutral-600">– $35</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Chardonnay (USA) <span className="text-neutral-600">– $25</span>
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Red Wines</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Pinot Noir (USA) <span className="text-neutral-600">– $45</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Merlot (France) <span className="text-neutral-600">– $50</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Cabernet Sauvignon (USA){" "}
                <span className="text-neutral-600">– $55</span>
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Dessert Wine</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Moscato d’Asti (Italy){" "}
                <span className="text-neutral-600">– $30</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Tawny Port (Portugal){" "}
                <span className="text-neutral-600">– $50</span>
              </Paragraph>
            </div>
          </div>
        </div>
        <div className="p-1 text-neutral-900">
          <div className="border-b border-neutral-500/15 px-4 py-3">
            <Heading level={3}>Non-Alcoholic Beverages</Heading>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Fresh Juices</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Orange Juice <span className="text-neutral-600">– $45</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Grapefruit Juice <span className="text-neutral-600">– $35</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Cranberry Juice <span className="text-neutral-600">– $25</span>
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Artisanal Sodas</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Ginger Ale <span className="text-neutral-600">– $5</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Lemon-Lime Soda <span className="text-neutral-600">– $6</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                CreamSoda <span className="text-neutral-600">– $4</span>
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Herbal Teas</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Peppermint Tea <span className="text-neutral-600">– $5</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Chamomile Tea <span className="text-neutral-600">– $6</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Lemon Balm Tea <span className="text-neutral-600">– $4</span>
              </Paragraph>
            </div>
          </div>
          <div className="mx-4 flex flex-col gap-4 py-4">
            <Heading level={4}>Specialty Coffee Drinks</Heading>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-4">
              <Paragraph size="sm" className="font-semibold">
                Espresso <span className="text-neutral-600">– $5</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Cappuccino <span className="text-neutral-600">– $6</span>
              </Paragraph>
            </div>
            <div className="flex flex-col gap-2.5 border-b border-neutral-500/15 pb-3">
              <Paragraph size="sm" className="font-semibold">
                Latte <span className="text-neutral-600">– $4</span>
              </Paragraph>
            </div>
          </div>
        </div>
      </Card>
    </>
  );
};

export default RestaurantMenu;
