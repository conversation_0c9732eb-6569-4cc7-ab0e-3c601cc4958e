import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import { IconIncrease } from "@/components/icons";

const SocialConversations = () => {
  return (
    <Card className="p-4">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <Heading level={5}>Social Conversations</Heading>
      </div>
      <div className="flex items-end justify-between">
        <div className="flex items-end gap-2">
          <Heading level={2} className="flex items-end gap-1.5">
            <span className="text-4">+</span>3.6K
          </Heading>
          <Paragraph size="xs" className="relative -top-0.5 text-neutral-600">
            conversation on social media
          </Paragraph>
        </div>
        <Tag size="xs" variant="greenOutline" className="px-1">
          <IconIncrease size={16} /> 8.30 %
        </Tag>
      </div>
    </Card>
  );
};

export default SocialConversations;
