import RestaurantGeneralDetails from "./restaurantGeneralDetails";
import DataSourcesReports from "@/components/shared/dataSourcesReports";
import GeographicPopularity from "@/components/menu/restaurant/general/geographicPopularity";
import SocialConversations from "./socialConversation";
import SocialSentiments from "./socialSentiments";
import LimitedTimeOffers from "./limited-time-offers/limitedTimeOffers";
import MostEngagedSocialMediaPosts from "./social-media-posts/mostEngagedSocialMediaPosts";

const RestaurantProfile = () => {
  return (
    <section className="mt-[60px] mb-24 flex flex-col gap-5 py-4">
      <RestaurantGeneralDetails />
      <div className="grid grid-cols-12 gap-5">
        <div className="col-span-7 flex flex-col gap-5">
          <GeographicPopularity />
        </div>
        <div className="col-span-5 flex flex-col gap-5">
          <SocialConversations />
          <SocialConversations />
          <SocialSentiments />
        </div>
      </div>
      <LimitedTimeOffers />
      <MostEngagedSocialMediaPosts />
      <DataSourcesReports />
    </section>
  );
};

export default RestaurantProfile;
