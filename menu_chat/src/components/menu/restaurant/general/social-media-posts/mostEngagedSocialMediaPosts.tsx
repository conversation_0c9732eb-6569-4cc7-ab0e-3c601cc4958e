import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import SocialMediaCard from "@/components/menu/details/socialmedia-insights/socialMediaCard";

const LimitedTimeOffers = () => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Most Engaged Social Media Posts</Heading>
        </div>
      </CardHeader>
      <div className="grid grid-cols-4 gap-2 p-4">
        <SocialMediaCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="chocolates"
          platform="instagram"
          username="Iblacksmithchicagorestaurant"
          engagementValue="260,178"
        />
        <SocialMediaCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="chocolates"
          platform="instagram"
          username="Ifuasiansushiplace"
          engagementValue="260,178"
        />
        <SocialMediaCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="chocolates"
          platform="instagram"
          username="chinatownmichigan"
          engagementValue="260,178"
        />
        <SocialMediaCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="chocolates"
          platform="instagram"
          username="blacksmithchicagorestaurant"
          engagementValue="260,178"
        />
      </div>
    </Card>
  );
};

export default LimitedTimeOffers;
