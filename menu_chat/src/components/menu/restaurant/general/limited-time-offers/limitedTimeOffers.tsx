import Heading from "@/components/base/heading";
import Card, { CardHeader } from "@/components/base/card";
import Button from "@/components/base/button";
import Link from "next/link";
import LimitedTimeOffersCard from "./limitedTimeOffersCard";
import { useRouter } from "next/router";

const LimitedTimeOffers = () => {
  const router = useRouter();
  const restaurantId = router.query.id;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Heading level={4}>Limited Time Offers</Heading>
        </div>
        <Link
          href={`/menu-items/restaurant/${restaurantId}/all-limited-time-offers`}
        >
          <Button variant="ghost" size="xs">
            View all
          </Button>
        </Link>
      </CardHeader>
      <div className="grid grid-cols-4 gap-2 p-4">
        <LimitedTimeOffersCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="chocolates"
          menuItemName="Chicken Protein"
          penetrationValue="8,6%"
          priceValue="$11,29"
        />
        <LimitedTimeOffersCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="chocolates"
          menuItemName="Fava Bean"
          penetrationValue="8,6%"
          priceValue="$11,29"
        />
        <LimitedTimeOffersCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="chocolates"
          menuItemName="Algae"
          penetrationValue="8,6%"
          priceValue="$11,29"
        />
        <LimitedTimeOffersCard
          imageSrc="/assets/images/<EMAIL>"
          imageAlt="chocolates"
          menuItemName="Ube"
          penetrationValue="8,6%"
          priceValue="$11,29"
        />
      </div>
    </Card>
  );
};

export default LimitedTimeOffers;
