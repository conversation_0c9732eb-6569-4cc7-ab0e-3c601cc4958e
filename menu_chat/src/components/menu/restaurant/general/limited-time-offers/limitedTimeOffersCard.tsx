import Card from "@/components/base/card";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import { SafeImage } from "@/components/utils/helpers";

interface LimitedTimeOffersCard {
  imageSrc: string;
  imageAlt: string;
  menuItemName: string;
  penetrationLabel?: string;
  priceLabel?: string;
  penetrationValue: string | number;
  priceValue: string | number;
  className?: string;
  imageClassName?: string;
}

const LimitedTimeOffersCard = ({
  imageSrc,
  imageAlt,
  menuItemName,
  penetrationLabel = "Penetration",
  priceLabel = "Price",
  penetrationValue,
  priceValue,
  className = "",
  imageClassName = "h-[140px] w-full rounded-lg object-cover",
}: LimitedTimeOffersCard) => {
  return (
    <Card>
      <div className={`flex flex-col gap-2 p-1 ${className}`}>
        <SafeImage
          src={imageSrc}
          alt={imageAlt}
          width={100}
          height={140}
          className={imageClassName}
        />
        <div className="px-2 py-1">
          <Paragraph
            size="sm"
            className="flex items-center gap-1 font-semibold"
          >
            <Tag>{menuItemName}</Tag>
          </Paragraph>
        </div>
        <div className="flex w-full flex-col">
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="font-archivo text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
              {penetrationLabel}
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {penetrationValue}
            </Paragraph>
          </div>
          <div className="flex items-center justify-between gap-2 px-3 py-1">
            <Paragraph className="font-archivo text-[11px] font-semibold tracking-[0.2px] text-neutral-600 uppercase">
              {priceLabel}
            </Paragraph>
            <Paragraph size="sm" className="font-medium">
              {priceValue}
            </Paragraph>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default LimitedTimeOffersCard;
