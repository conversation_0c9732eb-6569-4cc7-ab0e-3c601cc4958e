import DataSourcesReports from "@/components/shared/dataSourcesReports";
import LimitedTimeOffersCard from "./limitedTimeOffersCard";
import Input from "@/components/base/input";
import { IconSearch } from "@/components/icons";
import { useState } from "react";

const AllLimitedTimeOffers = () => {
  const [search, setSearch] = useState<string>("");

  const handleSearch = (value: string) => {
    setSearch(value);
  };

  return (
    <section className="pb-12">
      <div className="flex items-center justify-end py-3">
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
      </div>
      <div className="flex flex-col gap-5 pt-4">
        <div className="grid grid-cols-4 gap-5 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          <LimitedTimeOffersCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            menuItemName="Chicken Protein"
            penetrationValue="8,6%"
            priceValue="$11,29"
          />
          <LimitedTimeOffersCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            menuItemName="Fava Bean"
            penetrationValue="8,6%"
            priceValue="$11,29"
          />
          <LimitedTimeOffersCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            menuItemName="Algae"
            penetrationValue="8,6%"
            priceValue="$11,29"
          />
          <LimitedTimeOffersCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            menuItemName="Ube"
            penetrationValue="8,6%"
            priceValue="$11,29"
          />
          <LimitedTimeOffersCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            menuItemName="Chicken Protein"
            penetrationValue="8,6%"
            priceValue="$11,29"
          />
          <LimitedTimeOffersCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            menuItemName="Fava Bean"
            penetrationValue="8,6%"
            priceValue="$11,29"
          />
          <LimitedTimeOffersCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            menuItemName="Algae"
            penetrationValue="8,6%"
            priceValue="$11,29"
          />
          <LimitedTimeOffersCard
            imageSrc="/assets/images/<EMAIL>"
            imageAlt="chocolates"
            menuItemName="Ube"
            penetrationValue="8,6%"
            priceValue="$11,29"
          />
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default AllLimitedTimeOffers;
