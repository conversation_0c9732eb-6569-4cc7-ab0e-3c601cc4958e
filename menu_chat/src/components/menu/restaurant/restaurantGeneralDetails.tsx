import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import ImageSlider from "@/components/base/imageSlider";
import { IconChefHat, IconCopy } from "@/components/icons";

const RestaurantGeneralDetails = () => {
  return (
    <Card className="p-4">
      <div className="flex items-start justify-between gap-2">
        <div className="flex w-1/2 gap-2">
          <div className="w-1/2">
            <Heading level={3} className="mb-3">
              The Roaming Spoon
            </Heading>
            <div className="flex flex-wrap gap-1">
              <Paragraph size="xs" className="text-neutral-700">
                New York, US <br />
                345 W 42nd St, New York, NY 10036
              </Paragraph>
              <Paragraph size="xs" className="text-neutral-600">
                The Roaming Spoon offers a sophisticated experience with elegant
                ambiance, exquisite cuisine crafted from high-quality
                ingredients, and exceptional service.
              </Paragraph>
            </div>
          </div>
          <div className="flex w-1/2 flex-col gap-2">
            <div className="flex flex-col gap-2 rounded-lg border border-neutral-100 p-3">
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Restaurant Type
                </Paragraph>
                <Paragraph size="sm" className="font-semibold text-neutral-900">
                  Fine Dining
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Website
                </Paragraph>
                <Paragraph
                  size="sm"
                  className="flex items-center gap-1 font-semibold text-neutral-900"
                >
                  www.roaming-sp...
                  <button className="flex cursor-pointer items-center justify-center">
                    <IconCopy size={16} className="text-neutral-500" />
                  </button>
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Pricing (2 Persons)
                </Paragraph>
                <Paragraph size="sm" className="font-semibold text-neutral-900">
                  $150 – $200
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine
                </Paragraph>
                <div className="flex items-center gap-2">
                  <Tag variant="white">
                    <IconChefHat size={16} className="text-neutral-500" />
                    French
                  </Tag>
                  <Tag variant="white">+2</Tag>
                </div>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Ambient
                </Paragraph>
                <Paragraph size="sm" className="font-semibold text-neutral-900">
                  Sophisticated...
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Yelp
                </Paragraph>
                <Paragraph
                  size="sm"
                  className="flex items-center gap-1 font-semibold text-neutral-900"
                >
                  yelp.com/restaurant...
                  <button className="flex cursor-pointer items-center justify-center">
                    <IconCopy size={16} className="text-neutral-500" />
                  </button>
                </Paragraph>
              </div>
            </div>
          </div>
        </div>
        <ImageSlider
          className="w-1/2"
          thumbnailPosition="right"
          images={[
            "/assets/images/<EMAIL>",
            "/assets/images/<EMAIL>",
            "/assets/images/<EMAIL>",
            "/assets/images/<EMAIL>",
            "/assets/images/<EMAIL>",
          ]}
        />
      </div>
    </Card>
  );
};

export default RestaurantGeneralDetails;
