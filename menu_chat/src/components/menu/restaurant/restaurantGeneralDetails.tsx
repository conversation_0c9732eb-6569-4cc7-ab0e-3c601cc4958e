import Card from "@/components/base/card";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import Tag from "@/components/base/tag";
import ImageSlider from "@/components/base/imageSlider";
import { IconChefHat, IconCopy } from "@/components/icons";
import { useRestaurant, Image } from "@/contexts/RestaurantContext";
import { useCallback } from "react";
import titleCase from "voca/title_case";
import upperCase from "voca/upper_case";
import Link from "next/link";

const RestaurantGeneralDetails = () => {
  const {business_name, address, about, city_name, country, state_name, zip_code, restaurant_type, url, price, yelp_categories, ambient, yelp_url, images} = useRestaurant()
  const firstCategory = yelp_categories[0]
  const otherCategoryCount = yelp_categories.length > 1 ? yelp_categories.length - 1 : 0

  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text);
  }, []);

  const imagesMap = images?.map((img: Image) => {
    return img.src
  }) || []

  return (
    <Card className="p-4">
      <div className="flex items-start justify-between gap-2">
        <div className="flex w-1/2 gap-2">
          <div className="w-1/2">
            <Heading level={3} className="mb-3">
              {titleCase(business_name)}
            </Heading>
            <div className="flex flex-wrap gap-1">
              <Paragraph size="xs" className="text-neutral-700">
                {titleCase(city_name)}, {upperCase(country)}<br />
                {titleCase(address)}, {titleCase(city_name)}, {upperCase(state_name)} {zip_code}
              </Paragraph>
              <Paragraph size="xs" className="text-neutral-600">
                {about}
              </Paragraph>
            </div>
          </div>
          <div className="flex w-1/2 flex-col gap-2">
            <div className="flex flex-col gap-2 rounded-lg border border-neutral-100 p-3">
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Restaurant Type
                </Paragraph>
                <Paragraph size="sm" className="font-semibold text-neutral-900">
                  {titleCase(restaurant_type).replace('_', ' ')}
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Website
                </Paragraph>
                {url &&
                  <Paragraph
                    size="sm"
                    className="flex items-center gap-1 font-semibold text-neutral-900"
                  >
                    <Link
                      href={url}
                      className="text-xs font-medium text-neutral-600 hover:underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {url.slice(0, 20)}...
                    </Link>
                    <button
                      onClick={() => copyToClipboard(url)}
                      className="flex cursor-pointer items-center justify-center"
                    >
                      <IconCopy size={16} className="text-neutral-500" />
                    </button>
                  </Paragraph>
                }
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Pricing (2 Persons)
                </Paragraph>
                <Paragraph size="sm" className="font-semibold text-neutral-900">
                  {price}
                </Paragraph>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Cuisine
                </Paragraph>
                <div className="flex items-center gap-2">
                  {firstCategory &&
                    <Tag variant="white">
                      <IconChefHat size={16} className="text-neutral-500" />
                      {titleCase(firstCategory)}
                    </Tag>
                  }
                  {otherCategoryCount > 0 &&
                    <Tag variant="white">+{otherCategoryCount}</Tag>
                  }
                </div>
              </div>
              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Ambient
                </Paragraph>
                {ambient &&
                  <Paragraph size="sm" className="font-semibold text-neutral-900">
                    {ambient}
                  </Paragraph>
                }
              </div>


              <div className="flex items-center justify-between gap-2">
                <Paragraph className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
                  Yelp
                </Paragraph>
                {yelp_url &&
                  <Paragraph
                    size="sm"
                    className="flex items-center gap-1 font-semibold text-neutral-900"
                  >
                    <Link
                      href={yelp_url}
                      className="text-xs font-medium text-neutral-600 hover:underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {yelp_url.slice(0, 20)}...
                    </Link>
                    <button
                      onClick={() => copyToClipboard(yelp_url)}
                      className="flex cursor-pointer items-center justify-center"
                    >
                      <IconCopy size={16} className="text-neutral-500" />
                    </button>
                  </Paragraph>
                }
              </div>
            </div>
          </div>
        </div>
        <ImageSlider
          className="w-1/2"
          thumbnailPosition="right"
          images={imagesMap}
        />
      </div>
    </Card>
  );
};

export default RestaurantGeneralDetails;
