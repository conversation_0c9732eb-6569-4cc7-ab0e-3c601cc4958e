import { useMemo, useState } from "react";
import Pagination from "@/components/table/pagination";

import Tag from "@/components/base/tag";

import { useSession } from "next-auth/react";
import { EnhancedTable } from "@/components/table/EnhancedTable";
import {
  ColumnDef,
  ColumnResizeMode,
  FilterFn,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { fetchAllMenuItems } from "@/api/menu_items";
import Link from "next/link";
import titleCase from "voca/title_case";
import { getVariantForChange } from "@/components/utils/helpers";
import { IconWorkflow } from "@/components/icons";

interface MenuItemsResponse {
  guid: string;
  name: string;
  category: string;
  subcategory: string;
  cuisine_type: string[];
  menu_adoption: string;
  pen_rate: number;
  menu_mention_count: number;
  social_mentions: number;
  change: number;
  retail_change: number;
}
const AllMenuItemsTable = ({
  search,
  filters,
}: {
  search: string;
  filters: string[];
}) => {
  const { data: session } = useSession();

  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [pagination, setPagination] = useState({
    pageIndex: page - 1, //initial page index
    pageSize: perPage, //default page size
  });

  const totalResults = 100; // Example total results, replace with actual data
  const [globalFilter, setGlobalFilter] = useState("");

  // const [search] = useState<string>("");
  // const [filters] = useState<string[]>([]);
  const [columnSizing, setColumnSizing] = useState({});

  const { data: all_menu_items, isLoading } = useQuery({
    queryFn: () => {
      return fetchAllMenuItems({
        auth: session?.user.authorization as string,
        sorting,
        pagination,
        search,
        filters,
      });
    },
    queryKey: ["all_menu_items", sorting, pagination, search, filters],
    enabled: !!session?.user?.authorization,
  });

  const data = useMemo(() => {
    if (!all_menu_items?.data) return [];
    return all_menu_items.data || [];
  }, [all_menu_items]);

  const [columnResizeMode] = useState<ColumnResizeMode>("onChange");

  const columns = useMemo<ColumnDef<MenuItemsResponse>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Name",
        cell: (info) => {
          const { guid } = info.row.original;
          return (
            <Link href={`/menu-items/details/${guid}`}>
              <Tag>{titleCase(info.getValue<string>())}</Tag>
            </Link>
          );
        },
      },
      {
        header: "Category",
        accessorKey: "category",
        cell: (info) => (
          <Tag variant="white">
            <IconWorkflow size={16} className="text-neutral-500" />
            {titleCase(info.getValue<string>())}
          </Tag>
        ),
      },
      {
        header: "Subcategory",
        accessorKey: "subcategory",
        cell: (info) => (
          <Tag variant="white">
            <IconWorkflow size={16} className="text-neutral-500" />
            {titleCase(info.getValue<string>())}
          </Tag>
        ),
      },
      {
        header: "Cuisine Type",
        accessorKey: "cuisine_type",
        cell: (info) => `${titleCase(info.getValue<string>()[0])}`,
      },
      {
        header: "Menu Mentions",
        accessorKey: "menu_mention_count",
        cell: (info) => `${info.getValue<number>()}`,
      },
      {
        header: "Penetration",
        accessorKey: "pen_rate",
        cell: (info) => {
          const value = info.getValue<number>();
          const { type, icon } = getVariantForChange(value);

          return (
            <Tag variant={type}>
              {icon}
              {value?.toFixed(2) || 0.0}%
            </Tag>
          );
        },
      },
      {
        header: "Social Mentions",
        accessorKey: "social_mentions",
        cell: (info) => {
          const value = info.getValue<number>();
          const { type, icon } = getVariantForChange(value);

          return (
            <Tag
              size="xs"
              variant={type}
              className="mr-auto flex w-fit items-center"
            >
              {icon}
              {value?.toFixed(2) || 0.0}%
            </Tag>
          );
        },
      },
      {
        header: "Change",
        accessorKey: "foodservice_growth",
        cell: (info) => {
          const value = info.getValue<number>();
          const { type, icon } = getVariantForChange(value);

          return (
            <Tag
              size="xs"
              variant={type}
              className="mr-auto flex w-fit items-center"
            >
              {icon}
              {value?.toFixed(2) || 0.0}%
            </Tag>
          );
        },
      },
    ],
    [],
  );

  const fuzzyFilter: FilterFn<MenuItemsResponse> = (row, columnId, value) => {
    // If no filter value, return all rows
    if (!value || typeof value !== "string") return true;

    const rowValue = row.getValue(columnId);

    // Check if the value is a string and contains the filter value
    if (typeof rowValue === "string") {
      return rowValue.toLowerCase().includes(value.toLowerCase());
    }

    // For complex objects like menuAdoption, attempt to match against status
    if (rowValue && typeof rowValue === "object" && "status" in rowValue) {
      return (rowValue.status as string)
        .toLowerCase()
        .includes(value.toLowerCase());
    }

    // For other cases, don't filter
    return true;
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setPagination({
      pageIndex: newPage,
      pageSize: perPage,
    });
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPerPage(pageSize);
    setPagination({
      pageIndex: page,
      pageSize: pageSize,
    });
  };

  const table = useReactTable({
    data,
    columns,
    state: {
      pagination,
      sorting,
      globalFilter,
      columnSizing,
    },
    enableGlobalFilter: true,
    enableColumnResizing: true,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    manualSorting: true,
    manualPagination: true,
    manualFiltering: true,
    rowCount: all_menu_items?.total_records,
    onGlobalFilterChange: setGlobalFilter,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode,
    defaultColumn: {
      size: 150,
      minSize: 50,
      maxSize: 500,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    globalFilterFn: fuzzyFilter,
  });

  const emptyState = (
    <div className="py-6 text-center">
      <p className="text-gray-500">No ingredients found</p>
      <p className="text-sm text-gray-400">
        Try adjusting your search or filters
      </p>
    </div>
  );

  return (
    <div className="py-3">
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <EnhancedTable
          table={table}
          withBorder
          enableResizing
          emptyState={emptyState}
          isLoading={isLoading}
        />
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={totalResults}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
};

export default AllMenuItemsTable;
