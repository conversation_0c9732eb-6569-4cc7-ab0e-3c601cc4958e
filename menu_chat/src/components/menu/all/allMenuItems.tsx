import { useState } from "react";
import MenuItemsMenu from "../menuItemsMenu";
import ScrollContainer from "@/components/layout/scrollContainer";
import AllMenuItemsTable from "./allMenuItemsTable";
import MenuItemsFilters from "../menuItemsFilters";

const AllMenuItems = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(() => newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <>
      <MenuItemsMenu showFilters={false} />
      <ScrollContainer>
        <div className="mt-[60px]">
          <MenuItemsFilters
            search={search}
            onSearch={setSearch}
            filters={filters}
            onFilter={handleFilterChange}
            sort={sort}
            onSort={handleSortChange}
          />
          <AllMenuItemsTable search={search} filters={filters} />
        </div>
      </ScrollContainer>
    </>
  );
};

export default AllMenuItems;
