// import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Button from "@/components/base/button";
import Dropdown from "@/components/base/dropdown";
import { IconGeography } from "@/components/icons";
// import { IconCalendar } from "@/components/icons";
import {
  MENUITEMS_MENU,
  // MENUITEMS_FILTER
} from "@/data/menu-items";
import { useState } from "react";
// import FilterDropdown from "@/components/base/filterDropdown";

interface MenuItemsMenuProps {
  filters?: string[];
  onFilter?: (value: string[]) => void;
  showFilters?: boolean;
}

const MenuItemsMenu = (
  {
    // filters = [],
    // onFilter = () => {},
    // showFilters = true,
  }: MenuItemsMenuProps,
) => {
  // const [selectedPeriod, setSelectedPeriod] = useState<string>("Last quarter");
  const pathname = usePathname();
  const [selectedValue, setSelectedValue] = useState<string>("United States");
  // const handleSelectPeriod = (value: string) => {
  //   setSelectedPeriod(value);
  // };

  const handleSelect = (value: string) => {
    setSelectedValue(value);
  };

  return (
    <div className="bg-custom-blur absolute top-0 right-0 left-0 z-20 m-auto flex w-full items-center justify-between gap-2 px-8 py-3">
      <div className="flex items-center gap-1">
        {MENUITEMS_MENU.map((menu) => (
          <Link key={menu.slug} href={`${menu.slug}`}>
            <Button
              variant="tertiary"
              size="sm"
              className={twMerge(
                "hover:bg-muted-100 active:bg-muted-100 font-archivo rounded-lg border-0 px-4",
                menu.slug === pathname
                  ? "bg-muted-100 hover:bg-muted-100 active:bg-muted-200"
                  : "bg-transparent",
              )}
            >
              {menu.title}
            </Button>
          </Link>
        ))}
      </div>
      <Dropdown
        options={[
          { label: "United States", value: "United States" },
          { label: "United Kingdom", value: "United Kingdom" },
          { label: "France", value: "France" },
          { label: "Spain", value: "Spain" },
        ]}
        triggerLabel={selectedValue}
        selectedValue={selectedValue}
        onSelect={handleSelect}
        icon={<IconGeography size={16} />}
        withCaret
        withRadioButtons={true}
        triggerClassName="h-8"
      />
      {/*      <div className="flex items-center gap-1">
        <Dropdown
          options={[
            { label: "Last quarter", value: "Last quarter" },
            { label: "This quarter", value: "This quarter" },
            { label: "This month", value: "This month" },
          ]}
          selectedValue={selectedPeriod}
          onSelect={(e) => handleSelectPeriod(e)}
          icon={<IconCalendar size={16} />}
          withCaret
          triggerClassName="h-8"
        />
        {showFilters && (
          <FilterDropdown
            selectedValue={filters}
            options={MENUITEMS_FILTER}
            onSelect={onFilter}
          />
        )}
      </div>*/}
    </div>
  );
};

export default MenuItemsMenu;
