import Button from "@/components/base/button";
import Input from "@/components/base/input";
import { IconDownload, IconClose, IconSearch } from "@/components/icons";
import FilterDropdown from "@/components/base/filterDropdown";
import SortDropdown from "@/components/base/sortDropdown";
import { MENUITEMS_FILTER, MENUITEMS_SORT } from "@/data/menu-items";
import { Table as TanStackTable } from "@tanstack/react-table";
import { exportTableToCSV } from "@/lib/exportCsv";

interface MenuItemsFiltersProps<T> {
  search: string;
  onSearch: (value: string) => void;
  filters: string[];
  onFilter: (value: string[]) => void;
  sort: string;
  onSort: (value: string) => void;
  table?: TanStackTable<T>;
  tableName?: string;
}

const MenuItemsFilters = <T,>({
  search,
  onSearch,
  filters,
  onFilter,
  sort,
  onSort,
  table,
  tableName,
}: MenuItemsFiltersProps<T>) => {
  return (
    <>
      <div className="flex items-center justify-between gap-3 pt-3 pb-6">
        <Button
          variant="primary"
          size="sm"
          onClick={() => exportTableToCSV(table, tableName)}
        >
          <IconDownload size={20} />
          Export
        </Button>
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => onSearch(e.target.value)}
          />
          <FilterDropdown
            selectedValue={filters}
            options={MENUITEMS_FILTER}
            onSelect={onFilter}
          />
          <SortDropdown
            selectedValue={sort}
            options={MENUITEMS_SORT}
            onSelect={(value) => onSort(value)}
          />
        </div>
      </div>
      {filters.length > 0 && (
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1.5">
            {filters.map((filter) => (
              <span
                key={filter}
                className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
              >
                {filter}
                <button
                  type="button"
                  className="flex size-4 items-center justify-center"
                  onClick={() => onFilter(filters.filter((f) => f !== filter))}
                >
                  <IconClose
                    size={16}
                    className="text-muted-900 cursor-pointer"
                  />
                </button>
              </span>
            ))}
          </div>
          <Button variant="ghost" size="sm" onClick={() => onFilter([])}>
            Reset
          </Button>
        </div>
      )}
    </>
  );
};

export default MenuItemsFilters;
