import Heading from "@/components/base/heading";
import IconBinoculars from "@/components/icons/icon-binoculars";
import router from "next/router";

const SearchText = ({ onClose }: { onClose: () => void }) => {
  return (
    <div className="my-3 flex flex-col items-center justify-center px-5 py-16 text-center">
      <IconBinoculars size={32} className="text-neutral-800" />

      <Heading level={5} className="m-1 font-medium text-neutral-900">
        We couldn&apos;t find any search results.
      </Heading>
      <p className="font-worksans mb-2 text-xs font-medium text-neutral-700">
        Try text search to find more.
      </p>

      <button
        onClick={() => {
          router.push("/search/all-text-results");
          onClose();
        }}
        className="border-muted-200 font-worksans m-2 rounded-md border px-3 py-2 text-sm font-semibold tracking-[-0.3px] text-neutral-700 hover:cursor-pointer hover:bg-neutral-500/10"
      >
        Text search
      </button>
    </div>
  );
};

export default SearchText;
