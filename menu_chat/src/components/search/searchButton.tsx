import { twMerge } from "tailwind-merge";
import { IconSearch } from "@/components/icons";
import { useAppStore } from "@/store/useAppStore";

const SearchButton = () => {
  const { navCollapsed, setSearchToggle } = useAppStore();

  const handleSearchOpen = () => {
    setSearchToggle(true);
  };

  return (
    <div className="flex h-8 w-full items-center justify-center">
      <button
        onClick={handleSearchOpen}
        className={twMerge(
          "border-muted-200 flex h-8 w-full cursor-pointer items-center justify-between rounded-md border px-2 py-1.5",
          navCollapsed && "w-9 justify-center border-0 p-0",
        )}
      >
        <span
          className={twMerge(
            "text-muted-600 text-sm font-medium",
            navCollapsed && "hidden",
          )}
        >
          Search
        </span>
        <IconSearch size={20} className="text-muted-600" />
      </button>
    </div>
  );
};

export default SearchButton;
