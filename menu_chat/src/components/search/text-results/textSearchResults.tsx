import { useState } from "react";
import {
  IconLocation,
  IconRestaurant,
} from "@/components/icons";
import DataSourcesReports from "@/components/shared/dataSourcesReports";
import SearchFilters from "./searchFilters";
import SearchResultCard from "../SearchResultCard";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchSearchItems } from "@/api/search";
import { useEffect } from 'react';
import { useRouter } from "next/router";
import titleCase from "voca/title_case";


interface SearchResultRestaurant {
  business_name: string;
  city_name: string;
  state_name: string;
}

interface SearchResult {
  id: number;
  name: string;
  description: string;
  price: string;
  image_url: string;
  restaurant_id: number;
  restaurant: SearchResultRestaurant;
}

const TextSearchResults = () => {
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page] = useState(1);
  const [perPage] = useState(10);
  const router = useRouter();

  const [pagination] = useState({
    pageIndex: page - 1, //initial page index
    pageSize: perPage, //default page size
  });


  const { data: session } = useSession();
  const { data: search_results } = useQuery({
    queryFn: () => {
      return fetchSearchItems({
        auth: session?.user.authorization as string,
        query: search,
        filters: filters,
        pagination
      });
    },
    queryKey: ["search_results", search, filters, pagination],
    enabled: !!session?.user?.authorization,
  });


  const handleSearch = (value: string) => {
    setSearch(value);
    const params = new URLSearchParams(window.location.search);
    params.set('q', value);
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const qParams = params.get('q') || '';
      if (qParams !== search) {
        handleSearch(qParams);
      }
    }
  }, [search]);

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters, );
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const getRestaurantPath = (restaurantId: number) => {
    return `/menu-items/restaurant/${restaurantId}`;
  };

  const handleViewDetails = (restaurantId: number) => {
    router.push(getRestaurantPath(restaurantId));
  };


  const searchResultsMap =
    search_results?.data?.data.map((r: SearchResult) => {
      // return { title: r.name, type: r.model_type, model_guid: r.model_guid };

      const { id, name, description, price, image_url, restaurant_id } = r;
      const { business_name, city_name, state_name } = r.restaurant;
      const location = `${city_name}, ${state_name}`;

      return (
        <SearchResultCard
          key={id}
          imageSrc={image_url || "/assets/images/<EMAIL>"}
          imageAlt=""
          title={name}
          description={description}
          price={price && parseFloat(price) > 0 ? `$${price}` : ""}
          firstIcon={IconRestaurant}
          firstText={titleCase(business_name)}
          onFirstTextClick={() => handleViewDetails(restaurant_id)}
          typeIcon={IconLocation}
          typeText={titleCase(location)}
          onViewDetails={() => handleViewDetails(restaurant_id)}
        />
      );
    }) || [];

  return (
    <section className="mt-[60px] py-4">
      <SearchFilters
        search={search}
        onSearch={handleSearch}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
        totalResults={search_results?.data?.total}
      />
      <div className="flex flex-col gap-5">
        <div className="grid grid-cols-4 gap-5 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
          {searchResultsMap}
        </div>
        <DataSourcesReports />
      </div>
    </section>
  );
};

export default TextSearchResults;
