import Button from "@/components/base/button";
import Input from "@/components/base/input";
import { IconClose, IconSearch } from "@/components/icons";
import FilterDropdown from "@/components/base/filterDropdown";
import SortDropdown from "@/components/base/sortDropdown";
import Paragraph from "@/components/base/paragraph";
import { SEARCH_FILTER_STATIC, SEARCH_SORT_STATIC } from "@/data/search";

interface SearchFiltersProps {
  search: string;
  onSearch: (value: string) => void;
  filters: string[];
  onFilter: (value: string[]) => void;
  sort: string;
  onSort: (value: string) => void;
  totalResults: number;
}

const SearchFilters = ({
  search,
  onSearch,
  filters,
  onFilter,
  sort,
  onSort,
  totalResults,
}: SearchFiltersProps) => {
  return (
    <>
      <div className="flex items-center justify-between gap-3 pt-3 pb-6">
        <Paragraph size="sm" className="font-medium text-neutral-900">
          {totalResults} Results
        </Paragraph>
        <div className="flex items-center gap-3">
          <Input
            value={search}
            placeholder="Search"
            inputSize="sm"
            className="w-52"
            icon={<IconSearch size={20} />}
            onChange={(e) => onSearch(e.target.value)}
          />
          <FilterDropdown
            selectedValue={filters}
            options={SEARCH_FILTER_STATIC}
            onSelect={onFilter}
          />
          <SortDropdown
            selectedValue={sort}
            options={SEARCH_SORT_STATIC}
            onSelect={(value) => onSort(value)}
          />
        </div>
      </div>
      {filters.length > 0 && (
        <div className="mb-3 flex items-center justify-between gap-2">
          <div className="flex items-center gap-1.5">
            {filters.map((filter) => (
              <span
                key={filter}
                className="bg-muted-100 inline-flex h-5.5 items-center gap-1 rounded-md px-1.5 text-xs font-medium tracking-[-0.3px] text-neutral-900"
              >
                {filter}
                <button
                  type="button"
                  className="flex size-4 items-center justify-center"
                  onClick={() => onFilter(filters.filter((f) => f !== filter))}
                >
                  <IconClose
                    size={16}
                    className="text-muted-900 cursor-pointer"
                  />
                </button>
              </span>
            ))}
          </div>
          <Button variant="ghost" size="sm" onClick={() => onFilter([])}>
            Reset
          </Button>
        </div>
      )}
    </>
  );
};

export default SearchFilters;
