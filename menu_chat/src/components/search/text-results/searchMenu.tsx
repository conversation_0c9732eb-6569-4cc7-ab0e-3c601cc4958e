import { twMerge } from "tailwind-merge";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Button from "@/components/base/button";
import { SEARCH_MENU } from "@/data/search";

const SearchMenu = () => {
  const pathname = usePathname();

  return (
    <div className="bg-custom-blur absolute top-0 right-0 left-0 z-20 m-auto flex w-full items-center justify-between gap-2 px-8 py-3">
      <div className="flex items-center gap-1">
        {SEARCH_MENU.map((search) => (
          <Link key={search.slug} href={`${search.slug}`}>
            <Button
              variant="tertiary"
              size="sm"
              className={twMerge(
                "hover:bg-muted-100 active:bg-muted-100 font-archivo rounded-lg border-0 px-4",
                search.slug === pathname
                  ? "bg-muted-100 hover:bg-muted-100 active:bg-muted-200"
                  : "bg-transparent",
              )}
            >
              {search.title}
            </Button>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default SearchMenu;
