import { useState, useEffect, useMemo } from "react";
import { IconCircleUpright } from "@/components/icons";
import Pagination from "@/components/table/pagination";
import Tag from "@/components/base/tag";
import AllSearchFilters from "./allSearchFilters";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchFullSearch } from "@/api/search";
import {
  ColumnDef,
  ColumnResizeMode,
  FilterFn,
  getCoreRowModel, getPaginationRowModel,
  getSortedRowModel, SortingState,
  useReactTable
} from "@tanstack/react-table";
import titleCase from "voca/title_case";
import { EnhancedTable } from "@/components/table/EnhancedTable";


type SortingUpdateFn = SortingState | ((prev: SortingState) => SortingState);

interface FullSearchResult {
  id: number,
  recent_search_id: number,
  model_guid: number,
  model_type: string,
  cover_url: string,
  name: string,
  category: string,
  created_at: string,
  updated_at: string
}

const generateLink = (item: FullSearchResult) => {
  switch(item.model_type){
    case "Ingredient": {
      return `/ingredients/details/${item.model_guid}`
    }
    case "MenuItem": {
      return `/menu-items/details/${item.model_guid}`
    }
    case "RetailProduct": {
      return `/retail/details/${item.model_guid}`
    }
    default: {
      return ""
    }
  }
}



const AllSearchResults = () => {
  const router = useRouter();
  const [search, setSearch] = useState<string>("");

  useEffect(() => {
    if (!router.isReady) return;
    const query = router.query.q as string
    if (query) {
      setSearch(query);
    }
  }, [router.isReady, router.query.q]);

  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  const [pagination, setPagination] = useState({
    pageIndex: page - 1, //initial page index
    pageSize: perPage, //default page size
  });

  const totalResults = 100;

  const [sorting, setSorting] = useState<SortingState>([]);


  const { data: session } = useSession();
  const { data: full_search_results, isLoading }  = useQuery({
    queryFn: () => {
      return fetchFullSearch({auth: session?.user.authorization as string, pagination, sorting, query: search, filters: filters})
    },
    queryKey: ["full_search_results", search, filters, pagination, sorting],
    enabled: !!session?.user?.authorization,
  });


  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setPagination({
      pageIndex: newPage - 1,
      pageSize: perPage,
    })
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPerPage(pageSize);
    setPagination({
      pageIndex: page - 1,
      pageSize: pageSize,
    })
  };

  const handleSearchChange = (newSearch: string) => {
    setPage(1);
    setPagination({
      pageIndex: 0,
      pageSize: perPage,
    })
    setSearch(newSearch);
  }

  const data = useMemo(() => {
    if (!full_search_results?.data) return [];
    return full_search_results.data.data || [];
  }, [full_search_results]);


  const [columnResizeMode] = useState<ColumnResizeMode>('onChange');


  const columns = useMemo<({ accessorKey: string } & ColumnDef<FullSearchResult>)[]>(
    () => [
      {
        accessorKey: 'name',
        header: 'Name',
        cell: info => {
          const {cover_url} = info.row.original
          const name = titleCase(info.getValue<string>())
          return (
            <div className="flex items-center gap-2">
              {cover_url ?
                <Image
                  src={cover_url}
                  alt={name}
                  width={40}
                  height={40}
                  className="h-10 w-10 rounded object-cover object-center"
                /> : <Image
                  src={"/assets/images/placeholder_icon.png"}
                  alt={""}
                  width={40}
                  height={40}
                  className="h-10 w-10 rounded object-cover object-center"
                />
              }
              <span>{name}</span>
            </div>
          )
        },
      },
      {
        accessorKey: 'model_type',
        header: 'Type',
        cell: info => {
          return (
            <Tag variant="white">{titleCase(info.getValue<string>())}</Tag>
          )
        },
      },
      {
        accessorKey: 'category',
        header: 'Category',
        cell: info => {
          return (
            titleCase(info.getValue<string>())
          )
        },
      },
      {
        accessorKey: 'items_count',
        header: 'On Mentions / Shelves',
      },
      {
        accessorKey: 'id',
        header: 'Link',
        cell: info => {
          const link = generateLink(info.row.original)
          return (
            <Link href={link} className="inline-flex items-center gap-1">
              View{" "}
              <IconCircleUpright size={16} className="text-yellow-800" />
            </Link>
          )
        },
      }],
    []
  );

  const fuzzyFilter: FilterFn<FullSearchResult> = (row, columnId, value) => {
    // If no filter value, return all rows
    if (!value || typeof value !== 'string') return true;

    const rowValue = row.getValue(columnId);

    // Check if the value is a string and contains the filter value
    if (typeof rowValue === 'string') {
      return rowValue.toLowerCase().includes(value.toLowerCase());
    }

    // For complex objects like menuAdoption, attempt to match against status
    if (rowValue && typeof rowValue === 'object' && 'status' in rowValue) {
      return (rowValue.status as string).toLowerCase().includes(value.toLowerCase());
    }

    // For other cases, don't filter
    return true;
  };

  const emptyState = (
    <div className="text-center py-6">
      <p className="text-gray-500">No ingredients found</p>
      <p className="text-sm text-gray-400">Try adjusting your search or filters</p>
    </div>
  );

  const [globalFilter, setGlobalFilter] = useState("");
  const [columnSizing, setColumnSizing] = useState({});

  const handleSortingChange = (newSorting: SortingUpdateFn) => {
    setPage(1)
    setPagination({
      pageIndex: 0,
      pageSize: perPage,
    })
    setSorting(newSorting)
  }

  const table = useReactTable({
    data,
    columns,
    state: {
      pagination,
      sorting,
      globalFilter,
      columnSizing,
    },
    enableGlobalFilter: true,
    enableColumnResizing: true,
    onSortingChange: handleSortingChange,
    onPaginationChange: setPagination,
    manualSorting: true,
    manualPagination: true,
    manualFiltering: true,
    rowCount: totalResults,
    onGlobalFilterChange: setGlobalFilter,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode,
    defaultColumn: {
      size: 150,
      minSize: 50,
      maxSize: 500,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    globalFilterFn: fuzzyFilter,
  });

  return (
    <div className="pt-3 pb-7">
      <AllSearchFilters
        search={search}
        onSearch={handleSearchChange}
        filters={filters}
        onFilter={handleFilterChange}
        sort={sort}
        onSort={handleSortChange}
      />
      <div className="border-muted-100 rounded-lg border bg-white p-1.5">
        <EnhancedTable
          table={table}
          withBorder
          enableResizing
          emptyState={emptyState}
          isLoading={isLoading}
        />
        <Pagination
          currentPage={page}
          perPage={perPage}
          totalResults={full_search_results?.data?.total_records}
          onPageChange={handlePageChange}
          onPerPageChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
};

export default AllSearchResults;
