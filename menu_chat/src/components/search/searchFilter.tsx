import { twMerge } from "tailwind-merge";
import Button from "@/components/base/button";

interface SearchFilterProps {
  filter: string;
  handleFilter: (filterValue: string) => void;
}

const SearchFilter = ({ filter, handleFilter }: SearchFilterProps) => {
  const filterOptions = [
    { value: "Ingredient", label: "Ingredient" },
    { value: "MenuItem", label: "Menu Item" },
    { value: "RetailProduct", label: "Retail" },
  ];

  return (
    <div className="flex items-center gap-3 px-5 py-2">
      <span className="text-xs font-medium tracking-tight text-neutral-700">
        Filter by
      </span>
      {filterOptions.map(({ value, label }) => (
        <Button
          key={value}
          variant="tertiary"
          size="xs"
          className={twMerge(
            "hover:bg-muted-100 active:bg-muted-100 focus:bg-muted-100 rounded-md",
            filter === value && "bg-muted-100 border-muted-100",
          )}
          onClick={() => handleFilter(value)}
        >
          {label}
        </Button>
      ))}
    </div>
  );
};

export default SearchFilter;
