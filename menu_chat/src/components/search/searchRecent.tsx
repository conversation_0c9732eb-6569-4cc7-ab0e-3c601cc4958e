import Tag from "@/components/base/tag";
import Heading from "@/components/base/heading";

interface RecentItem {
  title: string;
  type: string;
  model_guid?: string;

}

interface SearchRecentProps {
  recent: RecentItem[];
  handleRecentClick: (searchTerm: string) => void;
}


const SearchRecent = ({ recent, handleRecentClick }: SearchRecentProps) => {
  return (
    <div className="px-5 py-4">
      <Heading level={6} className="font-worksans py-2">
        Recent results
      </Heading>
      <div className="max-h-60 overflow-y-auto">
        {recent.map((item, index) => {
          return (
            <div
              key={index}
              className="flex cursor-pointer items-center justify-between py-2"
              onClick={() => {
                handleRecentClick(item.title)
              }}
            >
              <Tag>{item.title}</Tag>
              <span className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
              {item.type}
            </span>
            </div>
          )
        } )}
      </div>
    </div>
  );
};

export default SearchRecent;
