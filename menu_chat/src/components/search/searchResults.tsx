import { useEffect } from "react";
import Tag from "@/components/base/tag";
import Heading from "@/components/base/heading";
import Paragraph from "@/components/base/paragraph";
import { useRouter } from "next/router";
import Link from "next/link";
import titleCase from "voca/title_case";


interface ResultItem {
  title: string;
  type: string;
  model_guid?: string;
}

interface SearchResultsProps {
  searchTerm: string;
  filter: string;
  results: ResultItem[];
  onClose: () => void;
}

function decodeHtml(html: string) {
  const txt = document.createElement("textarea");
  txt.innerHTML = html;
  return txt.value;
}

const generateLink = (item: ResultItem) => {
  switch(item.type){
    case "Item": {
      return `/menu-items/restaurant/${item.model_guid}`
    }
    case "Ingredient": {
      return `/ingredients/details/${item.model_guid}`
    }
    case "MenuItem": {
      return `/menu-items/details/${item.model_guid}`
    }
    case "RetailProduct": {
      return `/retail/details/${item.model_guid}`
    }
    default: {
      return ""
    }
  }
}

// Highlight matching text in search results
// const highlightMatch = (text: string, term: string): React.ReactNode[] => {
//   if (!term) return [text];
//   const regex = new RegExp(`(${term})`, "gi");
//   const parts = text.split(regex);
//   return parts.map((part, i) =>
//     regex.test(part) ? (
//       <span key={i} className="bg-yellow-300">
//         {part}
//       </span>
//     ) : (
//       <span key={i}>{part}</span>
//     ),
//   );
// };

const navigateToLink = (link: string) => {
  window.location.href = link;
}


const SearchResults = ({
  searchTerm,
  results,
  onClose,
}: SearchResultsProps) => {
  const router = useRouter();

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === "Enter") {
        router.push(`/search/all-text-results?q=${searchTerm}`);
        onClose();
      }
    };
    document.addEventListener("keydown", handleKeyPress);
    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [router, onClose, searchTerm]);

  return (
    <div className="px-5 py-4">
      <Heading level={6} className="font-worksans py-2">
        Results
      </Heading>
      <div className="max-h-60 overflow-y-auto">
        {results.map((item, index) => {
          const link = generateLink(item);
          const customNav = () => {
            navigateToLink(link)
            onClose()
          }
          return (
           <div key={index} className="flex items-center justify-between py-2">
             <Link href={link} onClick={customNav}>
               <Tag className="gap-0">
                 {titleCase(decodeHtml(item.title))}
                 {/*{highlightMatch(decodeHtml(item.title), searchTerm)}*/}
               </Tag>
             </Link>

             <span className="font-archivo text-[11px] font-semibold text-neutral-600 uppercase">
              {item.type}
            </span>
           </div>
         )
        })}
      </div>
      <Link
        href="/search-results"
        className="-mx-5 block px-5 py-2 transition-colors duration-200 hover:bg-neutral-100"
      >
        <Paragraph size="xs" className="font-medium text-neutral-700">
          Press{" "}
          <span className="inline-block rounded-sm bg-neutral-200 px-1 py-0.5">
            enter
          </span>{" "}
          for all results
        </Paragraph>
      </Link>
    </div>
  );
};

export default SearchResults;
