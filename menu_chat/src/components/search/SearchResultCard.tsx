import React from "react";
import { SafeImage } from "@/components/utils/helpers";
import Card from "@/components/base/card";
import Tag from "@/components/base/tag";
import Paragraph from "@/components/base/paragraph";
import Button from "@/components/base/button";
import {
  IconCategory,
  IconCircleUpright,
  IconIngredients,
} from "@/components/icons";

export interface SearchResultCardProps {
  // Image props
  imageSrc: string;
  imageAlt: string;

  // Content props
  title: string;
  description: string;
  price?: string;

  // Location/Category props
  firstIcon: typeof IconCategory;
  firstText: string;

  // Type props
  typeIcon: typeof IconIngredients;
  typeText: string;

  // Action props
  onViewDetails?: () => void;
  onFirstTextClick?: () => void;

  // Optional className for additional styling
  className?: string;
}

const SearchResultCard: React.FC<SearchResultCardProps> = ({
  imageSrc,
  imageAlt,
  title,
  description,
  price,
  firstIcon: FirstIcon,
  firstText,
  typeIcon: TypeIcon,
  typeText,
  onViewDetails,
  onFirstTextClick,
  className = "",
}) => {
  return (
    <Card className={`p-3 ${className}`}>
      <SafeImage
        src={imageSrc}
        alt={imageAlt}
        width={100}
        height={232}
        className="mb-1 h-[140px] w-full rounded-lg object-cover"
        fallback="/assets/images/<EMAIL>"
      />
      <div className="border-muted-200 mb-2 rounded-lg border p-2">
        <div className="mb-2 flex min-h-7 items-center justify-between gap-2">
          <Tag className="max-w-[65%]">{title}</Tag>
          {price && (
            <Paragraph size="lg" className="font-semibold">
              {price}
            </Paragraph>
          )}
        </div>
        <Paragraph size="xs" className="truncate font-medium text-neutral-600">
          {description}
        </Paragraph>
      </div>
      <Paragraph
        size="sm"
        className="mb-1 flex w-full items-center gap-2 truncate font-medium"
      >
        <Button
          className="group gap-2 p-2 font-medium"
          variant="ghost"
          size="xs"
          onClick={onFirstTextClick}
        >
          <FirstIcon size={16} className="text-neutral-500" />
          {firstText}
        </Button>
      </Paragraph>
      <div className="flex w-full flex-col">
        <div className="flex items-center justify-between gap-2">
          <Paragraph
            size="sm"
            className="mb-1 flex items-center gap-2 px-2 font-medium"
          >
            <TypeIcon size={16} className="text-neutral-500" />
            {typeText}
          </Paragraph>
          <Button
            className="group gap-0 p-1 hover:gap-1"
            variant="secondary"
            size="xs"
            onClick={onViewDetails}
          >
            {/* Text (hidden until hover) */}
            <span className="max-w-0 overflow-hidden text-neutral-700 opacity-0 group-hover:max-w-xs group-hover:opacity-100">
              View Details
            </span>
            {/* Icon */}
            <IconCircleUpright
              size={16}
              className="cursor-pointer text-yellow-800"
            />
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default SearchResultCard;
