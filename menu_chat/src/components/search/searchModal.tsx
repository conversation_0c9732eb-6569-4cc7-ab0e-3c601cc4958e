import { useRef, useEffect, useState } from "react";
import { useAppStore } from "@/store/useAppStore";
import { motion, AnimatePresence } from "motion/react";
import SearchInput from "./searchInput";
import SearchFilter from "./searchFilter";
import SearchResults from "./searchResults";
import { useQuery } from "@tanstack/react-query";
import { fetchSearch } from "@/api/search";
import { useSession } from "next-auth/react";
import SearchText from "./searchText";
//import SearchEmpty from "./searchEmpty";

interface SearchResult {
  name: string;
  model_guid: number;
  model_type: string;
}

const SearchModal = () => {
  const { searchToggle, setSearchToggle } = useAppStore();
  const inputRef = useRef<HTMLInputElement>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filter, setFilter] = useState<string>("");

  const { data: session } = useSession();
  const { data: search_results } = useQuery({
    queryFn: () => {
      return fetchSearch({
        auth: session?.user.authorization as string,
        query: searchTerm,
        filter: filter,
      });
    },
    queryKey: ["search_results", searchTerm, filter],
    enabled: !!session?.user?.authorization,
  });

  const searchResultsMap =
    search_results?.data.map((r: SearchResult) => {
      return { title: r.name, type: r.model_type, model_guid: r.model_guid };
    }) || [];

  const handleClose = () => {
    setSearchToggle(false);
  };

  const handleFilter = (filterValue: string) => {
    setFilter(filterValue);
  };

  const handleClear = () => {
    setSearchTerm("");
    setFilter("");
  };

  // Auto-focus the input when the modal opens
  useEffect(() => {
    if (searchToggle && inputRef.current) {
      // Small delay to ensure the modal animation has started
      const timer = setTimeout(() => {
        inputRef.current?.focus();
      }, 100);

      return () => {
        clearTimeout(timer);
        handleClear();
      };
    }
  }, [searchToggle]);

  return (
    <AnimatePresence>
      {searchToggle && (
        <motion.div
          className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center p-4 backdrop-blur-sm"
          style={{
            backgroundImage:
              "radial-gradient(5.17% 38.97% at 83.06% 108.23%, rgba(0,0,0,0) 0%, rgba(0,0,0,0.2) 100%)",
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <div
            className="w-full max-w-[800px] rounded-lg bg-neutral-50 shadow-[0px_1px_3px_rgba(33,31,28,0.08),_0px_1px_2px_rgba(33,31,28,0.06)]"
            onClick={(e) => e.stopPropagation()}
          >
            <SearchInput
              ref={inputRef}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
            <SearchFilter filter={filter} handleFilter={handleFilter} />
            {searchTerm === "" && !filter ? (
              <SearchText onClose={handleClose} />
            ) : (
              <SearchResults
                searchTerm={searchTerm}
                filter={filter}
                results={searchResultsMap}
                onClose={handleClose}
              />
              // add logic for empty state
              // <SearchEmpty />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SearchModal;
