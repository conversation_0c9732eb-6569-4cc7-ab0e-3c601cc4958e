import React from "react";
import Input from "@/components/base/input";
import { IconSearch } from "@/components/icons";

interface SearchInputProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ searchTerm, setSearchTerm }, ref) => {
    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      setSearchTerm(value);
      console.log("Search term:", value);
    };

    return (
      <div className="p-1">
        <Input
          ref={ref}
          value={searchTerm}
          placeholder="Search anything ..."
          onChange={handleInputChange}
          icon={<IconSearch size={20} />}
          inputSize="lg"
          iconPosition="left"
        />
      </div>
    );
  },
);

SearchInput.displayName = "SearchInput";

export default SearchInput;
