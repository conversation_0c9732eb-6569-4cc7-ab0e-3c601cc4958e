        <Table>
          <Thead>
            <Tr>
              <Th>Product</Th>
              <Th>Type</Th>
              <Th>Category</Th>
              <Th>On Mentions/Shelves</Th>
              <Th>Link</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Fries"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Fries</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Ingredient</Tag>
              </Td>
              <Td>Frozen Foods</Td>
              <Td>11.462 restaurants</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Pommes Frites"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Pommes Frites</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Menu Item</Tag>
              </Td>
              <Td>Side</Td>
              <Td>135.245 restaurants</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Frozen Fries"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Frozen Fries</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Retail</Tag>
              </Td>
              <Td>Frozen</Td>
              <Td>25 SKUs</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Fries"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Fries</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Ingredient</Tag>
              </Td>
              <Td>Frozen Foods</Td>
              <Td>11.462 restaurants</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Pommes Frites"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Pommes Frites</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Menu Item</Tag>
              </Td>
              <Td>Side</Td>
              <Td>135.245 restaurants</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Fries"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Fries</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Ingredient</Tag>
              </Td>
              <Td>Frozen Foods</Td>
              <Td>11.462 restaurants</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Pommes Frites"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Pommes Frites</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Menu Item</Tag>
              </Td>
              <Td>Side</Td>
              <Td>135.245 restaurants</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Frozen Fries"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Frozen Fries</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Retail</Tag>
              </Td>
              <Td>Frozen</Td>
              <Td>25 SKUs</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Fries"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Fries</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Ingredient</Tag>
              </Td>
              <Td>Frozen Foods</Td>
              <Td>11.462 restaurants</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
            <Tr>
              <Td>
                <div className="flex items-center gap-2">
                  <Image
                    src="/assets/images/<EMAIL>"
                    alt="Pommes Frites"
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded object-cover object-center"
                  />
                  <span>Pommes Frites</span>
                </div>
              </Td>
              <Td>
                <Tag variant="white">Menu Item</Tag>
              </Td>
              <Td>Side</Td>
              <Td>135.245 restaurants</Td>
              <Td>
                <Link href="/" className="inline-flex items-center gap-1">
                  View{" "}
                  <IconCircleUpright size={16} className="text-yellow-800" />
                </Link>
              </Td>
            </Tr>
          </Tbody>
        </Table>