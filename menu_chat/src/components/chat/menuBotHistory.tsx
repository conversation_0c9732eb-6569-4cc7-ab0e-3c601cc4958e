import { IconSearch, IconCircleUpright } from "@/components/icons";
import { AnimatePresence, motion } from "motion/react";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchChats } from "@/api/chats";

interface MenuBotHistory {
  hide: boolean;
  onSelect: (value: string) => void;
  onSubmit: (value: string) => void;
}

interface Chat {
  guid: string;
  topic: string;
}

const SkeletonSuggestion = () => (
  <div className="group border-muted-100 flex w-full items-center justify-between rounded-lg border bg-white p-2.5">
    <div className="flex items-center gap-1">
      <div className="bg-muted-200 relative h-4 w-4 overflow-hidden rounded-full">
        <div className="animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent" />
      </div>
      <div className="bg-muted-200 relative h-4 w-48 overflow-hidden rounded">
        <div className="animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent" />
      </div>
    </div>
    <div className="bg-muted-200 relative h-5 w-5 overflow-hidden rounded-full">
      <div className="animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent" />
    </div>
  </div>
);

const MenuBotHistory = ({ hide, onSelect, onSubmit }: MenuBotHistory) => {
  const { data: session } = useSession();

  const { data: chats, isLoading } = useQuery({
    queryFn: () => fetchChats(session?.user.authorization as string),
    queryKey: ["chats"],
    enabled: !!session?.user?.authorization,
  });

  const chatsMap = chats?.data.filter((c: Chat) => c.topic).slice(-4);

  const handleSelect = (topic: string) => {
    onSelect(topic);
    // Add a small delay before submitting to show the question in the text area
    setTimeout(() => {
      onSubmit(topic);
    }, 300);
  };

  return (
    <AnimatePresence initial={false} mode="wait">
      {!hide && (
        <motion.div
          layout
          key="question-suggestions"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="mt-9 mb-8 flex flex-col gap-1"
        >
          {isLoading || !chatsMap ? (
            <>
              <SkeletonSuggestion />
              <SkeletonSuggestion />
              <SkeletonSuggestion />
              <SkeletonSuggestion />
            </>
          ) : (
            chatsMap.map((item: Chat) => (
              <div
                key={item.guid}
                className="group border-muted-100 hover:bg-muted-100 hover:border-muted-200 flex w-full cursor-pointer items-center justify-between rounded-lg border bg-white p-2.5 transition-colors"
                onClick={() => item.topic && handleSelect(item.topic)}
              >
                <div className="flex items-center gap-1">
                  <IconSearch
                    size={16}
                    className="text-neutral-500 transition-colors"
                  />
                  <span className="text-sm font-medium text-neutral-700 transition-colors group-hover:text-neutral-900">
                    {item.topic}
                  </span>
                </div>
                <IconCircleUpright
                  size={20}
                  className="text-yellow-800 opacity-0 transition-opacity group-hover:opacity-100"
                />
              </div>
            ))
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MenuBotHistory;
