import { useState } from "react";
import { twMerge } from "tailwind-merge";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useMutation } from "@tanstack/react-query";
import MenuBotLogo from "./menuBotLogo";
import MenuBotInputSuggestions from "./menuBotInputSuggestions";
import MenuBotInput from "./menuBotInput";
import { createChat } from "@/api/chats";
import { generatePresentation, PresentationRequest } from "@/api/presentation";
import Button from "../base/button";
import {
  analyzeQuestions,
  compareQuestions,
  pricingQuestions,
  salesQuestions,
  innovateQuestions,
  defaultQuestions,
  IngredientQuestion,
  getQuestions,
} from "@/data/ingredients";
import {
  IconAnalyze,
  IconCompare,
  IconInnovate,
  IconPricing,
  IconSales,
} from "../icons";

// Define an error type for better type safety
interface ApiError {
  response?: {
    status?: number;
  };
  message?: string;
}

const MenuBotPanel = () => {
  const router = useRouter();
  const [question, setQuestion] = useState<string>("");
  const [inputActive, setInputActive] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentQuestions, setCurrentQuestions] = useState<
    IngredientQuestion[]
  >([]);
  const { data: session } = useSession();

  // Create chat mutation
  const chatMutation = useMutation({
    mutationFn: ({
      authorization,
      message,
    }: {
      authorization: string;
      message: string;
    }) => createChat(authorization, message),
    onSuccess: (response) => {
      if (response && response.data) {
        const { guid } = response.data;
        router.push(`/answer/${guid}`);
      }
    },
    onError: (error: ApiError) => {
      console.error("Error creating chat:", error);
      setIsLoading(false);
      if (error.response?.status === 500) {
        alert(
          "Server error while creating chat. Please try again in a few moments.",
        );
      } else if (error.response?.status === 401) {
        alert("Please sign in again.");
      } else if (error.message) {
        alert(`Error: ${error.message}`);
      } else {
        alert("Failed to create chat. Please try again.");
      }
    },
  });

  const handleQuestionChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    const newQuestion = event.target.value;
    setQuestion(newQuestion);
    setInputActive(true);

    // Get ingredient questions
    const ingredientQuestions = getQuestions(newQuestion);

    // Only set default questions if there are no ingredient questions
    if (ingredientQuestions.length === 0 && newQuestion.length > 0) {
      setCurrentQuestions(defaultQuestions);
    } else {
      setCurrentQuestions(ingredientQuestions);
    }
  };

  const handleSendMessage = (message?: string) => {
    const messageToSend = message || question;
    if (!messageToSend || !messageToSend.trim()) return;

    if (!session?.user?.authorization) {
      alert("Please sign in.");
      return;
    }

    try {
      setIsLoading(true);
      chatMutation.mutate({
        authorization: session.user.authorization,
        message: messageToSend.trim(),
      });
    } catch (error) {
      console.error("Error in handleSendMessage:", error);
      setIsLoading(false);
    }
  };

  const handleSelectSuggestion = (value: string) => {
    setQuestion(value);
    //  setInputActive(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleGenerateReport = async () => {
    if (!question || !question.trim()) {
      alert("Please enter a prompt for the presentation.");
      return;
    }

    try {
      setIsLoading(true);

      const presentationData: PresentationRequest = {
        prompt: question.trim(),
        n_slides: 5,
        language: "English",
        template: "general",
        export_as: "pptx"
      };

      const result = await generatePresentation(presentationData);

      if (result.success) {
        console.log("Presentation generated successfully:", result.data);
        // You can add additional success handling here
        // For example, show a success message or download the file
        alert("Presentation generated successfully!");
      } else {
        console.error("Presentation generation failed:", result.error);
        alert(`Failed to generate presentation: ${result.error}`);
      }
    } catch (error) {
      console.error("Error in handleGenerateReport:", error);
      alert("An unexpected error occurred while generating the presentation.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategoryClick = (category: string) => {
    setInputActive(true);
    switch (category) {
      case "analyze":
        setCurrentQuestions(analyzeQuestions);
        setQuestion("Analyze ");
        break;
      case "compare":
        setCurrentQuestions(compareQuestions);
        setQuestion("Compare ");
        break;
      case "pricing":
        setCurrentQuestions(pricingQuestions);
        setQuestion("What is ");
        break;
      case "sales":
        setCurrentQuestions(salesQuestions);
        setQuestion("What are ");
        break;
      case "innovate":
        setCurrentQuestions(innovateQuestions);
        setQuestion("Identify ");
        break;
      default:
        setCurrentQuestions([]);
    }
  };

  const handleSuggestionsClose = () => {
    setCurrentQuestions([]);
    setInputActive(false);
  };

  return (
    <div
      className={twMerge(
        "mx-auto w-[670px] transition-[max-width] delay-400 duration-200 ease-in-out",
      )}
    >
      <MenuBotLogo hide={false} />
      <div className="relative mb-5">
        <MenuBotInput
          question={question}
          onQuestionChange={handleQuestionChange}
          onSendMessage={handleSendMessage}
          onKeyDown={handleKeyDown}
          isLoading={isLoading}
        />
        <MenuBotInputSuggestions
          question={question}
          onSelect={handleSelectSuggestion}
          onSubmit={handleSendMessage}
          suggestions={currentQuestions}
          onClose={handleSuggestionsClose}
          hasInteracted={inputActive}
        />
      </div>
      <div className="mb-5">
        <div
          className={twMerge(
            "flex items-center justify-center gap-3 transition-all duration-300 ease-in-out",
            inputActive
              ? "pointer-events-none translate-y-2 opacity-0"
              : "translate-y-0 opacity-100",
          )}
        >
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("analyze")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconAnalyze size={20} className="text-yellow-800" />
            Analyze
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("compare")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconCompare size={20} className="text-yellow-800" />
            Compare
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("pricing")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconPricing size={20} className="text-yellow-800" />
            Pricing
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("sales")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconSales size={20} className="text-yellow-800" />
            Sales
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("innovate")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconInnovate size={20} className="text-yellow-800" />
            Innovate
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleGenerateReport()}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconInnovate size={20} className="text-yellow-800" />
            Generate
          </Button>
        </div>
      </div>
      {/* <MenuBotHistory
        hide={inputActive}
        onSelect={handleSelectSuggestion}
        onSubmit={handleSendMessage}
      /> */}
      {/* <DataSourcesReports hide={inputActive} count={2} /> */}
    </div>
  );
};

export default MenuBotPanel;
