import { useState } from "react";
import { twMerge } from "tailwind-merge";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useMutation } from "@tanstack/react-query";
import MenuBotLogo from "./menuBotLogo";
import MenuBotInputSuggestions from "./menuBotInputSuggestions";
import MenuBotInput from "./menuBotInput";
import { createChat } from "@/api/chats";
import {
  createPresentationFromJson,
  downloadPresentation,
  CreateFromJsonRequest,
  PresentationData,
} from "@/api/presentation";
import Button from "../base/button";
import {
  analyzeQuestions,
  compareQuestions,
  pricingQuestions,
  salesQuestions,
  innovateQuestions,
  defaultQuestions,
  IngredientQuestion,
  getQuestions,
} from "@/data/ingredients";
import {
  IconAnalyze,
  IconCompare,
  IconInnovate,
  IconPricing,
  IconSales,
} from "../icons";
import PreviousConversations from "./previousConversations";

// Define an error type for better type safety
interface ApiError {
  response?: {
    status?: number;
  };
  message?: string;
}

const MenuBotPanel = () => {
  const router = useRouter();
  const [question, setQuestion] = useState<string>("");
  const [inputActive, setInputActive] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentQuestions, setCurrentQuestions] = useState<
    IngredientQuestion[]
  >([]);
  const [lastGeneratedPresentation, setLastGeneratedPresentation] =
    useState<PresentationData | null>(null);
  const { data: session } = useSession();

  // Create chat mutation
  const chatMutation = useMutation({
    mutationFn: ({
      authorization,
      message,
    }: {
      authorization: string;
      message: string;
    }) => createChat(authorization, message),
    onSuccess: (response) => {
      if (response && response.data) {
        const { guid } = response.data;
        router.push(`/answer/${guid}`);
      }
    },
    onError: (error: ApiError) => {
      console.error("Error creating chat:", error);
      setIsLoading(false);
      if (error.response?.status === 500) {
        alert(
          "Server error while creating chat. Please try again in a few moments.",
        );
      } else if (error.response?.status === 401) {
        alert("Please sign in again.");
      } else if (error.message) {
        alert(`Error: ${error.message}`);
      } else {
        alert("Failed to create chat. Please try again.");
      }
    },
  });

  const handleQuestionChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    const newQuestion = event.target.value;
    setQuestion(newQuestion);
    setInputActive(true);

    // Get ingredient questions
    const ingredientQuestions = getQuestions(newQuestion);

    // Only set default questions if there are no ingredient questions
    if (ingredientQuestions.length === 0 && newQuestion.length > 0) {
      setCurrentQuestions(defaultQuestions);
    } else {
      setCurrentQuestions(ingredientQuestions);
    }
  };

  const handleSendMessage = (message?: string) => {
    const messageToSend = message || question;
    if (!messageToSend || !messageToSend.trim()) return;

    if (!session?.user?.authorization) {
      alert("Please sign in.");
      return;
    }

    try {
      setIsLoading(true);
      chatMutation.mutate({
        authorization: session.user.authorization,
        message: messageToSend.trim(),
      });
    } catch (error) {
      console.error("Error in handleSendMessage:", error);
      setIsLoading(false);
    }
  };

  const handleSelectSuggestion = (value: string) => {
    setQuestion(value);
    //  setInputActive(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleGenerateReport = async () => {
    try {
      setIsLoading(true);

      // Use the test payload structure from the documentation
      const presentationData: CreateFromJsonRequest = {
        prompt: `

Slide 1:

Title text: ‘Yogurt’ holds significant market presence in Foodservice with 14.85% penetration, while ‘Greek Yogurt’ showing strong traction as well

Table:
      
`,
        n_slides: 1,
        language: "English",
        template: "menudata-reports",
        export_as: "pptx",
      };

      // Create presentation using the new single endpoint
      const result = await createPresentationFromJson(presentationData);

      if (result.success && result.data) {
        console.log("Presentation created successfully:", result.data);

        // Store the presentation data for future reference
        setLastGeneratedPresentation(result.data);

        // Step 2: Edit the presentation to reinforce title and text for slides 1 and 2
        const editData: EditPresentationRequest = {
          presentation_id: generateResult.data.presentation_id,
          data: [
            {
              index: 0, // First slide (index 0)
              content: {
                title:
                  "‘Yogurt’ holds significant market presence in Foodservice with 14.85% penetration, while ‘Greek Yogurt’ showing strong traction as well",
                tableData: {
                  headers: ["Name", "Menu Penetration", "Foodservice Growth"],
                  rows: [
                    ["Frozen Yogurt", "0.18%", "10.42%"],
                    ["Greek Yogurt", "0.1%", "8.42%"],
                    ["Plain Yogurt", "0.19%", "11.42%"],
                    ["Plant Based Yogurt", "4.18%", "20.42%"],
                    ["Yogurt", "3.18%", "14.42%"],
                    ["Yogurt Dressing", "1.18%", "16.42%"],
                  ],
                },
              },
            },
          ],
          export_as: "pptx",
        };

        console.log("Editing presentation with reinforced content...");
        const editResult = await editPresentation(editData);

        if (editResult.success && editResult.data) {
          console.log("Presentation edited successfully:", editResult.data);

          // Extract filename from the edited presentation path
          const filename =
            editResult.data.path.split("/").pop() || "presentation.pptx";

          // Show success message and download
          const userChoice = confirm(
            `Presentation "${filename}" generated and edited successfully!\n\n` +
              `Presentation ID: ${editResult.data.presentation_id}\n\n` +
              `The first two slides have been reinforced with your prompt content.\n\n` +
              `Click OK to download the file, or Cancel to continue without downloading.`,
          );

          if (userChoice) {
            try {
              await downloadPresentation(editResult.data.path, filename);
            } catch (downloadError) {
              console.error("Download failed:", downloadError);
              alert(
                "Presentation was generated and edited but download failed. You can access it later using the presentation ID.",
              );
            }
          }

          // Optional: You could also provide an edit link
          console.log(
            "Edit presentation at:",
            `http://localhost:5001${editResult.data.edit_path}`,
          );
        } else {
          console.error("Presentation editing failed:", editResult.error);
          alert(
            `Presentation was generated but editing failed: ${editResult.error}. You can still download the original version.`,
          );

          // Fallback: offer to download the original presentation
          const fallbackChoice = confirm(
            "Would you like to download the original presentation instead?",
          );
          if (fallbackChoice) {
            const filename =
              generateResult.data.path.split("/").pop() || "presentation.pptx";
            try {
              await downloadPresentation(generateResult.data.path, filename);
            } catch (downloadError) {
              console.error("Download failed:", downloadError);
              alert(
                "Download failed. You can access the presentation later using the presentation ID.",
              );
            }
          }
        }
      } else {
        console.error("Presentation creation failed:", result.error);
        alert(`Failed to create presentation: ${result.error}`);
      }
    } catch (error) {
      console.error("Error in handleGenerateReport:", error);
      alert("An unexpected error occurred while creating the presentation.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategoryClick = (category: string) => {
    setInputActive(true);
    switch (category) {
      case "analyze":
        setCurrentQuestions(analyzeQuestions);
        setQuestion("Analyze ");
        break;
      case "compare":
        setCurrentQuestions(compareQuestions);
        setQuestion("Compare ");
        break;
      case "pricing":
        setCurrentQuestions(pricingQuestions);
        setQuestion("What is ");
        break;
      case "sales":
        setCurrentQuestions(salesQuestions);
        setQuestion("What are ");
        break;
      case "innovate":
        setCurrentQuestions(innovateQuestions);
        setQuestion("Identify ");
        break;
      default:
        setCurrentQuestions([]);
    }
  };

  const handleSuggestionsClose = () => {
    setCurrentQuestions([]);
    setInputActive(false);
  };

  return (
    <div
      className={twMerge(
        "mx-auto w-[670px] transition-[max-width] delay-400 duration-200 ease-in-out",
      )}
    >
      <MenuBotLogo hide={false} />
      <div className="relative mb-5">
        <MenuBotInput
          question={question}
          onQuestionChange={handleQuestionChange}
          onSendMessage={handleSendMessage}
          onKeyDown={handleKeyDown}
          isLoading={isLoading}
        />
        <MenuBotInputSuggestions
          question={question}
          onSelect={handleSelectSuggestion}
          onSubmit={handleSendMessage}
          suggestions={currentQuestions}
          onClose={handleSuggestionsClose}
          hasInteracted={inputActive}
        />
      </div>
      <div className="mb-5">
        <div
          className={twMerge(
            "flex items-center justify-center gap-3 transition-all duration-300 ease-in-out",
            inputActive
              ? "pointer-events-none translate-y-2 opacity-0"
              : "translate-y-0 opacity-100",
          )}
        >
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("analyze")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconAnalyze size={20} className="text-yellow-800" />
            Analyze
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("compare")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconCompare size={20} className="text-yellow-800" />
            Compare
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("pricing")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconPricing size={20} className="text-yellow-800" />
            Pricing
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("sales")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconSales size={20} className="text-yellow-800" />
            Sales
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("innovate")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconInnovate size={20} className="text-yellow-800" />
            Innovate
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleGenerateReport()}
            disabled={isLoading}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconInnovate size={20} className="text-yellow-800" />
            {isLoading ? "Generating..." : "Generate"}
          </Button>
        </div>
      </div>
      <PreviousConversations />
      {/* <MenuBotHistory
        hide={inputActive}
        onSelect={handleSelectSuggestion}
        onSubmit={handleSendMessage}
      /> */}
      {/* <DataSourcesReports hide={inputActive} count={2} /> */}
    </div>
  );
};

export default MenuBotPanel;
