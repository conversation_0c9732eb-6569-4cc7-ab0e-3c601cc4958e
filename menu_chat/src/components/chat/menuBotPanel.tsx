import { useState } from "react";
import { twMerge } from "tailwind-merge";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useMutation } from "@tanstack/react-query";
import MenuBotLogo from "./menuBotLogo";
import MenuBotInputSuggestions from "./menuBotInputSuggestions";
import MenuBotInput from "./menuBotInput";
import { createChat } from "@/api/chats";
import {
  createPresentationFromJson,
  downloadPresentation,
  CreateFromJsonRequest,
  PresentationData,
} from "@/api/presentation";
import Button from "../base/button";
import {
  analyzeQuestions,
  compareQuestions,
  pricingQuestions,
  salesQuestions,
  innovateQuestions,
  defaultQuestions,
  IngredientQuestion,
  getQuestions,
} from "@/data/ingredients";
import {
  IconAnalyze,
  IconCompare,
  IconInnovate,
  IconPricing,
  IconSales,
} from "../icons";
import PreviousConversations from "./previousConversations";

// Define an error type for better type safety
interface ApiError {
  response?: {
    status?: number;
  };
  message?: string;
}

const MenuBotPanel = () => {
  const router = useRouter();
  const [question, setQuestion] = useState<string>("");
  const [inputActive, setInputActive] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentQuestions, setCurrentQuestions] = useState<
    IngredientQuestion[]
  >([]);
  const [lastGeneratedPresentation, setLastGeneratedPresentation] =
    useState<PresentationData | null>(null);
  const { data: session } = useSession();

  // Create chat mutation
  const chatMutation = useMutation({
    mutationFn: ({
      authorization,
      message,
    }: {
      authorization: string;
      message: string;
    }) => createChat(authorization, message),
    onSuccess: (response) => {
      if (response && response.data) {
        const { guid } = response.data;
        router.push(`/answer/${guid}`);
      }
    },
    onError: (error: ApiError) => {
      console.error("Error creating chat:", error);
      setIsLoading(false);
      if (error.response?.status === 500) {
        alert(
          "Server error while creating chat. Please try again in a few moments.",
        );
      } else if (error.response?.status === 401) {
        alert("Please sign in again.");
      } else if (error.message) {
        alert(`Error: ${error.message}`);
      } else {
        alert("Failed to create chat. Please try again.");
      }
    },
  });

  const handleQuestionChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    const newQuestion = event.target.value;
    setQuestion(newQuestion);
    setInputActive(true);

    // Get ingredient questions
    const ingredientQuestions = getQuestions(newQuestion);

    // Only set default questions if there are no ingredient questions
    if (ingredientQuestions.length === 0 && newQuestion.length > 0) {
      setCurrentQuestions(defaultQuestions);
    } else {
      setCurrentQuestions(ingredientQuestions);
    }
  };

  const handleSendMessage = (message?: string) => {
    const messageToSend = message || question;
    if (!messageToSend || !messageToSend.trim()) return;

    if (!session?.user?.authorization) {
      alert("Please sign in.");
      return;
    }

    try {
      setIsLoading(true);
      chatMutation.mutate({
        authorization: session.user.authorization,
        message: messageToSend.trim(),
      });
    } catch (error) {
      console.error("Error in handleSendMessage:", error);
      setIsLoading(false);
    }
  };

  const handleSelectSuggestion = (value: string) => {
    setQuestion(value);
    //  setInputActive(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Fixed handleGenerateReport function
const handleGenerateReport = async () => {
  try {
    setIsLoading(true);

    // Use the test payload structure from the documentation
    const presentationData: CreateFromJsonRequest = {
      title: "Comprehensive Business Analytics Presentation",
      template: "custom-71a7d990-9206-4dbf-87e5-6303a8938a14",
      export_as: "pdf",
      slides: [
        {
          layout: "custom-71a7d990-9206-4dbf-87e5-6303a8938a14:header-table-brand-slide",
          content: {
            title: "Quarterly Performance Results Showing Strong Growth Across All Key Metrics",
            table: {
              columns: [
                { label: "Quarter" },
                { label: "Revenue ($M)" },
                { label: "Growth %" },
                { label: "Market Share" }
              ],
              rows: [
                { cells: ["Q1 2024", "125.4", "15.2%", "22.3%"] },
                { cells: ["Q2 2024", "142.8", "18.7%", "24.1%"] },
                { cells: ["Q3 2024", "158.2", "21.4%", "25.8%"] },
                { cells: ["Q4 2024", "175.6", "19.3%", "27.2%"] }
              ]
            }
          }
        },
        {
          layout: "custom-71a7d990-9206-4dbf-87e5-6303a8938a14:header-subheading-description-image-badge-slide",
          content: {
            title: "Innovation Drives Market Leadership",
            subheading: "Technology Focus",
            description: "Our cutting-edge AI solutions are transforming customer experiences and driving unprecedented growth in market adoption rates.",
            productImage: {
              __image_url__: "https://images.pexels.com/photos/373543/pexels-photo-373543.jpeg",
              __image_prompt__: "Modern technology workspace with AI interface displays and data analytics"
            }
          }
        }
      ]
    };

    // Create presentation using the new single endpoint
    const result = await createPresentationFromJson(presentationData);

    if (result.success && result.data) {
      console.log("Presentation created successfully:", result.data);

      // Store the presentation data for future reference
      setLastGeneratedPresentation(result.data);

      // Extract filename from the presentation path
      const filename = result.data.path.split("/").pop() || "presentation.pdf";

      // Show success message and download
      const userChoice = confirm(
        `Presentation "${filename}" created successfully!\n\n` +
          `Presentation ID: ${result.data.presentation_id}\n\n` +
          `Click OK to download the file, or Cancel to continue without downloading.`
      );

      if (userChoice) {
        try {
          await downloadPresentation(result.data.path, filename);
        } catch (downloadError) {
          console.error("Download failed:", downloadError);
          alert(
            "Presentation was created but download failed. You can access it later using the presentation ID."
          );
        }
      }

      // Optional: You could also provide an edit link
      console.log(
        "Edit presentation at:",
        `https://presenton.ai${result.data.edit_path}`
      );
    } else {
      console.error("Presentation creation failed:", result.error);
      alert(`Failed to create presentation: ${result.error}`);
    }
  } catch (error) {
    console.error("Error in handleGenerateReport:", error);
    alert("An unexpected error occurred while creating the presentation.");
  } finally {
    setIsLoading(false);
  }
};


  const handleCategoryClick = (category: string) => {
    setInputActive(true);
    switch (category) {
      case "analyze":
        setCurrentQuestions(analyzeQuestions);
        setQuestion("Analyze ");
        break;
      case "compare":
        setCurrentQuestions(compareQuestions);
        setQuestion("Compare ");
        break;
      case "pricing":
        setCurrentQuestions(pricingQuestions);
        setQuestion("What is ");
        break;
      case "sales":
        setCurrentQuestions(salesQuestions);
        setQuestion("What are ");
        break;
      case "innovate":
        setCurrentQuestions(innovateQuestions);
        setQuestion("Identify ");
        break;
      default:
        setCurrentQuestions([]);
    }
  };

  const handleSuggestionsClose = () => {
    setCurrentQuestions([]);
    setInputActive(false);
  };

  return (
    <div
      className={twMerge(
        "mx-auto w-[670px] transition-[max-width] delay-400 duration-200 ease-in-out",
      )}
    >
      <MenuBotLogo hide={false} />
      <div className="relative mb-5">
        <MenuBotInput
          question={question}
          onQuestionChange={handleQuestionChange}
          onSendMessage={handleSendMessage}
          onKeyDown={handleKeyDown}
          isLoading={isLoading}
        />
        <MenuBotInputSuggestions
          question={question}
          onSelect={handleSelectSuggestion}
          onSubmit={handleSendMessage}
          suggestions={currentQuestions}
          onClose={handleSuggestionsClose}
          hasInteracted={inputActive}
        />
      </div>
      <div className="mb-5">
        <div
          className={twMerge(
            "flex items-center justify-center gap-3 transition-all duration-300 ease-in-out",
            inputActive
              ? "pointer-events-none translate-y-2 opacity-0"
              : "translate-y-0 opacity-100",
          )}
        >
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("analyze")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconAnalyze size={20} className="text-yellow-800" />
            Analyze
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("compare")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconCompare size={20} className="text-yellow-800" />
            Compare
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("pricing")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconPricing size={20} className="text-yellow-800" />
            Pricing
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("sales")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconSales size={20} className="text-yellow-800" />
            Sales
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleCategoryClick("innovate")}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconInnovate size={20} className="text-yellow-800" />
            Innovate
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleGenerateReport()}
            disabled={isLoading}
            className="transition-colors duration-200 hover:bg-yellow-200"
          >
            <IconInnovate size={20} className="text-yellow-800" />
            {isLoading ? "Generating..." : "Generate"}
          </Button>
        </div>
      </div>
      <PreviousConversations />
      {/* <MenuBotHistory
        hide={inputActive}
        onSelect={handleSelectSuggestion}
        onSubmit={handleSendMessage}
      /> */}
      {/* <DataSourcesReports hide={inputActive} count={2} /> */}
    </div>
  );
};

export default MenuBotPanel;
