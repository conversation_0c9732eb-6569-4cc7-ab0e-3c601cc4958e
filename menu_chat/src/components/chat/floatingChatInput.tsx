import { useState } from "react";
import Input from "@/components/base/input";
import Button from "@/components/base/button";
import { IconSparkle, IconSend } from "@/components/icons";
import { useAppStore } from "@/store/useAppStore";
import { motion } from "motion/react";

interface FloatingChatInputProps {
  onSendMessage?: (message: string) => void;
  placeholder?: string;
  initialValue?: string;
  disabled?: boolean;
  onClick?: () => void;
}

const FloatingChatInput = ({
  onSendMessage,
  placeholder = "Ask MenuData AI anything ...",
  initialValue = "",
  disabled = false,
  onClick,
}: FloatingChatInputProps) => {
  const { navCollapsed: collapsed } = useAppStore();
  const [inputValue, setInputValue] = useState(initialValue);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  const handleInputClick = () => {
    if (onClick) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey && inputValue.trim() && !disabled) {
      e.preventDefault(); // Prevent default Enter behavior
      if (onSendMessage) {
        onSendMessage(inputValue);
        setInputValue("");
      }
    }
  };

  const handleSend = () => {
    if (inputValue.trim() && !disabled) {
      if (onSendMessage) {
        onSendMessage(inputValue);
        setInputValue("");
      }
    }
  };

  return (
    <motion.div
      initial={false}
      animate={{
        left: collapsed ? "var(--nav-collapsed-width)" : "var(--nav-width)",
      }}
      transition={{ duration: 0.2, ease: "easeInOut" }}
      className="fixed right-2 bottom-2 z-50 ml-2"
    >
      <div className="relative mx-auto max-w-[700px] min-w-[550px] px-10 py-5">
        {/* <span className="pointer-events-none absolute top-0 right-0 bottom-0 left-0 mx-auto block bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,1)_0%,rgba(255,255,255,0)_100%)] backdrop-blur-[2px]"></span> */}
        <div
          className="inline-flex w-full items-center justify-center rounded-[14px] bg-[linear-gradient(267deg,_#DBD8BF_0%,_#FFE4AE_43.18%,_#C6C5BD_100%)] p-[3px]"
          onClick={handleInputClick}
        >
          <div className="inline-flex w-full items-center justify-center rounded-[13px] bg-[#f5f5f5] p-0.5">
            <Input
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onClick={handleInputClick}
              type="text"
              placeholder={placeholder}
              disabled={disabled}
              inputClassName={`w-full h-[52px] rounded-xl px-14 !border-muted-200 ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
              className="relative z-20"
            />
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSend}
            disabled={disabled}
            className={`absolute top-0 right-12 bottom-0 z-20 my-auto w-9 !px-0 ${disabled ? "cursor-not-allowed opacity-50" : ""}`}
          >
            <IconSend />
            <span className="sr-only">Send</span>
          </Button>
          <IconSparkle className="pointer-events-none absolute top-0 bottom-0 left-14 z-30 my-auto" />
        </div>
      </div>
    </motion.div>
  );
};

export default FloatingChatInput;
