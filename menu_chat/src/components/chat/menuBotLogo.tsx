import Heading from "@/components/base/heading";
import { AnimatePresence, motion } from "motion/react";

interface MenuBotLogoProps {
  hide: boolean;
}

const MenuBotLogo = ({ hide }: MenuBotLogoProps) => {
  return (
    <AnimatePresence initial={false} mode="wait">
      {!hide && (
        <motion.div
          layout
          key="menubot-logo"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="mb-5 text-center"
        >
          <Heading level={2}>MenuData AI</Heading>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MenuBotLogo;
