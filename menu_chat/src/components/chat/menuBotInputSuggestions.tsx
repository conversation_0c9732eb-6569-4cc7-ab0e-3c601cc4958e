import { useEffect, useState, useRef } from "react";
import { twMerge } from "tailwind-merge";
import { IconSearch, IconCircleUpright } from "@/components/icons";
import {
  IngredientQuestion,
  getQuestions,
  ingredientList,
} from "@/data/ingredients";

interface MenuBotInputSuggestionsProps {
  className?: string;
  question: string;
  onSelect: (value: string) => void;
  onSubmit: (message?: string) => void;
  suggestions?: IngredientQuestion[];
  onClose?: () => void;
  hasInteracted?: boolean;
}

const MenuBotInputSuggestions = ({
  className,
  question,
  onSelect,
  onSubmit,
  suggestions = [],
  onClose,
  hasInteracted = false,
}: MenuBotInputSuggestionsProps) => {
  const [show, setShow] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShow(false);
        onClose?.();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  useEffect(() => {
    if (hasInteracted) {
      setShow(true);
    }
  }, [hasInteracted]);

  const highlightIngredient = (text: string, searchTerm: string) => {
    if (!searchTerm) return text;

    // First try to find a match for the entire search term
    const fullMatch = ingredientList.find(
      (ingredient) =>
        ingredient.toLowerCase() === searchTerm.toLowerCase().trim(),
    );

    if (fullMatch) {
      // Split the text by the full ingredient name (case insensitive)
      const regex = new RegExp(fullMatch, "gi");
      const parts = text.split(regex);

      return parts.map((part, index) => (
        <span key={index}>
          {index > 0 && (
            <span className="font-semibold text-yellow-800">{fullMatch}</span>
          )}
          {part}
        </span>
      ));
    }

    // If no full match, try individual words
    const words = searchTerm.toLowerCase().trim().split(/\s+/);
    const matchingIngredient = words.find((word) => {
      const match = ingredientList.some(
        (ingredient) => ingredient.toLowerCase() === word.toLowerCase(),
      );
      return match;
    });

    if (!matchingIngredient) return text;

    // Split the text by the ingredient name (case insensitive)
    const regex = new RegExp(matchingIngredient, "gi");
    const parts = text.split(regex);

    return parts.map((part, index) => (
      <span key={index}>
        {index > 0 && (
          <span className="font-semibold text-yellow-800">
            {matchingIngredient}
          </span>
        )}
        {part}
      </span>
    ));
  };

  // Get ingredient questions if available
  const ingredientQuestions = getQuestions(question);

  // Only show suggestions if user has interacted and there are actual questions to show
  const shouldShowSuggestions =
    show &&
    hasInteracted &&
    (suggestions.length > 0 || ingredientQuestions.length > 0);
  // If no suggestions to show, don't render the dropdown at all
  if (!shouldShowSuggestions) {
    return null;
  }

  // Determine which questions to show
  const questionsToShow =
    suggestions.length > 0 ? suggestions : ingredientQuestions;

  return (
    <div
      ref={dropdownRef}
      className={twMerge(
        "absolute top-[calc(100%+4px)] right-0 left-0 z-20 p-1",
        className,
      )}
    >
      <div className="flex flex-col gap-1">
        {questionsToShow.map((suggestion) => (
          <div
            key={suggestion.question}
            className="group hover:bg-muted-500/15 hover:border-muted-200 border-muted-100 flex w-full cursor-pointer items-center justify-between rounded-lg border-2 bg-white p-2.5 transition-colors"
            onClick={() => {
              onSelect(suggestion.question);
              onSubmit?.(suggestion.question);
              setShow(false);
            }}
          >
            <div className="flex items-center gap-1">
              <IconSearch
                size={16}
                className="text-neutral-500 transition-colors"
              />
              <span className="text-sm font-medium text-neutral-700 transition-colors group-hover:text-neutral-900">
                {highlightIngredient(suggestion.question, question)}
              </span>
            </div>
            <IconCircleUpright
              size={20}
              className="text-yellow-800 opacity-0 transition-opacity group-hover:opacity-100"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default MenuBotInputSuggestions;
