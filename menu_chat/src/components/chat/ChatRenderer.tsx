import React from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../answers/MarkdownRenderer';
import { SafeImage } from "@/components/utils/helpers";
import foodAnimation from '../../assets/food-animation.gif';

// Define the type for SDUI component data
interface StreamComponent {
  component_id: string;
  component_type: string;
  content: Record<string, unknown>;
}

interface StreamData {
  components?: StreamComponent[];
}

// Use the same Message type as in the application
interface Message {
  id: number;
  message_type: string;
  message: string;
  stream_data?: StreamData | StreamComponent[] | Record<string, unknown>; // More specific type for SDUI components
  chat_id: number;
  created_at: string;
  updated_at: string;
  severity?: number;
  isThinking?: boolean; // Add a flag to identify the thinking message
}

interface ChatRendererProps {
  messages: Message[];
  isLoading?: boolean;
}

const ChatRenderer: React.FC<ChatRendererProps> = ({ messages, isLoading }) => {  
  const renderMessage = (message: Message) => {
    const { message: text, isThinking } = message;
    
    // const messageClass = message_type === 'ai' 
    //   ? 'flex justify-start' 
    //   : 'flex justify-end';
    // If this is the thinking message, render the food animation GIF
    if (isThinking) {
      return (
          <div className="flex flex-col items-center my-4 w-full mx-auto">
            <SafeImage
              src={foodAnimation}
              alt="AI is thinking" 
              width={100} 
              height={100}
              className="mb-2"
            />
            <span className="text-sm text-gray-500">Thinking...</span>
          </div>
      );
    }
    
    // If the message has stream_data, render it using StreamBlockRenderer
    // if (message_type === 'ai' && stream_data) {
    //   // Transform any data format to our simplified components format
    //   const transformedData = {
    //     components: Array.isArray(stream_data) 
    //       ? stream_data // If it's already an array, use it directly
    //       : 'components' in stream_data && Array.isArray(stream_data.components)
    //         ? stream_data.components // If it already has components, use those
    //         : [] // Fallback to empty array
    //   };
      
    //   return (
    //     <div className="flex justify-start">
    //       <div className="mt-2 mb-2 rounded-3xl bg-gray-100 px-5 py-4 max-w-3xl">
    //         <StreamBlockRenderer streamData={transformedData} />
    //       </div>
    //     </div>
    //   );
    // }
    
    // For text messages, render links directly
    return (
      <div className="min-w-[550px] mb-4">
        <MarkdownRenderer markdown={text} />
      </div>
    );
  };
  
  const allMessages = [...messages];
  
  // If loading and we're waiting for an AI response, add a loading message
  if (isLoading) {
    // Find the last AI message
    const lastAiMessage = [...messages].reverse().find(m => m.message_type === 'ai');
    const lastMessage = messages[messages.length - 1];
    
    // Show loading if:
    // 1. There are no messages, or
    // 2. The last message is from the user, or
    // 3. The last AI message is empty/incomplete
    if (!lastMessage || 
        lastMessage.message_type === 'user' || 
        (lastAiMessage && (!lastAiMessage.message || lastAiMessage.message.trim() === ''))) {
      allMessages.push({
        id: Date.now(),
        chat_id: lastMessage?.chat_id || 0,
        message_type: 'ai',
        message: '',  // Empty message since we're using the animation
        isThinking: true, // Flag this as a thinking message
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        severity: 1
      } as Message);
    }
  }
  
  return (
    <div className="flex flex-col">
      {allMessages.map((message) => (
        <React.Fragment key={message.id}>
          {renderMessage(message)}
        </React.Fragment>
      ))}
    </div>
  );
};

export default ChatRenderer; 