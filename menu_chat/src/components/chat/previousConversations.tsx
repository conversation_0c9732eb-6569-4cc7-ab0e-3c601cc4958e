import React from "react";
import { IconCircleUpright } from "../icons";
import Heading from "../base/heading";

interface PreviousConversationsProps {
  conversations?: string[];
}

const previousConversations: React.FC<PreviousConversationsProps> = ({
  conversations = [
    "Plant based proteins in New York",
    "Past growing ingredients and dishes",
    "Chicken strips — foodservice analysis",
  ],
}) => {
  return (
    <>
      <Heading level={6} className="px-2 pb-3 text-neutral-700">
        Previous Conversations
      </Heading>
      <div className="space-y-1">
        {conversations.map((title, index) => (
          <div
            key={index}
            className="group border-muted-100 hover:bg-muted-500/15 hover:border-muted-200 flex w-full cursor-pointer items-center justify-between rounded-lg border-[1px] bg-white p-2.5 transition-colors"
          >
            <div className="flex items-center gap-1">
              <span className="font-worksans text-sm font-medium text-neutral-700 transition-colors group-hover:text-neutral-900">
                {title}
              </span>
            </div>
            <IconCircleUpright
              size={20}
              className="text-yellow-800 opacity-0 transition-opacity group-hover:opacity-100"
            />
          </div>
        ))}
      </div>
    </>
  );
};

export default previousConversations;
