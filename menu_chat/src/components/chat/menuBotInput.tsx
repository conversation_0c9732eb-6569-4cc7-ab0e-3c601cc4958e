import Textarea from "@/components/base/textarea";
import Button from "@/components/base/button";
import { IconSparkle, IconSend } from "@/components/icons";
import { useEffect } from "react";
import { useRef } from "react";
import IconAttachment from "../icons/icon-attachment";

interface MenuBotInputProps {
  question: string;
  onQuestionChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onSendMessage: (question: string) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  isLoading?: boolean;
}

const MenuBotInput = ({
  question,
  onQuestionChange,
  onSendMessage,
  onKeyDown,
  isLoading = false,
}: MenuBotInputProps) => {
  const textarea_ref = useRef<HTMLTextAreaElement>(null);
  useEffect(() => {
    textarea_ref.current?.focus();
  });
  return (
    <div className="w-full rounded-[14px] bg-[linear-gradient(267deg,_#DBD8BF_0%,_#FFE4AE_43.18%,_#C6C5BD_100%)] p-[3px]">
      <div className="rounded-[13px] bg-[#F5F5F5] p-0.5">
        <div className="relative flex flex-col gap-9 rounded-xl border-1 border-[#F0EDE6] bg-white p-4">
          <div className="flex w-full items-start gap-3">
            <IconSparkle
              className={`min-w-6 ${isLoading ? "animate-pulse" : ""}`}
            />
            <Textarea
              placeholder={
                isLoading
                  ? "Creating your chat..."
                  : "Ask MenuData AI anything ..."
              }
              rows={2}
              className={`border-none p-0 ${isLoading ? "cursor-not-allowed opacity-50" : ""}`}
              value={question}
              onChange={onQuestionChange}
              onKeyDown={onKeyDown}
              disabled={isLoading}
              ref={textarea_ref}
            />
          </div>
          <div className="flex w-full items-baseline justify-between gap-3 text-neutral-700">
            <Button variant="tertiary" size="xs">
              <IconAttachment size={16}/>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className={`min-w-9 bg-yellow-700 !px-0 ${isLoading ? "cursor-not-allowed opacity-50" : "hover:bg-yellow-600 active:bg-yellow-700"}`}
              onClick={() => onSendMessage(question)}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-900 border-b-transparent"></div>
              ) : (
                <IconSend />
              )}
              <span className="sr-only">{isLoading ? "Loading" : "Send"}</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default MenuBotInput;
