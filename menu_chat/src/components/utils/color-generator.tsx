type ColorVariant = "#78C5E3" | "#FAAB61" | "#DF9DE4" | "#FF9985" | "#BDA4CB" | "#8BC539"


// takes in a name, and returns a color. - color can only be used once.
class ColorGenerator {
  guid: string
  constructor(guid: string){
    this.guid = guid
  }

  private availableColors: ColorVariant[] = [
    "#78C5E3",
    "#FAAB61",
    "#DF9DE4",
    "#FF9985",
    "#BDA4CB",
    "#8BC539"
  ];
  private assignedColors: Map<string, ColorVariant> = new Map();

  getColor(name: string): ColorVariant | null {
    if (this.assignedColors.has(name)) {
      return this.assignedColors.get(name) || null;
    }
    if (this.availableColors.length === 0) {
      return null;
    }
    const randomIndex = Math.floor(Math.random() * this.availableColors.length);
    const color = this.availableColors[randomIndex];
    this.assignedColors.set(name, color);
    this.availableColors.splice(randomIndex, 1);
    return color;
  }
}

export default ColorGenerator;