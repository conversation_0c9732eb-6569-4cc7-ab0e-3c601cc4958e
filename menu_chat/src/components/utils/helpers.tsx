import { IconGraph, IconIncrease, IconDecrease } from "@/components/icons";
import { ReactNode } from "react";
type TagVariant = "greenOutline" | "redOutline" | "blueOutline" | "neutral" | "blue" | "red" | "green" | "yellow" | "violet" | "white" | undefined;

export const getVariantForMenuAdoption = (menuAdoption: string): TagVariant => {
  switch (menuAdoption) {
    case "Growth": return "green";
    case "Emergence": return "yellow";
    case "Mature": return "blue";
    case "Mainstream": return "violet";
    default: return "white";
  }
}

export const getVariantForRate = (rate: number) : {icon: ReactNode, type: TagVariant} => {
  if (rate > 0){
    return {
      icon: <IconIncrease size={16} />,
      type: "greenOutline",
    }
  }
  else if (rate == 0) {
    return {
      icon: <IconGraph size={16} />,
      type: "blueOutline",
    }
  }
  else {
    return {
      icon: <IconDecrease size={16} />,
      type: "redOutline",
    }
  }
}

interface GrowthChange {
  value: number;
  icon: React.ReactNode;
  type: TagVariant;
}

export const getVariantForChange = (change: number) : GrowthChange => {
  if (change > 0){
    return {
      value: change,
      icon: <IconIncrease/>,
      type: "greenOutline"
    }
  }
  else if (change == 0){
    return {
      value: change,
      icon: <IconGraph/>,
      type: "blueOutline"
    }
  }
  else {
    return {
      value: change,
      icon: <IconDecrease/>,
      type: "redOutline"
    }
  }

}

export const isValidUrl = (url: string) => {
  try {
    return Boolean(new URL(url));
  }
  catch {
    return false;
  }
}
