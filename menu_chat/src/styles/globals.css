@import "tailwindcss";

@theme {
  /* Fonts */
  --font-archivo: var(--font-archivo), sans-serif;
  --font-worksans: var(--font-worksans), sans-serif;

  /* Colors */
  --color-yellow-100: #fff0d6;
  --color-yellow-200: #ffe4ae;
  --color-yellow-300: #fedb91;
  --color-yellow-400: #ffd379;
  --color-yellow-500: #ffc857;
  --color-yellow-600: #ffbb32;
  --color-yellow-700: #ffad08;
  --color-yellow-800: #e09600;
  --color-yellow-900: #b87a00;
  --color-yellow-1000: #523600;

  --color-red-100: #fbe8e3;
  --color-red-200: #ffcfc6;
  --color-red-300: #ffbaad;
  --color-red-400: #ff9985;
  --color-red-500: #ff6d56;
  --color-red-600: #ea5e3f;
  --color-red-700: #e94626;
  --color-red-800: #cd391c;
  --color-red-900: #a82d15;
  --color-red-1000: #520a00;

  --blue-100: #ebf6fb;
  --blue-200: #bbe2f2;
  --blue-500: #57b7dd;
  --blue-1000: #0e3444;

  --color-green-100: #e8f3d7;
  --color-green-200: #c7e3a0;
  --color-green-500: #8bc539;
  --color-green-700: #648e29;
  --color-green-1000: #22300d;

  --color-violet-100: #f4eff6;
  --color-violet-200: #ddd6fe;
  --color-violet-600: #9571ad;
  --color-violet-1000: #402d4e;

  --color-orange-100: #fdddc0;
  --color-orange-200: #fbc088;
  --color-orange-600: #f99131;
  --color-orange-1000: #4f2802;

  --color-muted-1000: #323020;
  --color-muted-900: #4b4830;
  --color-muted-800: #646140;
  --color-muted-700: #7c7950;
  --color-muted-600: #95925f;
  --color-muted-500: #afac83;
  --color-muted-400: #bfbc9b;
  --color-muted-300: #cfccb4;
  --color-muted-200: #dfddce;
  --color-muted-100: #f0ede6;
  --color-muted-50: #f9f9f7;

  --color-neutral-1000: #2c2a26;
  --color-neutral-900: #37352f;
  --color-neutral-800: #4d4a42;
  --color-neutral-700: #58554b;
  --color-neutral-600: #7f7c72;
  --color-neutral-500: #989586;
  --color-neutral-400: #b4b1a7;
  --color-neutral-300: #c7c5bd;
  --color-neutral-200: #d9d8d3;
  --color-neutral-100: #ecece9;
  --color-neutral-50: #f5f5f4;

  --nav-width: 232px;
  --nav-collapsed-width: 72px;
}

@layer utilities {
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }
}

@layer base {
  .font-archivo {
    font-family: var(--font-archivo);
  }

  .font-worksans {
    font-family: var(--font-worksans);
  }

  /* Body styling */
  body {
    background-color: var(--color-muted-100);
    color: var(--color-neutral-1000);
    font-family: var(--font-worksans);
    height: 100vh;
    overflow: hidden;
  }

  a,
  p {
    font-family: var(--font-worksans);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-archivo);
    font-weight: 600;
  }

  h1 {
    font-size: 2.75rem;
    line-height: 1.09;
  }
  h2 {
    font-size: 2rem;
    line-height: 1.125;
  }
  h3 {
    font-size: 1.5rem;
    line-height: 1.33;
  }
  h4 {
    font-size: 1rem;
    line-height: 1.5;
  }
  h5 {
    font-size: 0.875rem;
    line-height: 1.43;
  }
  h6 {
    font-size: 0.75rem;
    line-height: 1.5;
  }

  ul, ol, li {
    margin: 0 !important;
  }

  .bg-custom-blur {
    background: rgba(255, 255, 255, 0.01);
    backdrop-filter: blur(20px);
  }

  .filter-heading-label {
    svg {
      display: none;
    }
  }

  /* tbody.with-border {
    tr {
      &:first-child {
        td {
          border-top: 1px solid var(--color-neutral-200);
          &:first-child {
            border-top-left-radius: 8px;
          }
          &:last-child {
            border-top-right-radius: 8px;
          }
        }
      }
      &:last-child {
        td {
          &:first-child {
            border-bottom-left-radius: 8px;
          }
          &:last-child {
            border-bottom-right-radius: 8px;
          }
        }
      }
    }
    td {
      &:first-child {
        border-left: 1px solid var(--color-neutral-200);
      }
      &:last-child {
        border-right: 1px solid var(--color-neutral-200);
      }
    }
  } */
}
