import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";
export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    if (!token) {
      return NextResponse.redirect(new URL("/login", req.url));
    }
    const now = Math.floor(Date.now() / 1000);
    if (token.exp && now >= (token.exp as number)) {
      return NextResponse.redirect(new URL("/login", req.url));
    }
    return NextResponse.next();
  },{
  pages: {
    signIn: "/login", // Redirect unauthenticated users
    error: "/error",
  },
  secret: process.env.NEXTAUTH_SECRET, // Must match your NextAuth config
});

// Specify which routes to protect
export const config = {
  matcher: [
    "/ingredients/:path*",
    "/menu/:path*",
    "/answer/:path*",
    "/menu-items/:path*",
    "/retail/:path*",
    "/settings/:path*",
  ], // Protect these routes
};