import { withAuth } from "next-auth/middleware";

export default withAuth({
  pages: {
    signIn: "/login", // Redirect unauthenticated users
    error: "/error",
  },
  callbacks: {
    // Optional: restrict to users with certain roles
    authorized: ({ token }) => !!token, // Only allow if token exists (user is authenticated)
    // Example: authorized: ({ token }) => token?.role === "admin"
  },
  secret: process.env.NEXTAUTH_SECRET, // Must match your NextAuth config
});

// Specify which routes to protect
export const config = {
  matcher: ["/ingredients/:path*", "/menu/:path*", "/answer/:path*"], // Protect these routes
};