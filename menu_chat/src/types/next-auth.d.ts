import { DefaultSession} from "next-auth";

declare module "next-auth" {
    interface Session extends DefaultSession {
        user: {
            id?: string
            name?: string | null
            email?: string | null
            image?: string | null
            authorization?: string | null
        }
    }

    interface User {
        id: string
        email: string
        emailVerified: Date | null
        authorization?: string | null
    }
}

