// import React from "react";

interface Chat {
  id: number;
  guid: string;
  topic?: string;
}

interface Message {
  id: number;
  chat_id: number;
  message: string;
  message_type: string;
}

interface ChatPayload {
  chat: {
    id: number;
  };
  messages: Message[];
}

interface FastestGrowingIngredient {
  guid: string;
  name: string;
  created_at: string;
  penetration: number;
  change: number;
  prediction_1y: number;
}



interface QuestionSuggestion {
  id: number;
  text: string;
}

interface InnovationIngredient {
  created_at: string;
  image_url: string;
  name: string;
  penetration: number;
  change: number
  guid: string
}

type Favorite = {
  guid: string
  name: string
}

interface Pairing {
  id: number;
  ingredients: string[];
  ingredient_a_name: string,
  ingredient_a_guid: string,
  ingredient_b_name: string,
  ingredient_b_guid: string,
  category: string,
  pairing_percent: number,
  change: number
}

interface Quarter {
  quarter: number
  year: number
  pen_rate: number
}

interface MenuItem {
  id: number;
  name: string;
  price: number;
  description: string;
  business_name: string;
  restaurant_type: string;
  restaurant_guid: string;
  cuisine: string[];
  city: string;
  image_url: string;
  score?: number
}

interface RetailerData {
  id: string | null;
  name: string;
  products_with_l0: number;
  sentiment_percentages?: number[];
  social_mentions?: number;
  unique_skus: number;
  unique_store?: string | null;
}

interface TopRetailersResult {
  maxValue: number;
  data: BarChartItem[];
  originalData: RetailerData[];
  total: number;
}

interface ProductInnovation {
  banner_name: string;
  brand: string;
  category: string;
  change: number;
  first_appearance: string;
  image_url: string;
  name: string;
}

interface TopProductInnovationResult {
  data: ProductInnovation[];
  total: number;
}

interface RetailProduct {
  id: string;
  gri: string;
  main_category: string;
  category: string;
  sub_category: string;
  products_count: number;
}

interface RetailProductDetail {
  id: string;
  aroma: string;
  category: string;
  claim: string;
  description: string;
  images: string[];
  main_category: string;
  name: string;
  taste: string;
  texture: string;
}

interface RetailProductFlavor {
  id: string;
  created_at: string;
  name: string;
  // saturation?: string;
  // change?: number;
}

interface RetailProductChartData {
  maxValue: number;
  data: BarChartItem[];
}

interface BarChartItem {
  id: number;
  label: string;
  value: number;
  symbol?: string;
  color: string;
  customValue?: number;
}

interface RetailProductInnovation {
  brand: string;
  first_appearance: string;
  name: string;
  retailer_name: string;
}

interface TopBrandChartData {
  maxValue: number;
  data: BarChartItem[];
}

interface TopFormat {
  name: string;
  count: number;
}

interface TopFormatChartData {
  maxValue: number;
  data: BarChartItem[];
}

interface MenuItemRequest {
  auth: string,
  guid: string,
  page?: number,
  pageSize?: number,
  limit?: number,
  secondary_filter?: string,
  filters?: string[]
}

interface LimitedTimeOffer {
  chain_name: string
  guid: string;
  name: string;
  description: string;
  date: string;
  img_url: string;
  cuisine: string;
  offer_url: string
}

interface RetailProductTableFilters {
  l0?: string[];
  l1?: string[];
  gri?: string[];
  brand?: string[];
  claims?: string[];
  flavours?: string[];
  textures?: string[];
  retailer_name?: string[];
  banner_name?: string[];
}

interface FilterOption {
  key?: string;
  label: string | React.ReactNode;
  filters: {
    label: string;
    value: string;
  }[];
}

interface SortOption {
  label: string | React.ReactNode;
  value: string;
  link?: string;
}

type RetailLifecycleType = "new" | "emergence" | "mainstream" | "declining";

interface RetailLifecycleItem {
  id: number | string;
  gri?: string;
  guid?: string;
  claim?: string;
  image_url?: string | null;
  penetration: number | string;
  change: number | string;
  prediction?: number | string;
  type: RetailLifecycleType;
}
