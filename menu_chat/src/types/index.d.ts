interface Chat {
  id: number;
  guid: string;
  topic?: string;
}

interface Message {
  id: number;
  chat_id: number;
  message: string;
  message_type: string;
}

interface ChatPayload {
  chat: {
    id: number;
  };
  messages: Message[];
}

interface FastestGrowingIngredient {
  guid: string;
  name: string;
  created_at: string;
  penetration: number;
  change: number;
  prediction_1y: number;
}

interface LimitedTimeOffer {
  date: string;
  description: string;
  img_url: string;
  name: string;
  offer_url: string;
}

interface QuestionSuggestion {
  id: number;
  text: string;
}

interface InnovationIngredient {
  created_at: string;
  image_url: string;
  name: string;
  penetration: number;
  change: number
  guid: string
}

type Favorite = {
  guid: string
  name: string
}

interface Pairing {
  id: number;
  ingredients: string[];
  ingredient_a_name: string,
  ingredient_a_guid: string,
  ingredient_b_name: string,
  ingredient_b_guid: string,
  category: string,
  pairing_percent: number,
  change: number
}

interface Quarter {
  quarter: number
  year: number
  pen_rate: number
}

interface MenuItem {
  id: number;
  name: string;
  price: number;
  description: string;
  business_name: string;
  restaurant_type: string;
  cuisine: string[];
  city: string;
  image_url: string;
}
