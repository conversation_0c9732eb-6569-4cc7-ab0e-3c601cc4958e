import { createContext, useContext, useState, ReactNode } from "react";

interface RestaurantTypeNavigationState {
  fromRestaurantType: boolean;
  menuItem: string;
  menuItemGuid: string;
  restaurantType: string;
  restaurantName: string;
  source: "menu-items" | "ingredients";
}

interface RestaurantTypeNavigationContextType {
  navigationState: RestaurantTypeNavigationState;
  setRestaurantTypeNavigation: (
    menuItem: string,
    menuItemGuid: string,
    restaurantType: string,
    restaurantName: string,
    source: "menu-items" | "ingredients",
  ) => void;
  clearRestaurantTypeNavigation: () => void;
}

const defaultNavigationState: RestaurantTypeNavigationState = {
  fromRestaurantType: false,
  menuItem: "",
  menuItemGuid: "",
  restaurantType: "",
  restaurantName: "",
  source: "menu-items",
};

export const RestaurantTypeNavigationContext = createContext<
  RestaurantTypeNavigationContextType | undefined
>(undefined);

export const RestaurantTypeNavigationProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [navigationState, setNavigationState] =
    useState<RestaurantTypeNavigationState>(defaultNavigationState);

  const setRestaurantTypeNavigation = (
    menuItem: string,
    menuItemGuid: string,
    restaurantType: string,
    restaurantName: string,
    source: "menu-items" | "ingredients",
  ) => {
    setNavigationState({
      fromRestaurantType: true,
      menuItem,
      menuItemGuid,
      restaurantType,
      restaurantName,
      source,
    });
  };

  const clearRestaurantTypeNavigation = () => {
    setNavigationState(defaultNavigationState);
  };

  return (
    <RestaurantTypeNavigationContext.Provider
      value={{
        navigationState,
        setRestaurantTypeNavigation,
        clearRestaurantTypeNavigation,
      }}
    >
      {children}
    </RestaurantTypeNavigationContext.Provider>
  );
};

export const useRestaurantTypeNavigation = () => {
  const context = useContext(RestaurantTypeNavigationContext);
  if (context === undefined) {
    throw new Error(
      "useRestaurantTypeNavigation must be used within a RestaurantTypeNavigationProvider",
    );
  }
  return context;
};
