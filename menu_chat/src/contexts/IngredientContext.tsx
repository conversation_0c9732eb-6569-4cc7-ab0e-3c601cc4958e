import { createContext, useContext } from 'react';

type Image = {
  url: string;
}

interface Ingredient {
  name: string;
  description: string;
  category: string;
  appearance: string;
  taste: string;
  aroma: string;
  texture: string;
  guid: string;
  menu_adoption: string;
  pen_rate: number;
  delta_last_period: number;
  change_1y: number;
  google_trend: number;
  growth_prediction: number;
  images: Image[];
}

export const IngredientContext = createContext<Ingredient | undefined>(undefined)

export const useIngredient = () => {
  const context = useContext(IngredientContext)
  if (context === undefined){
    throw new Error('useIngredient must be used within a Ingredients Provider')
  }
  return context
}
