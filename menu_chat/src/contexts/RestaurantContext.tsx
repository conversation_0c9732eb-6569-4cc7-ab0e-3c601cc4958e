import { createContext, useContext } from "react";

export type Image = {
  src: string;
};

interface Restaurant {
  id: string;
  guid: string;
  business_name: string;
  address: string;
  address_2: string;
  city_name: string;
  state_name: string;
  country: string;
  zip_code: string;
  about: string;
  restaurant_type: string;
  url: string;
  price: string;
  ambient: string;
  yelp_url: string;
  yelp_categories: string[];
  images: Image[];
}

export const RestaurantContext = createContext<Restaurant | undefined>(
  undefined,
);

export const useRestaurant = () => {
  const context = useContext(RestaurantContext);
  if (context === undefined) {
    throw new Error("useRestaurant must be used within a Restaurants Provider");
  }
  return context;
};
