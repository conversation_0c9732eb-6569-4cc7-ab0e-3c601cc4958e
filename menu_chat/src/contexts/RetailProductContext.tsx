import { createContext, useContext } from 'react';

interface RetailProduct {
  id: string;
  description: string;
  main_category: string;
  category: string;
  claim: string;
  taste: string;
  aroma: string;
  texture: string;
  name: string;
}

export const RetailProductContext = createContext<RetailProduct | undefined>(undefined);

export const useRetailProduct = () => {
  const context = useContext(RetailProductContext);
  if (context === undefined) {
    throw new Error('useRetailProduct must be used within a RetailProduct Provider');
  }
  return context;
}