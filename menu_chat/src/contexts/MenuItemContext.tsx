import { createContext, useContext } from 'react';

type Image = {
  url: string;
}

interface MenuItem {
  guid: string;
  name: string;
  description: string;
  appearance: string[];
  taste: string[];
  aroma: string[];
  texture: string[];
  menu_adoption: string;
  pen_rate: number;
  foodservice_growth: number;
  foodservice_prediction: number;
  google_trend: number;
  growth_prediction: number;
  category: string;
  subcategory: string;
  dietary_tags: string[];
  cuisine_type: string[];
  images: Image[];
}
export const MenuItemContext = createContext<MenuItem | undefined>(undefined)

export const useMenuItem = () => {
  const context = useContext(MenuItemContext)
  if (context === undefined){
    throw new Error('useMenuItem must be used within a Menu Items Provider')
  }
  return context
}
