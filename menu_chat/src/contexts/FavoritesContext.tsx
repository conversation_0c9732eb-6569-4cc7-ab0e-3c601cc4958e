import React, { createContext, useContext } from 'react';
import { useFavorites } from '@/hooks/useFavorites'

type Favorite = {
  guid: string
  name: string
}

interface FavoritesContextType {
  favorites: Favorite[];
  addFavorite: (ingredient_guid: string) => Promise<void>,
  removeFavorite: (ingredient_guid: string) => Promise<void>,
  isFavorite: (favorites: Favorite[], ingredient_guid: string) => boolean,
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined)

interface FavoritesProviderProps {
  children: React.ReactNode;
}

export const FavoritesProvider = ({ children } : FavoritesProviderProps) => {
  const {favorites, addFavorite, removeFavorite, isFavorite} = useFavorites()
  const value: FavoritesContextType = {
    favorites: favorites?.data?.data || [],
    addFavorite,
    removeFavorite,
    isFavorite
  }

  return (<FavoritesContext.Provider value={value}>
    {children}
  </FavoritesContext.Provider>)
}

export const useFavoritesContext = () => {
  const context = useContext(FavoritesContext)
  if (context === undefined){
    throw new Error('useFavorites must be used within a FavoritesProvider')
  }
  return context
}