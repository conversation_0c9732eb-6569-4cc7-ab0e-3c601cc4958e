import { createContext, useContext, useState, ReactNode } from 'react';

type option = {
  label: string,
  value: string
}

interface DateToggle {
  selectedPeriod: string
  setSelectedPeriod: (date: string) => void
  options: option[]
}

const options: option[]  = [
  { label: "Last quarter", value: "Last quarter" },
  { label: "This quarter", value: "This quarter" },
]


export const DateToggleContext = createContext<DateToggle>({
  selectedPeriod: '',
  setSelectedPeriod: () => {},
  options: options
})

export const DateToggleProvider = ({children} : {children: ReactNode}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('Last quarter')
  return (
    <DateToggleContext.Provider value={{ selectedPeriod, setSelectedPeriod, options}}>
      {children}
    </DateToggleContext.Provider>
  )
}

export const useDateToggle = () => {
  const context = useContext(DateToggleContext)
  if (context === undefined){
    throw new Error('useDateToggle must be used within a DateToggle Provider')
  }
  return context
}

