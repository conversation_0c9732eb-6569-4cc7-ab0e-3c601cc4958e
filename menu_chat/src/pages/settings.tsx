import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import SettingsForm from "@/components/settings/settingsForm";

export default function SettingsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Settings</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Settings
            </Heading>
          </Header>
          <ScrollContainer>
            <SettingsForm />
          </ScrollContainer>
        </Main>
      </Layout>
    </>
  );
}
