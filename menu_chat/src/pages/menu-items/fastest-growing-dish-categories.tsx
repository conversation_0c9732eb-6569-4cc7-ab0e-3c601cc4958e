import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconMenuSolid } from "@/components/icons";
import Heading from "@/components/base/heading";
import FastestGrowingDishTable from "@/components/menu/industry-insights/fastest-growing-dish/fastestGrowingDishTable";

export default function MenuFastestGrowingPage() {
  return (
    <>
      <Head>
        <title>Fastest Growing Dish Categories</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Fastest Growing Dish Categories
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Menu Items",
                    icon: (
                      <IconMenuSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/menu-items",
                  },
                  {
                    label: "Fastest Growing Dish Categories",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <FastestGrowingDishTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
