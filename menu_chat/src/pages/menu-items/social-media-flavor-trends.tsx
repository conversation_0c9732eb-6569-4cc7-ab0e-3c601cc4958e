import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconMenuSolid } from "@/components/icons";
import MostTalkedAboutTable from "@/components/menu/socialmedia-insights/most-talked-about/mostTalkedAboutTable";

export default function MenuItemsSocialMediaFlavorTrendsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Social Media Flavor Trends</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Social Media Flavor Trends
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Menu Items",
                    icon: (
                      <IconMenuSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/menu-items",
                  },
                  {
                    label: "Social Media Flavor Trends",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <MostTalkedAboutTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
