import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconMenuSolid, IconIngredientsSolid } from "@/components/icons";
import Heading from "@/components/base/heading";
import { useRestaurantTypeNavigation } from "@/contexts/RestaurantTypeNavigationContext";
import { useRouter } from "next/router";
import titleCase from "voca/title_case";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchRestaurant } from "@/api/restaurants";
import { RestaurantContext } from "@/contexts/RestaurantContext";
import AllLimitedTimeOffers from "@/components/menu/restaurant/general/limited-time-offers/allLimitedTimeOffers";

export default function AllLimitedTimeOffersPage() {
  const { data: session } = useSession();
  const { navigationState } = useRestaurantTypeNavigation();
  const router = useRouter();
  const restaurantId = router.query.id as string;

  const { data: restaurant } = useQuery({
    queryFn: () => {
      return fetchRestaurant({
        auth: session?.user.authorization as string,
        guid: restaurantId,
      });
    },
    queryKey: ["restaurant", restaurantId],
    enabled: !!session?.user?.authorization && !!restaurantId,
  });

  const {
    fromRestaurantType,
    menuItem: menuItemName,
    menuItemGuid,
    restaurantType,
    restaurantName,
    source,
  } = navigationState;

  const formattedTitle = restaurant?.business_name
    ? titleCase(restaurant.business_name)
    : "Restaurant";

  // Build breadcrumb items based on navigation context
  const getBreadcrumbItems = () => {
    const baseItems = [
      {
        label: source === "ingredients" ? "Ingredients" : "Menu Items",
        icon:
          source === "ingredients" ? (
            <IconIngredientsSolid size={16} className="text-neutral-600" />
          ) : (
            <IconMenuSolid size={16} className="text-neutral-600" />
          ),
        link: source === "ingredients" ? "/ingredients" : "/menu-items",
      },
    ];

    if (
      fromRestaurantType &&
      menuItemGuid &&
      menuItemName &&
      restaurantType &&
      restaurantName
    ) {
      // Show full path: Ingredients/Menu Items > Ratatouille > QSR > McDonald's > All Limited Time Offers
      const basePath =
        source === "ingredients"
          ? "/ingredients/details"
          : "/menu-items/details";
      return [
        ...baseItems,
        {
          label: titleCase(menuItemName),
          link: `${basePath}/${menuItemGuid}`,
        },
        {
          label: titleCase(restaurantType),
          link: `${basePath}/${menuItemGuid}/${restaurantType.toLowerCase().replace(/\s+/g, "-")}`,
        },
        {
          label: restaurantName,
          link: `/menu-items/restaurant/${restaurantId}`,
        },
        {
          label: "All Limited Time Offers",
        },
      ];
    } else {
      // Default breadcrumb: Menu Items > Restaurant Name > All Limited Time Offers
      return [
        ...baseItems,
        {
          label: formattedTitle,
          link: `/menu-items/restaurant/${restaurantId}`,
        },
        {
          label: "All Limited Time Offers",
        },
      ];
    }
  };

  return (
    <>
      <Head>
        <title>All Limited Time Offers - {formattedTitle} - MenuData</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                All Limited Time Offers
              </Heading>
              <Breadcrumbs items={getBreadcrumbItems()} />
            </div>
          </Header>
          <ContentWrap>
            {restaurant && (
              <RestaurantContext.Provider value={restaurant}>
                <ScrollContainer>
                  <AllLimitedTimeOffers />
                </ScrollContainer>
              </RestaurantContext.Provider>
            )}
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
