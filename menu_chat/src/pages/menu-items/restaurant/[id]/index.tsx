import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconIngredientsSolid } from "@/components/icons";
import titleCase from "voca/title_case";
import RestaurantProfile from "@/components/menu/restaurant/restaurantProfile";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchRestaurant } from "@/api/restaurants";
import { RestaurantContext } from "@/contexts/RestaurantContext";

export default function MenuItemsRestaurantPage() {
  const { data: session } = useSession();
  const router = useRouter();

  const { data: restaurant } = useQuery({
    queryFn: () => {
      return fetchRestaurant({
        auth: session?.user.authorization as string,
        guid: router.query.id as string,
      });
    },
    queryKey: ["restaurant"],
    enabled: !!(session?.user?.authorization && router.query.id),
  });
  const name = restaurant?.business_name || router.query.id || "restaurant"; // Default value if id is not available
  const formattedTitle = titleCase(name.toString());

  return (
    <>
      <Head>
        <title>MenuData - {formattedTitle}</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Menu Items
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Menu Items",
                    icon: (
                      <IconIngredientsSolid
                        size={16}
                        className="text-neutral-600"
                      />
                    ),
                    link: "/menu-items",
                  },
                  ...(formattedTitle
                    ? [
                        {
                          label: formattedTitle,
                        },
                      ]
                    : []),
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            {restaurant &&
              <RestaurantContext.Provider value={restaurant}>
                <ScrollContainer>
                  <RestaurantProfile />
                </ScrollContainer>
              </RestaurantContext.Provider>
            }
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
