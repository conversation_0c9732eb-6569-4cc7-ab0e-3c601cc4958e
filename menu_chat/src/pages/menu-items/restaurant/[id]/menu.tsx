import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconMenuSolid, IconIngredientsSolid } from "@/components/icons";
import titleCase from "voca/title_case";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchRestaurant } from "@/api/restaurants";
import { RestaurantContext } from "@/contexts/RestaurantContext";
import { useRestaurantTypeNavigation } from "@/contexts/RestaurantTypeNavigationContext";
import RestaurantProfileMenu from "@/components/menu/restaurant/menu/restaurantProfileMenu";
import RestaurantDetailsMenu from "@/components/menu/restaurant/restaurantDetailsMenu";

export default function MenuItemsRestaurantPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const { navigationState } = useRestaurantTypeNavigation();

  const { data: restaurant } = useQuery({
    queryFn: () => {
      return fetchRestaurant({
        auth: session?.user.authorization as string,
        guid: router.query.id as string,
      });
    },
    queryKey: ["restaurant"],
    enabled: !!(session?.user?.authorization && router.query.id),
  });

  const id = router.query.id || "restaurant";

  const name = restaurant?.business_name || router.query.id || "restaurant"; // Default value if id is not available
  const formattedTitle = titleCase(name.toString());

  // Check if user came from restaurant type details page via context
  const fromRestaurantType = navigationState.fromRestaurantType;
  const menuItemGuid = navigationState.menuItemGuid;
  const menuItemName = navigationState.menuItem;
  const restaurantType = navigationState.restaurantType;
  const restaurantName = navigationState.restaurantName;
  const source = navigationState.source;

  // Build breadcrumb items based on navigation context
  const getBreadcrumbItems = () => {
    const baseItems = [
      {
        label: source === "ingredients" ? "Ingredients" : "Menu Items",
        icon:
          source === "ingredients" ? (
            <IconIngredientsSolid size={16} className="text-neutral-600" />
          ) : (
            <IconMenuSolid size={16} className="text-neutral-600" />
          ),
        link: source === "ingredients" ? "/ingredients" : "/menu-items",
      },
    ];

    if (
      fromRestaurantType &&
      menuItemGuid &&
      menuItemName &&
      restaurantType &&
      restaurantName
    ) {
      // Show full path: Ingredients/Menu Items > Ratatouille > QSR > McDonald's
      const basePath =
        source === "ingredients"
          ? "/ingredients/details"
          : "/menu-items/details";
      return [
        ...baseItems,
        {
          label: titleCase(menuItemName),
          link: `${basePath}/${menuItemGuid}`,
        },
        {
          label: titleCase(restaurantType),
          link: `${basePath}/${menuItemGuid}/${restaurantType.toLowerCase().replace(/\s+/g, "-")}`,
        },
        {
          label: restaurantName,
        },
      ];
    } else {
      // Default breadcrumb: Menu Items > Restaurant Name
      return [
        ...baseItems,
        {
          label: formattedTitle,
        },
      ];
    }
  };

  return (
    <>
      <Head>
        <title>MenuData - {formattedTitle}</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Breadcrumbs items={getBreadcrumbItems()} />
            </div>
          </Header>
          <ContentWrap>
            {restaurant && (
              <RestaurantContext.Provider value={restaurant}>
                <RestaurantDetailsMenu restaurantSlug={id?.toString() || ""} />
                <ScrollContainer className="h-[calc(100vh-80px)]">
                  <RestaurantProfileMenu />
                </ScrollContainer>
              </RestaurantContext.Provider>
            )}
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
