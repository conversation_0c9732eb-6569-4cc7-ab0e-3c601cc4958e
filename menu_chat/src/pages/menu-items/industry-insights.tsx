import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import MenuIndustryInsights from "@/components/menu/industry-insights/menuIndustryInsights";
// import Button from "@/components/base/button";

export default function MenuIndustryInsightsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Industry Insights</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Menu Items
            </Heading>
            {/* <Button>Create New Menu</Button> */}
          </Header>
          <ContentWrap>
            <MenuIndustryInsights />
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
