import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Button from "@/components/base/button";
import AllMenuItems from "@/components/menu/all/allMenuItems";

export default function MenuIndustryInsightsPage() {
  return (
    <>
      <Head>
        <title>MenuData - All Menu Items</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Menu Items
            </Heading>
            <Button>Create New Menu</Button>
          </Header>
          <ContentWrap>
            <AllMenuItems />
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
