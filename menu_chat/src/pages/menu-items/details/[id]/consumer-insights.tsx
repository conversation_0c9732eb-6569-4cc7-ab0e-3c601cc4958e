import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconIngredientsSolid } from "@/components/icons";
import MenuItemsDetailsMenu from "@/components/menu/details/menuItemsDetailsMenu";
import titleCase from "voca/title_case";
import MenuItemsDetailsConsumerInsights from "@/components/menu/details/consumer-insights/menuDetailsConsumerInsights";

export default function MenuItemsDetailsConsumerInsightsPage() {
  const router = useRouter();
  const id = router.query.id || "menu-item"; // Default value if id is not available

  const formattedTitle = titleCase(id.toString());

  return (
    <>
      <Head>
        <title>MenuData - {formattedTitle}</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Menu Items
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Menu Items",
                    icon: (
                      <IconIngredientsSolid
                        size={16}
                        className="text-neutral-600"
                      />
                    ),
                    link: "/menu-items",
                  },
                  {
                    label: formattedTitle,
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <MenuItemsDetailsMenu menuItemsSlug={id?.toString() || ""} />
            <ScrollContainer className="h-[calc(100vh-110px)]">
              <MenuItemsDetailsConsumerInsights />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
