import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconMenuSolid } from "@/components/icons";
import MenuItemsDetailsMenu from "@/components/menu/details/menuItemsDetailsMenu";
import titleCase from "voca/title_case";
import MenuItemsDetailsGeneral from "@/components/menu/details/general/menuDetailsGeneral";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMenuItem } from "@/api/menu_items";
import { MenuItemContext } from "@/contexts/MenuItemContext";

export default function MenuItemsDetailsGeneralPage() {
  const { data: session } = useSession();
  const router = useRouter();

  const { data: menu_item } = useQuery({
    queryFn: () => {
      return fetchMenuItem({
        auth: session?.user.authorization as string,
        guid: router.query.id as string,
      });
    },
    queryKey: ["menu_item"],
    enabled: !!(session?.user?.authorization && router.query.id),
  });

  const id = router.query.id || "menu-item"; // Default value if id is not available

  const formattedTitle = titleCase(menu_item?.name);

  return (
    <>
      <Head>
        <title>MenuData - {formattedTitle}</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Menu Items
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Menu Items",
                    icon: (
                      <IconMenuSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/menu-items",
                  },
                  ...(formattedTitle ? [{ label: formattedTitle }] : []),
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            {menu_item && (
              <MenuItemContext.Provider value={menu_item}>
                <MenuItemsDetailsMenu menuItemsSlug={id?.toString() || ""} />
                <ScrollContainer className="h-[calc(100vh-110px)]">
                  <MenuItemsDetailsGeneral />
                </ScrollContainer>
              </MenuItemContext.Provider>
            )}
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
