import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconMenuSolid } from "@/components/icons";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

import { MenuItemContext } from "@/contexts/MenuItemContext";
import titleCase from "voca/title_case";
import { useState } from "react";
import SocialMediaFilters from "@/components/menu/details/socialmedia-insights/socialMediaFilters";
import { fetchMenuItem } from "@/api/menu_items";
import AllMostEngagedSocialMediaPosts from "@/components/menu/details/socialmedia-insights/allMostEngagedSocialMediaPosts";

export default function AllSocialMediaPostsPage() {
  const { data: session } = useSession();

  const router = useRouter();
  const id = router.query.id || "ingredient"; // Default value if id is not available

  const { data: menuItem } = useQuery({
    queryFn: () => {
      return fetchMenuItem({
        auth: session?.user.authorization as string,
        guid: router.query.id as string,
      });
    },
    queryKey: ["menu_item"],
    enabled: !!(session?.user?.authorization && router.query.id),
  });

  const formattedTitle = titleCase(menuItem?.name || "");

  const [search, setSearch] = useState<string>("");

  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <>
      <Head>
        <title>MenuData - All Social Media Posts</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                All Social Media Posts
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Menu Items",
                    icon: (
                      <IconMenuSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/menu-items",
                  },
                  {
                    label: formattedTitle,
                    link: `/menu-items/details/${id}`,
                  },

                  {
                    label: "All Social Media Posts",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            {menuItem && (
              <MenuItemContext.Provider value={menuItem}>
                <SocialMediaFilters
                  search={search}
                  onSearch={setSearch}
                  filters={filters}
                  onFilter={handleFilterChange}
                  sort={sort}
                  onSort={handleSortChange}
                />
                <ScrollContainer>
                  <AllMostEngagedSocialMediaPosts />
                </ScrollContainer>
              </MenuItemContext.Provider>
            )}
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
