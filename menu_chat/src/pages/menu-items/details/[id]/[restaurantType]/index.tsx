import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconMenuSolid } from "@/components/icons";
import titleCase from "voca/title_case";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchMenuItem } from "@/api/menu_items";
import { MenuItemContext } from "@/contexts/MenuItemContext";
import RestaurantTypeDetails from "@/components/menu/details/distribution-overview/restaurant-type/restaurantTypeDetails";

enum RestaurantTypes {
  qsr = "QSR",
  "fast-casual" = "Fast Casual",
  "fine-dining" = "Fine Dining",
  "mid-scale" = "Mid Scale",
  "casual-dining" = "Casual Dining",
  other = "Other",
}

export default function MenuItemsRestaurantTypePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const { id, restaurantType } = router.query;

  const { data: menu_item } = useQuery({
    queryFn: () => {
      return fetchMenuItem({
        auth: session?.user.authorization as string,
        guid: id as string,
      });
    },
    queryKey: ["menu_item", id],
    enabled: !!(session?.user?.authorization && id),
  });

  const menuItemName = menu_item?.name || "Menu Item";
  const formattedMenuItemTitle = titleCase(menuItemName);
  const formattedRestaurantType =
    RestaurantTypes[restaurantType as keyof typeof RestaurantTypes] ||
    titleCase(restaurantType as string);

  return (
    <>
      <Head>
        <title>
          MenuData - {formattedMenuItemTitle} - {formattedRestaurantType}
        </title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                {formattedRestaurantType}
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Menu Items",
                    icon: (
                      <IconMenuSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/menu-items",
                  },
                  {
                    label: formattedMenuItemTitle,
                    link: `/menu-items/details/${id}`,
                  },
                  {
                    label: formattedRestaurantType,
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            {menu_item && (
              <MenuItemContext.Provider value={menu_item}>
                <ScrollContainer className="h-[calc(100vh-110px)]">
                  <RestaurantTypeDetails />
                </ScrollContainer>
              </MenuItemContext.Provider>
            )}
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
