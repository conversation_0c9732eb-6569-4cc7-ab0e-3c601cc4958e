import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
// import Button from "@/components/base/button";
import MenuSocialMedaInsights from "@/components/menu/socialmedia-insights/menuConsumerInsghts";

export default function MenuIndustryInsightsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Social Media Insights</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Menu Items
            </Heading>
            {/* <Button>Create New Menu</Button> */}
          </Header>
          <ContentWrap>
            <MenuSocialMedaInsights />
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
