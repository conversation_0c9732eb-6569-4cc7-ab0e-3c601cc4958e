import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import MenuConsumerInsights from "@/components/menu/consumer-insights/menuConsumerInsights";
import Button from "@/components/base/button";

export default function MenuConsumerInsightsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Consumer Insights</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Menu Items
            </Heading>
            <Button>Create New Menu</Button>
          </Header>
          <ContentWrap>
            <MenuConsumerInsights />
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
