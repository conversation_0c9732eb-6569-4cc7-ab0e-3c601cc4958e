import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconMenuSolid } from "@/components/icons";
import Heading from "@/components/base/heading";
import GeographicPopularityTable from "@/components/menu/consumer-insights/geographic-popularity/geographicPopularityTable";

export default function MenuGeographicPopularityPage() {
  return (
    <>
      <Head>
        <title>Menu Items Geographic Popularity</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Menu Items Geographic Popularity
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Menu Items",
                    icon: (
                      <IconMenuSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/menu-items",
                  },
                  {
                    label: "Menu Items Geographic Popularity",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <GeographicPopularityTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
