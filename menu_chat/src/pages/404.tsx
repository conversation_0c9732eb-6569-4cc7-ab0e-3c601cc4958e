import Head from "next/head";
import Layout from "@/components/layout/layout";
import Logo from "@/components/base/logo";
import Heading from "@/components/base/heading";
import Link from "next/link";
import Button from "@/components/base/button";
import { IconPreviousBox } from "@/components/icons";

export default function Custom404() {
  return (
    <>
      <Head>
        <title>MenuData - 404: Page Not Found</title>
      </Head>
      <Layout>
        <div className="relative h-screen overflow-hidden py-24">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="481"
            height="481"
            fill="none"
            className="absolute -top-[150px] -left-[150px]"
          >
            <path
              fill="url(#a)"
              fill-opacity=".6"
              d="M120.846 33c114.79-66.275 261.572-26.945 327.846 87.845 66.274 114.791 26.944 261.572-87.846 327.846C246.056 514.966 99.274 475.636 33 360.845-33.274 246.055 6.056 99.273 120.846 32.999Z"
            />
            <defs>
              <linearGradient
                id="a"
                x1="410.275"
                x2="71.401"
                y1="391.002"
                y2="338.808"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#DFDDCE" />
                <stop offset="1" stop-color="#F0EDE6" stop-opacity="0" />
              </linearGradient>
            </defs>
          </svg>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="640"
            height="640"
            fill="none"
            className="absolute -right-[200px] -bottom-[200px]"
          >
            <path
              fill="url(#a)"
              fill-opacity=".6"
              d="M0 320C0 143.269 143.269 0 320 0S640 143.27 640 320 496.731 640 320 640 0 496.731 0 320Z"
            />
            <defs>
              <linearGradient
                id="a"
                x1="606.339"
                x2="320.154"
                y1="224.465"
                y2="580.967"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#CFCCB4" />
                <stop offset="1" stop-color="#F0EDE6" stop-opacity="0" />
              </linearGradient>
            </defs>
          </svg>
          <div className="relative z-50 mx-auto">
            <div className="flex justify-center">
              <Logo className="text-muted-900 mb-12 px-8 py-7 lg:mb-32" />
            </div>
            <div className="px-8 py-7 text-center">
              <Heading level={1} className="mb-1">
                <span className="mb-2 block text-[32px] text-neutral-600">
                  404
                </span>
                Page is not found
              </Heading>
              <div className="mt-10 flex items-center justify-center gap-2">
                <Link href="/">
                  <Button
                    size="lg"
                    variant="secondary"
                    className="min-w-40 bg-transparent hover:bg-yellow-500/15"
                  >
                    <IconPreviousBox />
                    Go to Home
                  </Button>
                </Link>
                <Link href="/login">
                  <Button size="lg" variant="primary" className="min-w-40">
                    Login
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
}
