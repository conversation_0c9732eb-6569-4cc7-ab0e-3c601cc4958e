import Head from "next/head";
import Layout from "@/components/layout/layout";
import LoginWrap from "@/components/login/loginWrap";
import Image from "next/image";
import Logo from "@/components/base/logo";
import { OnboardingStart } from "@/components/onboarding/onboardingStart";

export default function Onboarding() {
  return (
    <>
      <Head>
        <title>MenuData - Onboarding</title>
      </Head>
      <Layout>
        <LoginWrap>
          <div className="mx-auto max-w-[528px] p-8">
            <Logo className="text-muted-900 mt-24 mb-[152px] flex justify-start" />
            <OnboardingStart />
          </div>
          <div className="p-2.5">
            <div className="relative w-full overflow-hidden rounded-lg lg:h-[calc(100vh-20px)]">
              <Image
                src="/assets/images/<EMAIL>"
                width={700}
                height={950}
                alt="login background"
                className="top-0 right-0 bottom-0 left-0 w-full object-cover object-center lg:absolute lg:h-full"
              />
            </div>
          </div>
        </LoginWrap>
      </Layout>
    </>
  );
}
