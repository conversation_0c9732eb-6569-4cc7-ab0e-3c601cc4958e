import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import AllSearchResults from "@/components/search/allSearchResults";
import SearchModal from "@/components/search/searchModal";

export default function SearchResultsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Search Results</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <SearchModal />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              All Results
            </Heading>
          </Header>
          <ScrollContainer>
            <AllSearchResults />
          </ScrollContainer>
        </Main>
      </Layout>
    </>
  );
}
