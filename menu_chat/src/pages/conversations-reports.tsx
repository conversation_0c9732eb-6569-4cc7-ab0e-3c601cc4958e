import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import LatestConversations from "@/components/conversations-reports/latestConversations";
import ReportsResources from "@/components/conversations-reports/reportsResources";

export default function ConversationsReportsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Conversations & Reports</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Conversations & Reports
            </Heading>
          </Header>
          <ScrollContainer className="space-y-5 pt-4">
            <LatestConversations />
            <ReportsResources />
          </ScrollContainer>
        </Main>
      </Layout>
    </>
  );
}
