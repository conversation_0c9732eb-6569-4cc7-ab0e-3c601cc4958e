import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconReportSolid } from "@/components/icons";

import { useState } from "react";
import AllLatestConversations from "@/components/conversations-reports/all/allLatestConversations";
import AllLatestConversationsFilters from "@/components/conversations-reports/all/conversationsReportsFilters";

export default function AllSocialMediaPostsPage() {
  const [search, setSearch] = useState<string>("");

  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <>
      <Head>
        <title>MenuData - All Conversations</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Conversations & Reports
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Conversations & Reports",
                    icon: (
                      <IconReportSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/conversations-reports",
                  },
                  {
                    label: "All Conversations",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <AllLatestConversationsFilters
              search={search}
              onSearch={setSearch}
              filters={filters}
              onFilter={handleFilterChange}
              sort={sort}
              onSort={handleSortChange}
            />
            <ScrollContainer className="mt-3 pb-27">
              <AllLatestConversations />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
