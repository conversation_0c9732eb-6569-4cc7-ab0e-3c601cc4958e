import Head from "next/head";
import Layout from "@/components/layout/layout";
import LoginWrap from "@/components/login/loginWrap";
import Image from "next/image";
import Logo from "@/components/base/logo";
import ForgotPasswordForm from "@/components/login/forgotPasswordForm";

export default function ForgotPassword() {
  return (
    <>
      <Head>
        <title>MenuData - Forgot Password</title>
      </Head>
      <Layout>
        <LoginWrap>
          <div className="0 flex flex-col items-center justify-center">
            <div className="mx-auto max-w-[528px]">
              <Logo className="text-muted-900 px-8 py-7 lg:mb-24" />
              <ForgotPasswordForm />
            </div>
          </div>
          <div className="p-2.5">
            <div className="relative w-full overflow-hidden rounded-lg lg:h-[calc(100vh-20px)]">
              <Image
                src="/assets/images/<EMAIL>"
                width={700}
                height={950}
                alt="login background"
                className="top-0 right-0 bottom-0 left-0 w-full object-cover object-center lg:absolute lg:h-full"
              />
            </div>
          </div>
        </LoginWrap>
      </Layout>
    </>
  );
}
