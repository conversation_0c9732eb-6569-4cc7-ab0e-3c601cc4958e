import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import PreviousReports from "@/components/previous-reports/previousReports";

export default function PreviousReportsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Previous Reports</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Previous Reports
            </Heading>
          </Header>
          <ScrollContainer>
            <PreviousReports />
          </ScrollContainer>
        </Main>
      </Layout>
    </>
  );
}
