import NextAuth, { NextAuthOptions } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: "<PERSON>rna<PERSON>", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        const credentialsWithEmail = credentials as Record<"email" | "password", string>
        const requestBody = {
          user: {
            email: credentialsWithEmail?.email,
            password: credentialsWithEmail?.password
          }
        }

        const res = await fetch(`${process.env.MENU_BASE_URL}/users/sign_in`, {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { "Content-Type": "application/json" }
        })
        const data = await res.json()

        if (res.ok && data) {
          const authorization = res.headers.get('authorization')
          return {...data, email: data.user.email, authorization: authorization}
        }
        return null
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 3 * 24 * 60 * 60
  },
  callbacks: {
    async session({ session, token }) {
      if (session.user) {
        session.user.email = token.email
        session.user.authorization = token.authorization as string;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.email = user.email
        token.authorization = user.authorization;
      }
      return token;
    }
  }
}

export default NextAuth(authOptions)
