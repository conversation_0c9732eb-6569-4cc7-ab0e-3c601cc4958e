import type { NextApiRequest, NextApiResponse } from 'next';

interface PresentationRequest {
  prompt: string;
  n_slides: number;
  language: string;
  template: string;
  export_as: string;
}

interface PresentationResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PresentationResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { prompt, n_slides, language, template, export_as }: PresentationRequest = req.body;

    // Validate required fields
    if (!prompt || !n_slides || !language || !template || !export_as) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: prompt, n_slides, language, template, export_as'
      });
    }

    // Make the request to the external service
    const response = await fetch('http://localhost:5000/api/v1/ppt/presentation/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        n_slides,
        language,
        template,
        export_as
      }),
    });

    if (!response.ok) {
      throw new Error(`External service error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    return res.status(200).json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in presentation generation:', error);
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}
