import type { NextApiRequest, NextApiResponse } from 'next';

interface SlideContent {
  [key: string]: any;
}

interface SlideData {
  layout: string;
  content: SlideContent;
}

interface CreateFromJsonRequest {
  title: string;
  template: string;
  export_as: string;
  slides: SlideData[];
}

interface PresentationResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PresentationResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { title, template, export_as, slides }: CreateFromJsonRequest = req.body;

    // Validate required fields
    if (!title || !template || !export_as || !slides || !Array.isArray(slides)) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: title, template, export_as, and slides array'
      });
    }

    // Make the request to the external service create-from-json endpoint
    const response = await fetch('https://api.presenton.ai/api/v1/ppt/presentation/create/from-json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-presenton-17ebd2ff533523c4f99494ffe69d7cdaa29cbd25197036d6054a13347bce157182ff64b0ee77759153f5f521127101890d1752d7f371e1aa811df48d10536e4d'
      },
      body: JSON.stringify({
        title,
        template,
        export_as,
        slides
      }),
    });

    if (!response.ok) {
      throw new Error(`External service error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    return res.status(200).json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error in presentation creation from JSON:', error);
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}
