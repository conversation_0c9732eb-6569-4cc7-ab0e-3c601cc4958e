import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { path } = req.query;

  if (!path || typeof path !== 'string') {
    return res.status(400).json({ error: 'File path is required' });
  }

  try {
    // Fetch the file from the external service
    const fileResponse = await fetch(`http://localhost:5001${path}`);

    if (!fileResponse.ok) {
      throw new Error(`Failed to fetch file: ${fileResponse.status} ${fileResponse.statusText}`);
    }

    // Get the file buffer
    const fileBuffer = await fileResponse.arrayBuffer();
    
    // Extract filename from path
    const filename = path.split('/').pop() || 'presentation.pptx';
    
    // Set appropriate headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', fileBuffer.byteLength);

    // Send the file
    res.send(Buffer.from(fileBuffer));

  } catch (error) {
    console.error('Error downloading file:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}
