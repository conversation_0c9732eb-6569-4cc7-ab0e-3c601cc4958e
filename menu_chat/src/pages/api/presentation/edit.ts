import type { NextApiRequest, NextApiResponse } from 'next';

interface EditSlideContent {
  title?: string;
  text?: string;
  bullets?: string[];
  [key: string]: any;
}

interface EditSlideData {
  index: number;
  content: EditSlideContent;
}

interface EditPresentationRequest {
  presentation_id: string;
  data: EditSlideData[];
  export_as?: string;
}

interface PresentationResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PresentationResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { presentation_id, data, export_as }: EditPresentationRequest = req.body;

    // Validate required fields
    if (!presentation_id || !data || !Array.isArray(data)) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: presentation_id and data array'
      });
    }

    // Make the request to the external service edit endpoint
    const response = await fetch('http://localhost:5001/api/v1/ppt/presentation/from-template', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        presentation_id,
        data,
        export_as: export_as || 'pptx'
      }),
    });

    if (!response.ok) {
      throw new Error(`External service error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    return res.status(200).json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error in presentation editing:', error);
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}
