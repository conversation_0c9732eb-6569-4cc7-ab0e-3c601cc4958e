import Heading from "@/components/base/heading";
import Header from "@/components/layout/header";
import Layout from "@/components/layout/layout";
import Main from "@/components/layout/main";
import Navigation from "@/components/layout/navigation/navigation";
import ScrollContainer from "@/components/layout/scrollContainer";
import SearchModal from "@/components/search/searchModal";
import Head from "next/head";
import TextSearchResults from "@/components/search/text-results/textSearchResults";
import SearchMenu from "@/components/search/text-results/searchMenu";
import ContentWrap from "@/components/layout/contentWrap";

export default function AllTextResultsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Text Results</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <SearchModal />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Text Results
            </Heading>
          </Header>
          <ContentWrap>
            <SearchMenu />
            <ScrollContainer>
              <TextSearchResults />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
