import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
//import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
//import Heading from "@/components/base/heading";
//import InsightHighlights from "@/components/home/<USER>";
//import Link from "next/link";
import MenuBotPanel from "@/components/chat/menuBotPanel";
// import { useSession } from "next-auth/react";

export default function Home() {
  // const { data: session } = useSession();
  return (
    <>
      <Head>
        <title>MenuData - Home</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          {/* <Header>
            <Heading level={2}>Home</Heading>
            {!!session?.user?.authorization ?
              `${session?.user?.email}` :
              <Link href={"/login"}> Sign In</Link>
            }
          </Header> */}
          <ScrollContainer className="flex h-full flex-col justify-center p-4">
            {/* <InsightHighlights /> */}
            <MenuBotPanel />
          </ScrollContainer>
        </Main>
      </Layout>
    </>
  );
}
