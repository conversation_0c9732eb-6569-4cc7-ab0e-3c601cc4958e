import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconRetailSolid } from "@/components/icons";
import MostTalkedAboutTable from "@/components/retail/socialmedia-insights/most-talked-about/mostTalkedAboutTable";

export default function RetailSocialMediaFlavorTrendsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Social Media Flavor Trends</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Social Media Flavor Trends
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Retail",
                    icon: (
                      <IconRetailSolid
                        size={16}
                        className="relative -top-1 text-neutral-600"
                      />
                    ),
                    link: "/retail",
                  },
                  {
                    label: "Social Media Flavor Trends",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <MostTalkedAboutTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
