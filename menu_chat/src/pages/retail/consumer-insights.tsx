import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import RetailMenu from "@/components/retail/retailMenu";
import RetailConsumerInsights from "@/components/retail/consumer-insights/retailConsumerInsights";

export default function RetailIndustryInsightsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Consumer Insights</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Retail
            </Heading>
          </Header>
          <ContentWrap>
            <RetailMenu />
            <ScrollContainer>
              <RetailConsumerInsights />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
