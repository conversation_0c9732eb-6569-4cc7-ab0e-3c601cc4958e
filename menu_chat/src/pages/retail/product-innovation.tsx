import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconRetailSolid } from "@/components/icons";
import TopProductInnovationTable from "@/components/retail/industry-insights/top-product-innovation/topProductInnovationTable";

export default function RetailFastestGrowingProductCategoriesPage() {
  return (
    <>
      <Head>
        <title>MenuData - Products Innovation</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Products Innovation
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Retail",
                    icon: (
                      <IconRetailSolid
                        size={16}
                        className="relative -top-1 text-neutral-600"
                      />
                    ),
                    link: "/retail",
                  },
                  {
                    label: "Products Innovation",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <TopProductInnovationTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
