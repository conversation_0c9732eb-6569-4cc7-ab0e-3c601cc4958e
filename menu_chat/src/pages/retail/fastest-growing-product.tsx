import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconRetailSolid } from "@/components/icons";
import FastestGrowingRetailTable from "@/components/retail/industry-insights/fastest-growing-product/fastestGrowingProductTable";

export default function RetailFastestGrowingProductCategoriesPage() {
  return (
    <>
      <Head>
        <title>MenuData - Fastest Growing Product Categories</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Fastest Growing Product Categories
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Retail",
                    icon: (
                      <IconRetailSolid
                        size={16}
                        className="relative -top-1 text-neutral-600"
                      />
                    ),
                    link: "/retail",
                  },
                  {
                    label: "Fastest Growing Product Categories",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <FastestGrowingRetailTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
