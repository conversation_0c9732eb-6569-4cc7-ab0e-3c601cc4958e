import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconRetailSolid } from "@/components/icons";
import MostLikedProductsTable from "@/components/retail/consumer-insights/most-liked-products/mostLikedProductsTable";

export default function RetailMostLikedProductsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Most Liked Products</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Most Liked Products
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Retail",
                    icon: (
                      <IconRetailSolid
                        size={16}
                        className="relative -top-1 text-neutral-600"
                      />
                    ),
                    link: "/retail",
                  },
                  {
                    label: "Most Liked Products",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <MostLikedProductsTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
