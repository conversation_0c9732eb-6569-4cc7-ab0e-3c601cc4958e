import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconRetailSolid } from "@/components/icons";
import titleCase from "voca/title_case";
import RetailDetailsMenu from "@/components/retail/details/retailDetailsMenu";
import RetailDetailsConsumerInsights from "@/components/retail/details/consumer-insights/retailDetailsConsumerInsights";

export default function RetailDetailsConsumerInsightsPage() {
  const router = useRouter();
  const id = router.query.id || "menu-item"; // Default value if id is not available

  const formattedTitle = titleCase(id.toString());

  return (
    <>
      <Head>
        <title>MenuData - {formattedTitle}</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Retail
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Retail",
                    icon: (
                      <IconRetailSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/retail",
                  },
                  {
                    label: formattedTitle,
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <RetailDetailsMenu RetailSlug={id.toString()} />
            <ScrollContainer className="h-[calc(100vh-110px)]">
              <RetailDetailsConsumerInsights />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
