import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconRetailSolid } from "@/components/icons";
import titleCase from "voca/title_case";
import RetailDetailsMenu from "@/components/retail/details/retailDetailsMenu";
import RetailDetailsOnShelf from "@/components/retail/details/on-shelf/retailDetailsOnShelf";
import { fetchRetailProduct } from "@/api/retailer_products";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { RetailProductContext } from "@/contexts/RetailProductContext";

export default function RetailDetailsOnShelfPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const id = router.query.id as string;

  const {
    data: retailProduct,
    error,
    isLoading,
  } = useQuery({
    queryFn: () => {
      if (!id || Array.isArray(id)) throw new Error("Invalid product ID");
      return fetchRetailProduct({
        auth: session?.user.authorization as string,
        id,
      });
    },
    queryKey: ["retail_product", id],
    enabled: !!(session?.user?.authorization && id && !Array.isArray(id)),
  });

  const formattedTitle = retailProduct
    ? titleCase(retailProduct.name)
    : titleCase(id || "Retail");

  if (isLoading)
    return (
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Retail
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Retail",
                    icon: (
                      <IconRetailSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/retail",
                  },
                  { label: "Loading..." },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <div className="ml-8">Loading...</div>
          </ContentWrap>
        </Main>
      </Layout>
    );

  if (error) return <div>Error loading retail details</div>;

  return (
    <>
      <Head>
        <title>MenuData - {formattedTitle}</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Retail
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Retail",
                    icon: (
                      <IconRetailSolid size={16} className="text-neutral-600" />
                    ),
                    link: "/retail",
                  },
                  { label: formattedTitle },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            {retailProduct && (
              <RetailProductContext.Provider value={retailProduct}>
                <RetailDetailsMenu RetailSlug={id} />
                <ScrollContainer className="h-[calc(100vh-110px)]">
                  <RetailDetailsOnShelf />
                </ScrollContainer>
              </RetailProductContext.Provider>
            )}
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
