import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import RetailMenu from "@/components/retail/retailMenu";
import RetailIndustryInsights from "@/components/retail/industry-insights/retailIndustryInsights";

export default function RetailIndustryInsightsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Industry Insights</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Retail
            </Heading>
          </Header>
          <ContentWrap>
            <RetailMenu />
            <ScrollContainer>
              <RetailIndustryInsights />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
