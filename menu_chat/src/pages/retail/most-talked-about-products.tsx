import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ContentWrap from "@/components/layout/contentWrap";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconRetailSolid } from "@/components/icons";
import MostTalkedAboutProductsTable from "@/components/retail/consumer-insights/most-talked-about-products/mostTalkedAboutProductsTable";

export default function RetailMostTalkedAboutProductsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Most Talked About Products</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Most Talked About Products
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Retail",
                    icon: (
                      <IconRetailSolid
                        size={16}
                        className="relative -top-1 text-neutral-600"
                      />
                    ),
                    link: "/retail",
                  },
                  {
                    label: "Most Talked About Products",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <MostTalkedAboutProductsTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
