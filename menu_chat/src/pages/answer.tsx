import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import {
  IconBinoculars,
  IconSparkleSolid,
  IconHistory,
} from "@/components/icons";
import Tabs, { TabContent } from "@/components/base/tabs";
import ChatRenderer from "@/components/chat/ChatRenderer";
import AnswersActions from "@/components/answers/answersActions";
import AnswersRelatedQuestions from "@/components/answers/answersRelatedQuestions";
// import MenuBotInput from "@/components/chat/menuBotInput";
import Button from "@/components/base/button";
import { useAppStore } from "@/store/useAppStore";
import { motion } from "motion/react";
import { fetchChat, fetchChatMessages, createChatMessage } from "@/api/chats";
import { authorizedConsumer } from "@/lib/chat-consumer";
import FloatingChatInput from "@/components/chat/floatingChatInput";
import AnswersSources from "@/components/answers/answersSources";
import { defaultQuestions, IngredientQuestion } from "@/data/ingredients";
// import AnswersExample from "@/components/answers/answersExample";

// TODO: Replace with actual data
const suggestionsData: IngredientQuestion[] = defaultQuestions;

// Use the same Message type as ChatRenderer
interface Message {
  id: number;
  message_type: string;
  message: string;
  stream_data?: Record<string, unknown>;
  chat_id: number;
  created_at: string;
  updated_at: string;
  severity?: number;
  hideInRenderer?: boolean;
}

export default function Answer() {
  const router = useRouter();
  const { guid } = router.query;
  const { navCollapsed: collapsed } = useAppStore();
  const [question, setQuestion] = useState<string>("");
  const [chat, setChat] = useState<Partial<ChatPayload>>({});
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { data: session } = useSession();

  // If there's no guid, display a placeholder or redirect
  useEffect(() => {
    if (router.isReady && !guid) {
      // Option 1: Redirect to homepage
      // router.push('/');

      // Option 2: Show a placeholder message (we'll use this approach)
      console.log("No guid provided, showing placeholder");
    }
  }, [router.isReady, guid, router]);

  // Don't attempt to fetch data if there's no guid
  const hasGuid = !!guid;

  // const handleQuestionChange = (
  //   event: React.ChangeEvent<HTMLTextAreaElement>,
  // ) => {
  //   setQuestion(event.target.value);
  // };

  // Function to process links in message text
  const processLinks = (text: string) => {
    if (!text) return "";
    return text.replace(
      /<a\s+href="([^"]+)"[^>]*>([^<]+)<\/a>/g,
      (_, href, linkText) =>
        `<a href="${href}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">${linkText}</a>`,
    );
  };

  useEffect(() => {
    async function getData() {
      if (session?.user?.authorization && hasGuid) {
        const response = await fetchChat(
          session.user.authorization,
          guid as string,
        );
        setChat(response?.data);
      }
    }
    getData();
  }, [session, guid, hasGuid]);

  useEffect(() => {
    async function getMessages() {
      if (session?.user?.authorization && hasGuid) {
        const response = await fetchChatMessages(
          session.user.authorization,
          guid as string,
        );
        if (response?.data) {
          const messages = response.data;

          // // Find the first user message and mark it to be hidden in the renderer
          // const firstUserMessageIndex = messages.findIndex((m: Message) => m.message_type === 'user');
          // if (firstUserMessageIndex >= 0) {
          //   messages = messages.map((msg: Message, index: number) => {
          //     if (index === firstUserMessageIndex) {
          //       return { ...msg, hideInRenderer: true };
          //     }
          //     return msg;
          //   });
          // }

          setMessages(messages);

          // Find the last AI message
          const lastAiMessage = [...messages]
            .reverse()
            .find((m) => m.message_type === "ai");
          const lastMessage = messages[messages.length - 1];
          // Show loading if:
          // 1. There are no messages, or
          // 2. The last message is from the user, or
          // 3. The last AI message is empty/incomplete
          const shouldShowLoading =
            !lastMessage ||
            lastMessage.message_type === "user" ||
            (lastAiMessage &&
              (!lastAiMessage.message || lastAiMessage.message.trim() === ""));

          setIsLoading(shouldShowLoading);
        }
      }
    }
    getMessages();
  }, [session, guid, hasGuid]);

  useEffect(() => {
    if (chat?.chat?.id && hasGuid) {
      const subscription = authorizedConsumer(
        session?.user.authorization as string,
      ).subscriptions.create(
        { channel: "ChatChannel", room_id: chat.chat.id },
        {
          connected() {
            console.log("connected to websocket");
          },
          received(data) {
            if (data.type === "streaming_update" && data.message) {
              setMessages((prevMessages) => {
                const lastMessage = prevMessages[prevMessages.length - 1];

                // Only clear loading when we have a complete message with content
                const messageContent = data.message.message || "";
                const isComplete =
                  messageContent.trim().length > 0 &&
                  !messageContent.endsWith("...");

                if (isComplete) {
                  setIsLoading(false);
                } else {
                  setIsLoading(true);
                }

                if (!lastMessage || lastMessage.id !== data.message.id) {
                  return [
                    ...prevMessages,
                    {
                      ...data.message,
                      message: processLinks(data.message.message),
                      severity: 1,
                    },
                  ];
                }

                return prevMessages.map((msg) =>
                  msg.id === data.message.id
                    ? { ...msg, message: processLinks(data.message.message) }
                    : msg,
                );
              });
            }
          },
        },
      );

      return () => {
        if (subscription && subscription.unsubscribe) {
          subscription.unsubscribe();
        }
      };
    }
  }, [chat, session?.user.authorization, hasGuid]);

  async function createMessage(query: string) {
    if (!session?.user?.authorization || !hasGuid) {
      return;
    }
    if (!query.trim()) {
      return;
    }

    try {
      setIsLoading(true);

      // Add the user's message immediately
      const userMessage: Message = {
        id: Date.now(),
        message_type: "user",
        message: query,
        chat_id: chat.chat?.id || 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        severity: 1,
        hideInRenderer: messages.length === 0, // Hide if this is the first message
      };
      setMessages((prevMessages) => [...prevMessages, userMessage]);

      // Send the message to the server
      await createChatMessage(
        session.user.authorization,
        guid as string,
        query,
      );
    } catch (error) {
      console.error("Error in createMessage:", error);
      setIsLoading(false);
    }
  }

  const chatQuestion =
    messages.find((m) => m.message_type === "user")?.message ||
    "Chat with MenuData AI";
  const trimmedQuestion =
    chatQuestion.length > 50
      ? chatQuestion.substring(0, 47) + "..."
      : chatQuestion;

  // Show a placeholder if there's no guid
  if (!hasGuid && router.isReady) {
    return (
      <>
        <Head>
          <title>MenuData - Answer</title>
        </Head>
        <Layout withSideNav>
          <Navigation />
          <Main>
            <Header>
              <div className="w-full">
                <Breadcrumbs
                  items={[
                    {
                      label: "Home",
                      icon: (
                        <IconBinoculars
                          size={16}
                          className="text-neutral-600"
                        />
                      ),
                      link: "/",
                    },
                    {
                      label: "New Conversation",
                    },
                  ]}
                />
              </div>
            </Header>
            <ScrollContainer>
              <div className="relative mx-auto flex min-h-[calc(100vh-90px)] w-full max-w-[670px] flex-col items-center justify-center px-4 pt-5 pb-44">
                <Heading level={2} className="mb-5">
                  Start a new conversation
                </Heading>
                <p className="mb-6 max-w-md text-center">
                  Ask MenuData AI a question to get started or explore previous
                  conversations from your history.
                </p>
                <Button onClick={() => router.push("/")} variant="primary">
                  Go to Home
                </Button>
              </div>
            </ScrollContainer>
          </Main>
        </Layout>
      </>
    );
  }

  const finalMessageList = messages.filter((msg) => !msg.hideInRenderer);
  return (
    <>
      <Head>
        <title>MenuData - Answer</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div className="w-full">
              <Breadcrumbs
                items={[
                  {
                    label: "Home",
                    icon: (
                      <IconBinoculars size={16} className="text-neutral-600" />
                    ),
                    link: "/",
                  },
                  {
                    label: trimmedQuestion,
                  },
                ]}
              />
            </div>
          </Header>
          <ScrollContainer>
            <div className="relative mx-auto flex min-h-[calc(100vh-90px)] w-full max-w-[774px] flex-col px-4 pt-5 pb-44">
              {finalMessageList.map((msg, index) => {
                if (msg.message_type === "user") {
                  return (
                    <React.Fragment key={msg.id}>
                      <Heading level={2} className="mb-5">
                        {msg.message}
                      </Heading>
                    </React.Fragment>
                  );
                } else {
                  return (
                    <React.Fragment key={msg.id}>
                      <Tabs
                        navigation={[
                          <>
                            <IconSparkleSolid size={16} /> Answer
                          </>,
                          <>
                            <IconHistory size={16} /> Sources
                          </>,
                        ]}
                      >
                        <TabContent>
                          <div className="flex flex-1 flex-col justify-between gap-5">
                            <div className="flex w-full flex-col items-start gap-5 font-medium">
                              <div className="w-full whitespace-pre-wrap">
                                <ChatRenderer
                                  messages={[msg]}
                                  isLoading={isLoading}
                                />
                              </div>
                            </div>
                          </div>
                        </TabContent>
                        <TabContent>
                          <AnswersSources />
                        </TabContent>
                      </Tabs>
                      {index < finalMessageList.length - 1 && (
                        <div className="my-6 border-t border-neutral-200" />
                      )}
                    </React.Fragment>
                  );
                }
              })}
              {!isLoading && (
                <AnswersActions message="Let me know if I can help with anything else?" />
              )}
              {!isLoading && (
                <AnswersRelatedQuestions
                  questions={suggestionsData}
                  onSelect={(selectedQuestion) => {
                    setQuestion(selectedQuestion);
                    if (selectedQuestion.trim()) {
                      void createMessage(selectedQuestion);
                    }
                  }}
                />
              )}
              <motion.div
                initial={false}
                animate={{
                  left: collapsed
                    ? "var(--nav-collapsed-width)"
                    : "var(--nav-width)",
                }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
                className="fixed right-0 bottom-5 z-10 mx-auto w-full max-w-[774px] px-4 pt-5"
              >
                <FloatingChatInput
                  initialValue={question}
                  onSendMessage={(message) => {
                    if (message.trim()) {
                      void createMessage(message);
                    }
                  }}
                />
              </motion.div>
            </div>
          </ScrollContainer>
        </Main>
      </Layout>
    </>
  );
}
