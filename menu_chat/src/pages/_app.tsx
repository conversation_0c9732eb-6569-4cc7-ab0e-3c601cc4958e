import "@/styles/globals.css";
import type { AppProps } from "next/app";
import { SessionProvider } from "next-auth/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AppProvider } from "@/store/useAppStore";

const queryClient = new QueryClient();
import { FavoritesProvider } from "@/contexts/FavoritesContext";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider session={pageProps.session}>
        <AppProvider>
          <FavoritesProvider>
            <Component {...pageProps} />
          </FavoritesProvider>
        </AppProvider>
      </SessionProvider>
    </QueryClientProvider>
  );
}
