import "@/styles/globals.css";
import type { AppProps } from "next/app";
import { SessionProvider } from "next-auth/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AppProvider } from "@/store/useAppStore";

const queryClient = new QueryClient();
import { FavoritesProvider } from "@/contexts/FavoritesContext";
import { DateToggleProvider } from "@/contexts/DateToggleContext";
import { RestaurantTypeNavigationProvider } from "@/contexts/RestaurantTypeNavigationContext";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider session={pageProps.session}>
        <AppProvider>
          <DateToggleProvider>
            <FavoritesProvider>
              <RestaurantTypeNavigationProvider>
                <Component {...pageProps} />
              </RestaurantTypeNavigationProvider>
            </FavoritesProvider>
          </DateToggleProvider>
        </AppProvider>
      </SessionProvider>
    </QueryClientProvider>
  );
}
