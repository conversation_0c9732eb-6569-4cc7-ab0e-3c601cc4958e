import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconIngredientsSolid } from "@/components/icons";
import { fetchIngredient } from "@/api/ingredients";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

import { IngredientContext } from "@/contexts/IngredientContext";
import titleCase from "voca/title_case";
import { useState } from "react";
import AllMostEngagedSocialMediaPosts from "@/components/ingredients/details/social-media/allMostEngagedSocialMediaPosts";
import SocialMediaFilters from "@/components/ingredients/details/social-media/socialMediaFilters";

export default function AllSocialMediaPostsPage() {
  const { data: session } = useSession();

  const router = useRouter();
  const id = router.query.id || "ingredient"; // Default value if id is not available

  const { data: ingredient } = useQuery({
    queryFn: () => {
      return fetchIngredient({
        auth: session?.user.authorization as string,
        guid: router.query.id as string,
      });
    },
    queryKey: ["ingredient"],
    enabled: !!(session?.user?.authorization && router.query.id),
  });

  const formattedTitle = titleCase(ingredient?.name);

  const [search, setSearch] = useState<string>("");

  const [filters, setFilters] = useState<string[]>([]);
  const [sort, setSort] = useState<string>("");

  const handleFilterChange = (newFilters: string[]) => {
    setFilters(newFilters);
    console.log("Selected filters:", newFilters);
  };

  const handleSortChange = (newSort: string) => {
    setSort(newSort);
  };

  return (
    <>
      <Head>
        <title>MenuData - {formattedTitle}</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                {formattedTitle}
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Ingredients",
                    icon: (
                      <IconIngredientsSolid
                        size={16}
                        className="text-neutral-600"
                      />
                    ),
                    link: "/ingredients",
                  },
                  {
                    label: formattedTitle,
                    link: `/ingredients/details/${id}`,
                  },

                  {
                    label: "All Social Media Posts",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            {ingredient && (
              <IngredientContext.Provider value={ingredient}>
                <SocialMediaFilters
                  search={search}
                  onSearch={setSearch}
                  filters={filters}
                  onFilter={handleFilterChange}
                  sort={sort}
                  onSort={handleSortChange}
                />
                <ScrollContainer>
                  <AllMostEngagedSocialMediaPosts filters={filters} />
                </ScrollContainer>
              </IngredientContext.Provider>
            )}
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
