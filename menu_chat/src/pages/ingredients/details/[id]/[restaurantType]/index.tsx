import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconIngredientsSolid } from "@/components/icons";
import titleCase from "voca/title_case";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { fetchIngredient } from "@/api/ingredients";
import RestaurantTypeDetails from "@/components/ingredients/details/distribution-overview/restaurant-type/restaurantTypeDetails";
import { IngredientContext } from "@/contexts/IngredientContext";

enum RestaurantTypes {
  qsr = "QSR",
  "fast-casual" = "Fast Casual",
  "fine-dining" = "Fine Dining",
  "mid-scale" = "Mid Scale",
  "casual-dining" = "Casual Dining",
  other = "Other",
}

export default function IngredientsDetailsRestaurantTypePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const { id, restaurantType } = router.query;

  const { data: ingredient } = useQuery({
    queryFn: () => {
      return fetchIngredient({
        auth: session?.user.authorization as string,
        guid: id as string,
      });
    },
    queryKey: ["ingredient", id],
    enabled: !!(session?.user?.authorization && id),
  });

  const ingredientName = ingredient?.name || "Ingredient";
  const formattedIngredientTitle = titleCase(ingredientName);
  const formattedRestaurantType =
    RestaurantTypes[restaurantType as keyof typeof RestaurantTypes] ||
    titleCase(restaurantType as string);

  return (
    <>
      <Head>
        <title>
          MenuData - {formattedIngredientTitle} - {formattedRestaurantType}
        </title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                {formattedRestaurantType}
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Ingredients",
                    icon: (
                      <IconIngredientsSolid
                        size={16}
                        className="text-neutral-600"
                      />
                    ),
                    link: "/ingredients",
                  },
                  {
                    label: formattedIngredientTitle,
                    link: `/ingredients/details/${id}`,
                  },
                  {
                    label: formattedRestaurantType,
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            {ingredient && (
              <IngredientContext.Provider value={ingredient}>
                <ScrollContainer className="h-[calc(100vh-110px)]">
                  <RestaurantTypeDetails />
                </ScrollContainer>
              </IngredientContext.Provider>
            )}
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
