import Head from "next/head";
import { useRouter } from "next/router";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconIngredientsSolid } from "@/components/icons";
import IngredientsDetailsMenu from "@/components/ingredients/details/ingredientsDetailsMenu";
import IngredientsDetailsOnMenu from "@/components/ingredients/details/on-menu/ingredientsDetailsOnMenu";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { fetchIngredient } from "@/api/ingredients";
import titleCase from "voca/title_case";
import { IngredientContext } from "@/contexts/IngredientContext";

export default function IngredientDetailsOnMenusPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const id = router.query.id || "ingredient"; // Default value if id is not available

  const { data: ingredient } = useQuery({
    queryFn: () => {
      return fetchIngredient({
        auth: session?.user.authorization as string,
        guid: router.query.id as string,
      });
    },
    queryKey: ["ingredient"],
    enabled: !!(session?.user?.authorization && router.query.id),
  });


  const formattedTitle = titleCase(ingredient?.name);


  return (
    <>
      <Head>
        <title>MenuData - {formattedTitle}</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Ingredients
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Ingredients",
                    icon: (
                      <IconIngredientsSolid
                        size={16}
                        className="text-neutral-600"
                      />
                    ),
                    link: "/ingredients",
                  },
                  {
                    label: "Trending",
                    link: "/ingredients/trending", //TODO: change to right url
                  },
                  {
                    label: formattedTitle,
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            {ingredient && (<IngredientContext.Provider value={ingredient}>
              <IngredientsDetailsMenu ingredientSlug={id?.toString() || ""} />
              <ScrollContainer>
                <IngredientsDetailsOnMenu />
              </ScrollContainer>
            </IngredientContext.Provider>)}

          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
