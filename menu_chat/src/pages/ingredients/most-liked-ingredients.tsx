import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconIngredientsSolid } from "@/components/icons";
import MostLikedTable from "@/components/ingredients/consumer-insights/most-liked/mostLikedTable";

export default function MostLikedIngredientsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Most Liked Ingredients by Consumers</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Most Liked Ingredients by Consumers
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Ingredients",
                    icon: (
                      <IconIngredientsSolid
                        size={16}
                        className="text-neutral-600"
                      />
                    ),
                    link: "/ingredients",
                  },
                  {
                    label: "Most Liked Ingredients by Consumers",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <MostLikedTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
