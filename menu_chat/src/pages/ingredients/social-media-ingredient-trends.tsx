import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconIngredientsSolid } from "@/components/icons";
import IngredientTrendsTable from "@/components/ingredients/socialmedia-insights/ingredient-trends/ingredientTrendsTable";

export default function SocialMediaIngredientTrendsPage() {
  return (
    <>
      <Head>
        <title>MenuData - Social Media Ingredient Trends</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Social Media Ingredient Trends
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Ingredients",
                    icon: (
                      <IconIngredientsSolid
                        size={16}
                        className="text-neutral-600"
                      />
                    ),
                    link: "/ingredients",
                  },
                  {
                    label: "Social Media Ingredient Trends",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <IngredientTrendsTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
