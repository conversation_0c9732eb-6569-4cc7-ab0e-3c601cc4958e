import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import Breadcrumbs from "@/components/base/breadcrumbs";
import { IconIngredientsSolid } from "@/components/icons";
import TopRetailersTable from "@/components/ingredients/retail-insights/top-retailers/topRetailersTable";

export default function TopRetailersPage() {
  return (
    <>
      <Head>
        <title>MenuData - Top Retailers</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <div>
              <Heading level={2} className="mb-1">
                Top Retailers
              </Heading>
              <Breadcrumbs
                items={[
                  {
                    label: "Ingredients",
                    icon: (
                      <IconIngredientsSolid
                        size={16}
                        className="text-neutral-600"
                      />
                    ),
                    link: "/ingredients",
                  },
                  {
                    label: "Top Retailers",
                  },
                ]}
              />
            </div>
          </Header>
          <ContentWrap>
            <ScrollContainer>
              <TopRetailersTable />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
