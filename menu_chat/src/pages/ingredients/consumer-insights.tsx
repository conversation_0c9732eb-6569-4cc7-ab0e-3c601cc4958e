import Head from "next/head";
import Layout from "@/components/layout/layout";
import Navigation from "@/components/layout/navigation/navigation";
import Main from "@/components/layout/main";
import Header from "@/components/layout/header";
import ScrollContainer from "@/components/layout/scrollContainer";
import ContentWrap from "@/components/layout/contentWrap";
import Heading from "@/components/base/heading";
import IngredientsMenu from "@/components/ingredients/ingredientsMenu";
import ConsumerInsights from "@/components/ingredients/consumer-insights/consumerInsights";

export default function IngredientsConsumerInsights() {
  return (
    <>
      <Head>
        <title>MenuData - Consumer Insights</title>
      </Head>
      <Layout withSideNav>
        <Navigation />
        <Main>
          <Header>
            <Heading level={2} className="mb-1">
              Ingredients
            </Heading>
          </Header>
          <ContentWrap>
            <IngredientsMenu />
            <ScrollContainer>
              <ConsumerInsights />
            </ScrollContainer>
          </ContentWrap>
        </Main>
      </Layout>
    </>
  );
}
