import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  type ReactNode,
} from "react";

interface AppContextType {
  navCollapsed: boolean;
  setNavCollapsed: (collapsed: boolean) => void;
  searchToggle: boolean;
  setSearchToggle: (toggle: boolean) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider = ({ children }: AppProviderProps) => {
  const [navCollapsed, setNavCollapsed] = useState(false);
  const [searchToggle, setSearchToggle] = useState(false);

  // Load initial state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem("menudata-store");
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        if (parsed.state?.navCollapsed !== undefined) {
          setNavCollapsed(parsed.state.navCollapsed);
        }
      } catch (error) {
        console.error("Error loading state from localStorage:", error);
      }
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(
      "menudata-store",
      JSON.stringify({ state: { navCollapsed } }),
    );
  }, [navCollapsed]);

  return (
    <AppContext.Provider
      value={{ navCollapsed, setNavCollapsed, searchToggle, setSearchToggle }}
    >
      {children}
    </AppContext.Provider>
  );
};

export function useAppStore() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppStore must be used within an AppProvider");
  }
  return context;
}
