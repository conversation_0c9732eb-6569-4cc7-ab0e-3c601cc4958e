import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {fetchFavorites, favoriteIngredient, unfavoriteIngredient} from '@/api/favorites'
import { useSession } from "next-auth/react";

export const useFavorites = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const { data: favorites } = useQuery({
    queryFn: () => {
      return fetchFavorites({auth: session?.user.authorization as string})
    },
    queryKey: ["favorites"],
    enabled: !!session?.user?.authorization,
  });

  const addMutation = useMutation({
    mutationFn: favoriteIngredient,
    onSuccess: () => {
      queryClient.invalidateQueries({queryKey: ['favorites']})
    }
  })

  const removeMutation = useMutation({
    mutationFn: unfavoriteIngredient,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['favorites']
      })
    }
  })

  const addFavorite = async( ingredient_guid: string) => {
    await addMutation.mutateAsync({auth: session?.user.authorization as string, ingredient_guid})
  }

  const removeFavorite = async( ingredient_guid: string) => {
    await removeMutation.mutateAsync({auth: session?.user.authorization as string, ingredient_guid})
  }

  const isFavorite = (favorites: Favorite[], ingredient_guid: string) => {
    return favorites.find(f => f.guid === ingredient_guid) != undefined
  }

  return {
    favorites,
    addFavorite,
    removeFavorite,
    isFavorite
  }
}