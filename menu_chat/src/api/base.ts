import axios, { AxiosError, AxiosResponse } from "axios";
import Router from 'next/router';

export const MENU_BASE_URL: string =
  process.env.NEXT_PUBLIC_MENU_BASE_URL ||
  "https://staging-menu-data.herokuapp.com/menu_api/v1";

export const getApi = (authorization: string) => {
  const api = axios.create({
    baseURL: MENU_BASE_URL,
  });

  api.interceptors.request.use((config) => {
    config.headers.Authorization = authorization;

    return config;
  });

  api.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      if (error.response?.status === 401) {
        Router.push('/login');
      }
      return Promise.reject(error);
    }
  )

  return api;
};

export const parseFilters = (filters?: string[]) => {
  const params: Record<string, string[] | string | undefined> = {};

  if (filters && filters.length) {
    filters.forEach(filter => {
      const [key, ...valueParts] = filter.split("-");
      const value = valueParts.join("-");
      const paramKey = `filters[${key}][]`;
      if (!params[paramKey]) {
        params[paramKey] = [];
      }
      (params[paramKey] as string[]).push(value);
    });
  }

  return params;
};