import axios from "axios";

export const MENU_BASE_URL: string =
  process.env.NEXT_PUBLIC_MENU_BASE_URL ||
  "https://staging-menu-data.herokuapp.com/menu_api/v1";

export const getApi = (authorization: string) => {
  const api = axios.create({
    baseURL: MENU_BASE_URL,
  });

  api.interceptors.request.use((config) => {
    config.headers.Authorization = authorization;

    return config;
  });
  return api;
};
