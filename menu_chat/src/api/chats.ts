import { getApi } from './base'

export const createChat = async(auth: string, message: string) => {
    try {
        const response = await getApi(auth).post(`/chat`, {
            message: message
        })
        return response
    }
    catch(error){
        console.log(error)
    }
}


export const fetchChats = async (auth: string) => {
    try {
        const response = await getApi(auth).get(`/chat`)
        return response
    }
    catch(error){
        console.log(error)
    }
}

export const fetchChat = async(auth: string, id: string) => {
    try {
        const response = await getApi(auth).get(`/chat/${id}`)
        return response
    }
    catch(error){
        console.log(error)
    }
}

export const fetchChatMessages = async(auth: string, id: string) => {
    try {
        const response = await getApi(auth).get(`/chat/${id}/messages`)
        return response
    }
    catch(error){
        console.log(error)
    }
}

export const createChatMessage = async(auth: string, id: string, message: string) => {
    try {
        const response = await getApi(auth).post(`/chat/${id}/messages`,{
            message: message
        })
        return response
    }
    catch(error){
        console.log(error)
    }
}