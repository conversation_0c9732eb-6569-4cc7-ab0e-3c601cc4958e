import { getApi } from './base'

interface QuarterData {
  value: number;
  percent: string | number;
}

interface CategoryData {
  category_name: string;
  quarters: { [key: string]: QuarterData };
}

const formatQuarterLabel = (quarter: string): string => {
  const [year, quarterNum] = quarter.split('_q');
  return `Q${quarterNum} ${year}`;
};

export const fastestGrowingIngredientCategory = async ({ auth }: { auth: string }) => {
  try {
    const response = await getApi(auth).get<CategoryData[]>('/products/fastest_growing_ingredient_category');

    const allQuarterKeys = Array.from(
      new Set(response.data.flatMap(item => Object.keys(item.quarters)))
    ).sort((a, b) => {
      const [yearA, qA] = a.split('_q').map(Number);
      const [yearB, qB] = b.split('_q').map(Number);
      return yearA - yearB || qA - qB;
    });

    const data = response.data.map((item, idx) => ({
      id: idx + 1,
      label: item.category_name,
      color: ["#FFBE05", "#FF6D56", "#8BC539", "#D273D8", "#3CA9E2", "#CBAF92" ][idx % 6],
      data: allQuarterKeys.map(q => {
        const raw = item.quarters[q];
        return raw ? raw.value : 0;
      }),
      percentData: allQuarterKeys.map(q => {
        const raw = item.quarters[q];
        if (!raw) return 0;
        const percent = raw.percent;
        return typeof percent === 'string' ? parseFloat(percent.replace('%', '')) : percent;
      }),
    }));

    const formattedLabels = allQuarterKeys.map(formatQuarterLabel);

    return { labels: formattedLabels, data };
  } catch (error) {
    console.error(error);
  }
};

export const fetchWhiteSpaceOpportunities = async ({ auth }: { auth: string }) => {
  try {
    const response = await getApi(auth).get('/products/white_space_opportunities');
    return response.data;
  } catch (error) {
    console.log(error);
  }
}
