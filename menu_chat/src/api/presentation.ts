export interface PresentationRequest {
  prompt: string;
  n_slides: number;
  language: string;
  template: string;
  export_as: string;
}

export interface PresentationData {
  presentation_id: string;
  path: string;
  edit_path: string;
}

export interface PresentationResponse {
  success: boolean;
  data?: PresentationData;
  error?: string;
}

export interface EditSlideContent {
  title?: string;
  text?: string;
  bullets?: string[];
  [key: string]: any; // Allow for other content fields
}

export interface EditSlideData {
  index: number;
  content: EditSlideContent;
}

export interface EditPresentationRequest {
  presentation_id: string;
  data: EditSlideData[];
  export_as?: string;
}

export const generatePresentation = async (
  requestData: PresentationRequest
): Promise<PresentationResponse> => {
  try {
    const response = await fetch('/api/presentation/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        "Authorization": "Bearer sk-presenton-17ebd2ff533523c4f99494ffe69d7cdaa29cbd25197036d6054a13347bce157182ff64b0ee77759153f5f521127101890d1752d7f371e1aa811df48d10536e4d"
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error generating presentation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

export const editPresentation = async (
  requestData: EditPresentationRequest
): Promise<PresentationResponse> => {
  try {
    const response = await fetch('/api/presentation/edit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error editing presentation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

export const downloadPresentation = async (filePath: string, filename?: string): Promise<void> => {
  try {
    // Use our Next.js API proxy for downloading to avoid CORS issues
    const downloadUrl = `/api/presentation/download?path=${encodeURIComponent(filePath)}`;

    // Create a temporary link element and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'presentation.pptx';

    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading presentation:', error);
    throw error;
  }
};
