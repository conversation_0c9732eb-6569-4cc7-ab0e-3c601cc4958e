export interface PresentationRequest {
  prompt: string;
  n_slides: number;
  language: string;
  template: string;
  export_as: string;
}

export interface PresentationData {
  presentation_id: string;
  path: string;
  edit_path: string;
}

export interface PresentationResponse {
  success: boolean;
  data?: PresentationData;
  error?: string;
}

export const generatePresentation = async (
  requestData: PresentationRequest
): Promise<PresentationResponse> => {
  try {
    const response = await fetch('/api/presentation/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error generating presentation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

export const downloadPresentation = async (filePath: string, filename?: string): Promise<void> => {
  try {
    // Use our Next.js API proxy for downloading to avoid CORS issues
    const downloadUrl = `/api/presentation/download?path=${encodeURIComponent(filePath)}`;

    // Create a temporary link element and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'presentation.pptx';

    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading presentation:', error);
    throw error;
  }
};
