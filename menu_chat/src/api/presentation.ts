export interface PresentationRequest {
  prompt: string;
  n_slides: number;
  language: string;
  template: string;
  export_as: string;
}

export interface PresentationResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export const generatePresentation = async (
  requestData: PresentationRequest
): Promise<PresentationResponse> => {
  try {
    const response = await fetch('/api/presentation/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error generating presentation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
