import { getApi, parseFilters } from "./base";
import { PaginationState, SortingState } from "@tanstack/react-table";

interface SearchQuery {
  auth: string
  query: string
  filter?: string
  filters?: string[]
  pagination?: PaginationState
  sorting?: SortingState
}
export const fetchSearch = async({auth, query, filter} : SearchQuery) => {
  try {
    const response = await getApi(auth).get(`/search`, {
      params: {
        query: query,
        filter: filter
      }
    })
    return response
  }
  catch(error){
    console.log(error)
  }
}

export const fetchFullSearch = async({auth, query, filters, pagination, sorting} : SearchQuery) => {
  try {
    const response = await getApi(auth).get(`/full_search`, {
      params: {
        query: query,
        filters: filters,
        pagination: pagination,
        sorting: sorting
      }
    })
    return response
  }
  catch(error){
    console.log(error)
  }
}


export const fetchRecentSearches = async({auth} : {auth: string}) => {
  try {
    const response = await getApi(auth).get(`/recent_searches`, {
      params: {
      }
    })
    return response
  }
  catch(error){
    console.log(error)
  }
}

export const fetchSearchItems = async({auth, pagination, query, filters} : SearchQuery) => {
  try {
    const response = await getApi(auth).get(`/search/items`, {
      params: {
        query,
        pagination,
        ...parseFilters(filters),
      }
    })
    return response
  }
  catch(error){
    console.log(error)
  }
}
