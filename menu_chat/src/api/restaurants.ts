import { getApi, parseFilters } from './base';

interface RestaurantRequest {
  auth: string,
  guid: string,
  limit?: number
}

export const fetchRestaurant = async({auth, guid} : RestaurantRequest) => {
  try {
    const response = await getApi(auth).get(`/restaurants/${guid}`);
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const fetchRestaurantItems = async({auth, guid, secondary_filter, filters} : MenuItemRequest) => {
  try {

    const params: Record<string, string[] | string | undefined> = {
      secondary_filter,
      ...parseFilters(filters)
    };

    const response = await getApi(auth).get(`/restaurants/${guid}/items`, { params });

    return response.data
  } catch(error){
    console.log(error)
  }
}