import { getApi } from "./base";
import { BarChartItem } from "@/components/charts/barChart";

interface ManufacturerData {
  name: string;
  count: number;
  price: number;
}

interface RetailerData {
  name: string;
  count: number;
  price: number;
}

interface TopManufacturersResponse {
  id: number;
  top_brands: ManufacturerData[];
}

interface TopRetailersResponse {
  id: number;
  top_retails_by_banner_name: RetailerData[];
}

export const topManufacturers = async ({ auth, dashboardId }: { auth: string; dashboardId: string }) => {
  try {
    const response = await getApi(auth).get<TopManufacturersResponse>(
      `/retail_insights/${dashboardId}/top_manufacturers`
    );

    const maxValue = Math.max(
      ...response.data.top_brands.map((item) => item.count),
    );

    const data: BarChartItem[] = response.data.top_brands.map((item, index) => ({
      id: index + 1,
      label: item.name,
      color: item.count === maxValue ? "#A2D161" : "#D2D0BC",
      value: item.count,
      secondaryValue: item.price,
    }));

    return { maxValue: roundUpToNiceNumber(maxValue), data };
  } catch (error) {
    console.error("Error fetching top manufacturers:", error);
    throw error;
  }
};

export const topRetailers = async ({ auth, ingredientId }: { auth: string; ingredientId: string }) => {
  try {
    const response = await getApi(auth).get<TopRetailersResponse>(
      `/retail_insights/${ingredientId}/top_retailers`
    );

    const maxValue = Math.max(
      ...response.data.top_retails_by_banner_name.map((item) => item.count),
    );

    const data: BarChartItem[] = response.data.top_retails_by_banner_name.map((item, index) => ({
      id: index + 1,
      label: item.name,
      color: item.count === maxValue ? "#A2D161" : "#D2D0BC",
      value: item.count,
      secondaryValue: item.price,
    }));

    return { maxValue: roundUpToNiceNumber(maxValue), data };
  } catch (error) {
    console.error("Error fetching top retailers:", error);
    throw error;
  }
};

function roundUpToNiceNumber(num: number): number {
  const power = Math.pow(10, Math.floor(Math.log10(num)));
  return Math.ceil(num / power) * power;
}
