import {getApi} from './base'

export const fetchFavorites = async({auth} : {auth: string}) => {
  try {
    const response = await getApi(auth).get(`/favorites`, {
      params: {}
    })
    return response
  }
  catch(error){
    console.log(error)
  }
}

export const favoriteIngredient = async({auth, ingredient_guid} : {auth: string, ingredient_guid: string}) => {
  try {
    const response = await getApi(auth).post(`/ingredients/${ingredient_guid}/favorite`)
    return response
  }
  catch(error){
    console.log(error)
  }
}


export const unfavoriteIngredient = async({auth, ingredient_guid} : {auth: string, ingredient_guid: string}) => {
  try {
    const response = await getApi(auth).delete(`/ingredients/${ingredient_guid}/unfavorite`)
    return response
  }
  catch(error){
    console.log(error)
  }
}