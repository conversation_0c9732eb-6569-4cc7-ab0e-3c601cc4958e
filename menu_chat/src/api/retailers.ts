import { getApi } from "./base";
import { BarChartItem } from "@/components/charts/barChart";

export const topRetailers = async ({ auth, page = 1, perPage = 10, chartLimit = 5 }: {
  auth: string;
  page?: number;
  perPage?: number;
  chartLimit?: number;
}): Promise<TopRetailersResult> => {
  try {
    const response = await getApi(auth).get<{ data: RetailerData[]; meta?: { total?: number; page?: number; per_page?: number } }>(
      `/retailers/top_retailers?page=${page}&per_page=${perPage}&limit=${chartLimit}`
    );

    const items = response.data.data;
    const metaTotal = response.data.meta?.total ?? items.length;
    if (!Array.isArray(items) || items.length === 0) {
      throw new Error("No data received from API");
    }

    const maxValue = Math.max(...items.map((item) => item.unique_skus || 0));

    const data: BarChartItem[] = items.map((item, index) => ({
      id: item.id ? Number(item.id) : index,
      label: item.name || "Unknown",
      color: item.unique_skus === maxValue ? "#A2D161" : "#D2D0BC",
      value: item.unique_skus || 0,
    }));

    return {
      maxValue: roundUpToNiceNumber(maxValue),
      data,
      originalData: items,
      total: metaTotal,
    };
  } catch (error) {
    console.error("Error in topRetailers API call:", error);
    throw error;
  }
};

export const topProductInnovation = async ({ auth }: { auth: string }): Promise<TopProductInnovationResult> => {
  try {
    const response = await getApi(auth).get<ProductInnovation[]>(
      "/retailers/top_product_innovation"
    );

    const items = response.data;
    if (!Array.isArray(items) || items.length === 0) {
      throw new Error("No data received from API");
    }

    return {
      data: items,
      total: items.length,
    };
  } catch (error) {
    console.error("Error in topProductInnovation API call:", error);
    throw error;
  }
};

function roundUpToNiceNumber(num: number): number {
  const power = Math.pow(10, Math.floor(Math.log10(num)));
  return Math.ceil(num / power) * power;
}
