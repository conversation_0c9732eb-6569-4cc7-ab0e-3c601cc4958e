import { getApi } from "./base";
import { BarChartItem } from "@/components/charts/barChart";

interface RetailerData {
  id: string;
  name: string;
  products_with_l0: number;
  unique_skus: number;
}

export const topRetailers = async ({ auth }: { auth: string }) => {
  try {
    const response = await getApi(auth).get<RetailerData[]>("/retailers/top_retailers");

    const maxValue = Math.max(
      ...response.data.map((item) => item.unique_skus),
    );

    const data: BarChartItem[] = response.data.map((item) => {
      return {
        id: Number(item.id),
        label: item.name,
        color: item.unique_skus === maxValue ? "#A2D161" : "#D2D0BC",
        value: item.unique_skus,
      };
    });

    return { maxValue: roundUpToNiceNumber(maxValue), data };
  } catch (error) {
    console.log(error);
  }
};

function roundUpToNiceNumber(num: number): number {
  const power = Math.pow(10, Math.floor(Math.log10(num)));
  return Math.ceil(num / power) * power;
}
