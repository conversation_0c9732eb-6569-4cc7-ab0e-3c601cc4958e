import { getApi } from './base';
import { PaginationState, SortingState } from "@tanstack/react-table";

interface MenuItemsRequest {
  auth: string,
  sorting?: SortingState,
  pagination?: PaginationState,
}

export const fastestGrowingDashCategoriesChart = async({auth} : MenuItemsRequest) => {
  try {
    const params = {};

    const response = await getApi(auth).get('/menu_items/fastest_growing_dish_categories_chart', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const menuItemsInnovation = async({auth, sorting, pagination} : MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      pagination,
    };

    const response = await getApi(auth).get('/menu_items/menu_items_innovation', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const limitedTimeOffers = async({auth, sorting, pagination} : MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      pagination,
    };

    const response = await getApi(auth).get('/menu_items/popular_limited_time_offers', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const fastestGrowingMenuItems = async({auth, sorting, pagination} : MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      pagination,
    };

    const response = await getApi(auth).get('/menu_items/fastest_growing_menu_items', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const highestPenetrationCompared = async({auth, sorting, pagination} : MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      pagination,
    };

    const response = await getApi(auth).get('/menu_items/highest_penetration', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}



export const fetchAllMenuItems = async({auth, sorting, pagination}: MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      pagination,
    };

    const response = await getApi(auth).get('/menu_items', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

interface MenuItemRequest {
  auth: string,
  guid: string,
  page?: number,
  pageSize?: number
}

export const fetchMenuItem = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}`);
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const fetchPenRateOverTime = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/pen_rate_over_time`, );
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchMenuItemItems = async({auth, guid, page, pageSize} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/items`, {
      params: {
        page,
        per_page: pageSize,
      }
    });

    return response.data
  } catch(error){
    console.log(error)
  }
}


