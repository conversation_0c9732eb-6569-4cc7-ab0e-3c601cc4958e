import { getApi, parseFilters } from './base';
import { PaginationState, SortingState } from "@tanstack/react-table";

interface MenuItemsRequest {
  auth: string,
  sorting?: SortingState,
  pagination?: PaginationState,
  search?: string,
  guid?: string,
  filters?: string[],
}

export const fastestGrowingDashCategoriesChart = async({auth} : MenuItemsRequest) => {
  try {
    const params = {};

    const response = await getApi(auth).get('/menu_items/fastest_growing_dish_categories_chart', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}




export const menuItemsInnovation = async({auth, sorting, pagination, guid} : MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      page: pagination?.pageIndex,
      per_page: pagination?.pageSize,
      guid
    };

    const response = await getApi(auth).get('/menu_items/menu_items_innovation', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const limitedTimeOffers = async({auth, sorting, pagination, search, filters} : MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      pagination,
      search,
      ...parseFilters(filters)
    };

    const response = await getApi(auth).get('/menu_items/popular_limited_time_offers', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const fastestGrowingMenuItems = async({auth, sorting, pagination} : MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      pagination,
    };

    const response = await getApi(auth).get('/menu_items/fastest_growing_menu_items', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const highestPenetrationCompared = async({auth, sorting, pagination} : MenuItemsRequest) => {
  try {
    const params = {
      sorting,
      pagination,
    };

    const response = await getApi(auth).get('/menu_items/highest_penetration', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}



export const fetchAllMenuItems = async({auth, sorting, pagination, search, filters}: MenuItemsRequest) => {
  try {
    const params: Record<string, string[] | SortingState | PaginationState | string | undefined> = {
      sorting,
      pagination,
      search,
      ...parseFilters(filters)
    };

    const response = await getApi(auth).get('/menu_items', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const fetchMenuItem = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}`);
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const fetchPenRateOverTime = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/pen_rate_over_time`, );
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchMenuItemItems = async({auth, guid, page, pageSize, secondary_filter} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/items`, {
      params: {
        page,
        per_page: pageSize,
        secondary_filter: secondary_filter
      }
    });

    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchCuisineDistro = async({auth, guid, limit} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/cuisine_distro`, {
      params: {
        limit
      }
    });

    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchDayPartDistro = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/day_part`);
    return response.data
  } catch(error){
    console.log(error)
  }
}



export const fetchMealTypeDistro = async({auth, guid, limit} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/meal_type_distro`, {
      params: {
        limit
      }
    });

    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchHighestReviewedDishes = async({auth, guid, page, pageSize} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/highest_reviewed_dishes`, {
      params: {
        page,
        per_page: pageSize,
      }
    });

    return response.data
  } catch(error){
    console.log(error)
  }
}



export const fetchGeographicPopularity = async({ auth } : { auth: string }) => {
  try {
    const response = await getApi(auth).get('/menu_items/geographic_popularity');
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchPopularMealTypes = async({ auth } : { auth: string }) => {
  try {
    const response = await getApi(auth).get('/menu_items/popular_meal_types');
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchMostPopularCuisines = async({ auth } : { auth: string }) => {
  try {
    const response = await getApi(auth).get('/menu_items/most_popular_cuisines');
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchMostLikedMenuItems = async({ auth } : { auth: string }) => {
  try {
    const response = await getApi(auth).get('/menu_items/most_liked_menu_items');
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchMostTalkedAboutMenuItems = async({ auth } : { auth: string }) => {
  try {
    const response = await getApi(auth).get('/menu_items/most_talked_about_menu_items');
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchMentionsByState = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/mentions_by_state`);
    return response.data;
  } catch (error) {
    console.error("Error fetching mentions by state:", error);
    throw error;
  }
};

export const fetchSocialConversations = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/social_conversations`);
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchSocialSentiments = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/social_sentiments`);
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchIncomeLevel = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/income_level`);
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchConsumedByGeneration = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/consumed_by_generation`);
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchConsumerExperience = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/consumer_experience`);
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchByMenuAdoption = async({auth, filters} : {auth: string, filters: string[]}) => {
  try {
    const response = await getApi(auth).get(`/menu_items/by_menu_adoption`, { params: {...parseFilters(filters)} });
    return response.data;
  } catch(error){
    console.log(error)
  }
}

export const fetchSocialMediaPopularity = async({auth, guid} : {auth: string, guid: string}) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/social_media_popularity`);
    return response.data;
  } catch (error) {
    console.error("Error fetching social media popularity:", error);
    throw error;
  }
}

export const fetchSocialMediaConversations = async({auth, guid} : {auth: string, guid: string}) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/social_media_conversations`);
    return response.data;
  } catch (error) {
    console.error("Error fetching social media conversations:", error);
    throw error;
  }
}

export const fetchSocialMediaWords = async({auth, guid} : {auth: string, guid: string}) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/social_media_words`);
    return response.data;
  } catch (error) {
    console.error("Error fetching social media words:", error);
    throw error;
  }
}
export const fetchSocialMediaPosts = async({auth, guid} : {auth: string, guid: string}) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/social_media_posts`);
    return response.data;
  } catch (error) {
    console.error("Error fetching social media posts:", error);
    throw error;
  }
}

export const fetchSocialMediaSentiments = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/social_media_sentiments`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchRegionsDistro = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/regions_distro`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchRestaurantTypes = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/restaurant_types`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchChainTypes = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/chain_types_distro`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchTopIngredients = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/top_ingredients`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchPriceVariation = async({auth, guid} : MenuItemRequest) => {
  try {
    const response = await getApi(auth).get(`/menu_items/${guid}/price_variation_chart`)
    return response.data
  } catch (error){
    console.log(error)
  }
}