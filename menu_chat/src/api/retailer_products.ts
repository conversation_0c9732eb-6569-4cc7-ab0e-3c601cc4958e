import { getApi } from "./base";

interface RetailProductsResponse {
  data: RetailProduct[];
  total_count: number;
}

interface MostPopularFlavor {
  id: number;
  name: string;
  product_count: number;
}

interface TopBrand {
  name: string;
  count: number;
}

export interface PenetrationTrendPoint {
  q: number;
  year: number;
  percent: number;
}

export interface PenetrationTrendingLineResponse {
  id: string;
  compared: string;
  gri: string;
  growth: number;
  prediction: string;
  trend_line: PenetrationTrendPoint[];
}

interface ApiParams {
  auth: string;
  id?: string;
  limit?: number;
}

interface RetailProductsParams {
  auth: string;
  page?: number;
  perPage?: number;
  sort?: string | { column?: string; direction?: "asc" | "desc" };
  search?: string;
  filters?: RetailProductTableFilters;
}

interface RetailProductOnShelfRow {
  id: number;
  name: string;
  brand: string | null;
  retailer_name: string | null;
  banner_name: string | null;
  l0: string | null;
  l1: string | null;
  price: string | number | null;
  listing_url: string | null;
  image_urls: string[];
}

export interface RetailProductOnShelfResponse {
  data: RetailProductOnShelfRow[];
  total_count: number;
}

interface RetailOnShelfFiltersResponse {
  brand?: string[];
  claims?: string[];
  flavours?: string[];
  textures?: string[];
  l0?: string[];
  l1?: string[];
  l2?: string[];
  banner_name?: string[];
}

const COLOR_PALETTE = [
  "#78C5E3",
  "#FAAB61",
  "#DF9DE4",
  "#FF9985",
  "#9ebf5d",
  "#85a1ff",
  "#f4e684",
  "#75e3ac",
  "#77b3d8",
  "#d890ac",
];

// const FORMAT_COLOR_PALETTE = [
//   "#78C5E3",
//   "#FAAB61",
//   "#DF9DE4",
//   "#FF9985",
//   "#6dd3a0",
// ];

// Common API request handler
async function fetchApi<T>(auth: string, endpoint: string, errorMessage: string): Promise<T> {
  try {
    const response = await getApi(auth).get<T>(endpoint);
    return response.data;
  } catch (error) {
    console.error(errorMessage, error);
    throw error;
  }
}

// Round up to the nearest "nice" number (e.g., 10, 100, 1000)
function roundUpToNiceNumber(num: number): number {
  if (num <= 0) return 10; // Handle edge case
  const magnitude = Math.pow(10, Math.floor(Math.log10(num)));
  return Math.ceil(num / magnitude) * magnitude;
}

// Distribute percentages to sum up to maxTotal (default 100)
function distributePercentages(counts: number[], total: number, maxTotal: number = 100): number[] {
  if (total === 0) return counts.map(() => 0);

  const percentages = counts.map((count) => (count / total) * maxTotal);
  const floored = percentages.map(Math.floor);
  const remainders = percentages.map((val, i) => ({ index: i, remainder: val - floored[i] }));

  const sum = floored.reduce((a, b) => a + b, 0);
  const remaining = maxTotal - sum;

  remainders.sort((a, b) => b.remainder - a.remainder);
  for (let i = 0; i < remaining && i < remainders.length; i++) {
    floored[remainders[i].index]++;
  }

  return floored;
}

export const getRetailProducts = async ({
  auth,
  page = 1,
  perPage = 10,
  sort = "",
  search = "",
  filters = { l0: [], l1: [], gri: [] },
}: RetailProductsParams): Promise<RetailProductsResponse> => {
  const queryParams = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString(),
    search,
  });

  if (typeof sort === "string") {
    const trimmed = sort.trim();
    if (trimmed.includes(":")) {
      const [rawColumn, direction] = trimmed.split(":");
      const column = rawColumn === "retailer_name" ? "banner_name" : rawColumn;
      if (column) queryParams.append("sort[column]", column);
      if (direction === "asc" || direction === "desc") queryParams.append("sort[direction]", direction);
    } else if (trimmed) {
      queryParams.set("sort", trimmed);
    }
  } else if (sort && sort.column) {
    const col = sort.column === "retailer_name" ? "banner_name" : sort.column;
    if (col) queryParams.append("sort[column]", col);
    if (sort.direction) queryParams.append("sort[direction]", sort.direction);
  }

  if (filters.l0?.length) { queryParams.append("filters[l0]", filters.l0.join("|")); }
  if (filters.l1?.length) { queryParams.append("filters[l1]", filters.l1.join("|")); }
  if (filters.gri?.length) { queryParams.append("filters[gri]", filters.gri.join("|")); }

  return fetchApi<RetailProductsResponse>(
    auth,
    `/retail_products?${queryParams.toString()}`,
    "Error in getRetailProducts API call:",
  );
};

export const getRetailProductOnShelf = async ({
  auth,
  id,
  page = 1,
  perPage = 20,
  sort = "",
  search = "",
  filters = { l0: [], l1: [], gri: [], brand: [], claims: [], flavours: [], textures: [], retailer_name: [] },
}: ApiParams & RetailProductsParams & { id: string }): Promise<RetailProductOnShelfResponse> => {
  const queryParams = new URLSearchParams({
    page: String(page),
    per_page: String(perPage),
    search,
  });

  if (typeof sort === "string") {
    const trimmed = sort.trim();
    if (trimmed.includes(":")) {
      const [rawColumn, direction] = trimmed.split(":");
      const column = rawColumn === "retailer_name" ? "banner_name" : rawColumn;
      if (column) queryParams.append("sort[column]", column);
      if (direction === "asc" || direction === "desc") queryParams.append("sort[direction]", direction);
    } else if (trimmed) {
      queryParams.set("sort", trimmed);
    }
  } else if (sort && sort.column) {
    const mapped = sort.column === "retailer_name" ? "banner_name" : sort.column;
    if (mapped) queryParams.append("sort[column]", mapped);
    if (sort.direction) queryParams.append("sort[direction]", sort.direction);
  }

  if (filters.l0?.length) queryParams.append('filters[l0]', filters.l0.join('|'));
  if (filters.l1?.length) queryParams.append('filters[l1]', filters.l1.join('|'));
  if (filters.gri?.length) queryParams.append('filters[gri]', filters.gri.join('|'));
  if (filters.brand?.length) queryParams.append('filters[brand]', filters.brand.join('|'));
  if (filters.claims?.length) queryParams.append('filters[claims]', filters.claims.join('|'));
  if (filters.flavours?.length) queryParams.append('filters[flavours]', filters.flavours.join('|'));
  if (filters.textures?.length) queryParams.append('filters[textures]', filters.textures.join('|'));
  if (filters.retailer_name?.length) queryParams.append('filters[banner_name]', filters.retailer_name.join('|'));

  return fetchApi<RetailProductOnShelfResponse>(
    auth,
    `/retail_products/${id}/products_list?${queryParams.toString()}`,
    'Error in getRetailProductOnShelf API call:',
  );
};

export const getRetailOnShelfFilters = async ({ auth, id }: { auth: string; id: string }): Promise<RetailProductTableFilters> => {
  const resp = await fetchApi<RetailOnShelfFiltersResponse>(
    auth,
    `/retail_products/${id}/on_the_self_filters`,
    'Error in getRetailOnShelfFilters API call:',
  );
  return {
    l0: resp.l0,
    l1: resp.l1,
    brand: resp.brand,
    claims: resp.claims,
    flavours: resp.flavours,
    textures: resp.textures,
    banner_name: resp.banner_name,
  };
};

export const getRetailProductDetail = async ({
  auth,
  id,
}: ApiParams): Promise<RetailProductDetail> => {
  return fetchApi<RetailProductDetail>(auth, `/retail_products/${id}/general_details`, "Error in getRetailProductDetail API call:",);
};

export const getRetailProductNewFlavors = async ({
  auth,
  id,
  limit = 5,
}: ApiParams): Promise<RetailProductFlavor[]> => {
  return fetchApi<RetailProductFlavor[]>(auth, `/retail_products/${id}/new_flavors?limit=${limit}`, "Error in getRetailProductNewFlavors API call:",);
};

export const getRetailProductNewInnovations = async ({
  auth,
  id,
  limit = 4,
}: ApiParams): Promise<RetailProductInnovation[]> => {
  return fetchApi<RetailProductInnovation[]>(auth, `/retail_products/${id}/new_product_innovation?limit=${limit}`, "Error in getRetailProductNewInnovations API call:",
  );
};

export const getRetailProductMostPopularFlavors = async ({
  auth,
  id,
  limit = 10,
}: ApiParams): Promise<RetailProductChartData> => {
  const response = await fetchApi<MostPopularFlavor[]>(auth, `/retail_products/${id}/most_popular_flavours?limit=${limit}`, "Error in getRetailProductMostPopularFlavors API call:",);

  const maxValue = roundUpToNiceNumber(
    Math.max(...response.map((item) => item.product_count)) * 1.1,
  );

  const data: BarChartItem[] = response.map((item, index) => ({
    id: item.id,
    label: item.name,
    value: item.product_count,
    symbol: "",
    color: COLOR_PALETTE[index % COLOR_PALETTE.length] || "#BDA4CB",
    customValue: item.product_count,
  }));

  return { maxValue, data };
};

export const getRetailProductPenetrationTrendingLine = async ({ auth, id }: { auth: string; id: string }): Promise<PenetrationTrendingLineResponse> => {
  return fetchApi<PenetrationTrendingLineResponse>(
    auth,
    `/retail_products/${id}/penetration_trending_line`,
    "Error in getRetailProductPenetrationTrendingLine API call:",
  );
};

export const getRetailProductTopBrands = async ({
  auth,
  id,
  limit = 10,
}: ApiParams): Promise<RetailProductChartData> => {
  const response = await fetchApi<TopBrand[]>(auth, `/retail_products/${id}/top_brands?limit=${limit}`, "Error in getRetailProductTopBrands API call:",);

  const maxValue = roundUpToNiceNumber(
    Math.max(...response.map((item) => item.count)) * 1.1,
  );

  const data: BarChartItem[] = response.map((item, index) => ({
    id: index + 1,
    label: item.name,
    value: item.count,
    symbol: "",
    color: COLOR_PALETTE[index % COLOR_PALETTE.length] || "#BDA4CB",
    customValue: item.count,
  }));

  return { maxValue, data };
};

export const getRetailProductTopFormats = async ({
  auth,
  id,
  limit = 10,
}: ApiParams): Promise<RetailProductChartData> => {
  const response = await fetchApi<TopFormat[]>(auth, `/retail_products/${id}/top_retail_formats?limit=${limit}`, "Error in getRetailProductTopFormats API call:",);

  const totalCount = response.reduce((sum, item) => sum + item.count, 0);
  const maxValue = 100;
  const counts = response.map((item) => item.count);
  const distributed = distributePercentages(counts, totalCount, maxValue);

  const data: BarChartItem[] = response.map((item, index) => ({
    id: index + 1,
    label: item.name,
    value: distributed[index],
    symbol: "%",
    color: COLOR_PALETTE[index % COLOR_PALETTE.length] || "#BDA4CB",
    customValue: item.count,
  }));

  return { maxValue, data };
};

export const fetchRetailProduct = async ({
  auth,
  id,
}: ApiParams): Promise<RetailProductDetail> => {
  return fetchApi<RetailProductDetail>(auth, `/retail_products/${id}`, "Error in fetchRetailProduct API call:",);
};

export const getRetailProductTopClaims = async ({
  auth,
  id,
  limit = 10,
}: ApiParams): Promise<RetailProductChartData> => {
  const response = await fetchApi<{ name: string; count: number }[]>(
    auth,
    `/retail_products/${id}/top_claims?limit=${limit}`,
    "Error in getRetailProductTopClaims API call:",
  );

  const totalCount = response.reduce((sum, item) => sum + item.count, 0);
  const maxValue = 100;
  const counts = response.map((item) => item.count);
  const distributed = distributePercentages(counts, totalCount, maxValue);

  const data: BarChartItem[] = response.map((item, index) => ({
    id: index + 1,
    label: item.name,
    value: distributed[index],
    symbol: "%",
    color: COLOR_PALETTE[index % COLOR_PALETTE.length] || "#BDA4CB",
    customValue: item.count,
  }));

  return { maxValue, data };
};

export const getRetailProductTopTextures = async ({
  auth,
  id,
  limit = 10,
}: ApiParams): Promise<RetailProductChartData> => {
  const response = await fetchApi<{ name: string; count: number }[]>(
    auth,
    `/retail_products/${id}/top_textures?limit=${limit}`,
    "Error in getRetailProductTopTextures API call:",
  );

  const totalCount = response.reduce((sum, item) => sum + item.count, 0);
  const maxValue = 100;
  const counts = response.map((item) => item.count);
  const distributed = distributePercentages(counts, totalCount, maxValue);

  const data: BarChartItem[] = response.map((item, index) => ({
    id: index + 1,
    label: item.name,
    value: distributed[index],
    symbol: "%",
    color: COLOR_PALETTE[index % COLOR_PALETTE.length] || "#BDA4CB",
    customValue: item.count,
  }));

  return { maxValue, data };
};

export const getRetailProductsTableFilters = async ({
  auth,
  claimed = false,
}: ApiParams & { claimed?: boolean }): Promise<RetailProductTableFilters> => {
  const queryParams = new URLSearchParams();
  if (claimed) queryParams.append("claimed", "true");
  return fetchApi<{ l0: string[]; l1: string[]; gri: string[] }>(
    auth,
    `/retail_products/table_filters?${queryParams.toString()}`,
    "Error in getRetailProductsTableFilters API call:",
  );
};

export const getRetailProductLifecycle = async ({
  auth,
  filters = { l0: [], l1: [], gri: [] },
  per_page = 40,
  claimed = false,
}: {
  auth: string;
  filters?: RetailProductTableFilters;
  per_page?: number;
  claimed?: boolean;
}): Promise<RetailLifecycleItem[]> => {
  const query = new URLSearchParams({ per_page: String(per_page) });

  if (filters.l0?.length) query.append("filters[l0]", filters.l0.join("|"));
  if (filters.l1?.length) query.append("filters[l1]", filters.l1.join("|"));
  if (filters.gri?.length) query.append("filters[gri]", filters.gri.join("|"));

  const endpoint = claimed
    ? `/retail_products/claims_lifecycle?${query.toString()}`
    : `/retail_products/product_lifecycle?${query.toString()}`;

  return fetchApi<RetailLifecycleItem[]>(
    auth,
    endpoint,
    "Error in getRetailProductLifecycle API call:",
  );
};

export const getRetailLifecycleFilters = async ({
  auth,
  claimed = false,
}: ApiParams & { claimed?: boolean }): Promise<RetailProductTableFilters> => {
  const queryParams = new URLSearchParams();
  if (claimed) queryParams.append("claimed", 'true');
  return fetchApi<{ l0: string[]; l1: string[]; gri: string[] }>(
    auth,
    `/retail_products/lifecycle_filters?${queryParams.toString()}`,
    "Error in getRetailLifecycleFilters API call:",
  );
};
