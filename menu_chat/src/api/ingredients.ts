import { getApi, parseFilters } from './base';
import { PaginationState, SortingState } from "@tanstack/react-table";

interface BarChartItem {
  id: number;
  label: string;
  color: string;
  value: number;
  secondaryValue: number;
}

interface QuarterData {
  value: number;
  percent: string | number;
}

interface CategoryData {
  category_name: string;
  quarters: { [key: string]: QuarterData };
}

interface CuisineData {
  id: string;
  category: string;
  item_count: number;
}

interface MostCommonPoductCategory {
  id: number;
  chart_data: CuisineData[];
}

interface RetailGrowthQuarter {
  name: string;
  count: number;
}

interface RetailGrowthData {
  total_count: number;
  quarters: RetailGrowthQuarter[];
  growth_percent: number;
  growth_prediction: number;
}

interface RetailGrowthResponse {
  id: number;
  retail_growth: RetailGrowthData;
}

export const fetchFastestGrowingIngredients = async({auth, sorting, pagination, search, filters} : IngredientsRequest) => {
  try {
    const params: Record<string, string[] | SortingState | PaginationState | string | undefined> = {
      sorting,
      pagination,
      search,
      ...parseFilters(filters)
    };

    const response = await getApi(auth).get('/ingredients/fastest_growing_ingredients', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

interface PairingTrendRequest {
  auth: string,
  sorting?: SortingState,
  pagination?: PaginationState,
  search?: string,
  filters?: string[]
}

export const pairingTrends = async({auth, sorting, pagination, search, filters} : PairingTrendRequest) => {
  try {
    const params: Record<string, string[] | SortingState | PaginationState | string | undefined> = {
      sorting,
      pagination,
      search,
      ...parseFilters(filters)
    };

    const response = await getApi(auth).get('/ingredients/pairing_trends', { params });
    return response
  }
  catch(error){
    console.log(error)
  }
}

export const menuAdoptionCurve = async({auth, filters} : {auth: string, filters?: string[],}) => {
  try {
    const q_params: Record<string, string[] | SortingState | PaginationState | string | undefined> = {
      ...parseFilters(filters)
    };

    const response = await getApi(auth).get('/ingredients/ingredients_by_stage', { params: q_params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const ingredientsInnovation = async({auth, sorting, pagination, search, filters, params} : IngredientsRequest) => {
  try {
    const q_params: Record<string, string[] | SortingState | PaginationState | string | undefined> = {
      ...params,
      sorting,
      pagination,
      search,
      ...parseFilters(filters)
    };

    const response = await getApi(auth).get('/ingredients/ingredients_innovation', { params: q_params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}

export const fetchMostTalkedAbout = async({auth, page, per_page, sort, filters, search} : {auth: string, page: number, per_page: number, sort: string, filters: object, search: string}) => {
  try {
    const response = await getApi(auth).get(`/ingredients/customer_insights/most_talked_about`, {
      params: {
        page,
        per_page,
        sort,
        filters,
        search
      }
    })
    return response.data
  }
  catch(error){
    console.log(error)
  }
}

interface IngredientsRequest {
  auth: string,
  sorting?: SortingState,
  pagination?: PaginationState,
  search?: string,
  filters?: string[],
  params?: Record<string, string | undefined>
}


export const fetchIngredients = async({auth, sorting, pagination, search, filters} : IngredientsRequest) => {
  try {
    const params: Record<string, string[] | SortingState | PaginationState | string | undefined> = {
      sorting,
      pagination,
      search,
      ...parseFilters(filters)
    };

    const response = await getApi(auth).get('/ingredients/', { params });
    return response.data;
  }
  catch(error){
    console.log(error)
  }
}


export const fetchMostLikedIngredients = async({auth, page, per_page, sort, filters, search} : {auth: string, page: number, per_page: number, sort: string, filters: object, search: string}) => {
  try {
    const response = await getApi(auth).get(`/ingredients/customer_insights/most_liked_ingredients`, {
      params: {
        page,
        per_page,
        sort,
        filters,
        search
      }
    })
    return response.data
  } catch (error) {
    console.log(error)
  }
}

interface IngredientRequest {
  auth: string,
  guid: string
}

export const fetchIngredient = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

interface IngPairingTrendRequest extends IngredientRequest {
  limit: number
}

export const fetchPairingTrendsForIng = async({auth, guid, limit} : IngPairingTrendRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/ingredient_pairing_trends`, {
      params: {limit}
    })
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchMenuTypes = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/menu_types`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchPenRateOverTime = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/pen_rate_over_time`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchConsummationHabbits = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/consummation_habbits`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchSocialSentiments = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/social_sentiments`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchSocialMediaSentiments = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/social_media_sentiments`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchMostLikedRetailers = async({auth} : {auth: string}) => {
  try {
    const response = await getApi(auth).get(`/ingredients/customer_insights/consumer_favorite_retailers`)
    return response.data
  }
  catch(error){
    console.log(error)
  }
}

interface RetailImage {
  img_url: string;
  name: string | null;
  offer_url: string | null;
}

interface RetailImagesResponse {
  id: number;
  carousel_images: RetailImage[];
}

export const fetchRetailImages = async ({ auth, ingredientId }: { auth: string; ingredientId: string }) => {
  try {
    const response = await getApi(auth).get<RetailImagesResponse>(`/ingredients/${ingredientId}/retails_images`);
    return response.data.carousel_images;
  } catch (error) {
    console.error("Error fetching retail images:", error);
    throw error;
  }
};

interface ManufacturerData {
  name: string;
  count: number;
  price: number;
}

interface RetailerData {
  name: string;
  count: number;
  price: number;
}

interface TopManufacturersResponse {
  id: number;
  top_brands: ManufacturerData[];
}

interface TopRetailersResponse {
  id: number;
  top_retails_by_banner_name: RetailerData[];
}

export const topManufacturers = async ({ auth, ingredientId }: { auth: string; ingredientId: string }) => {
  try {
    const response = await getApi(auth).get<TopManufacturersResponse>(
      `/ingredients/${ingredientId}/top_manufacturers`
    );

    const maxValue = Math.max(
      ...response.data.top_brands.map((item) => item.count),
    );

    const data: BarChartItem[] = response.data.top_brands.map((item, index) => ({
      id: index + 1,
      label: item.name,
      color: item.count === maxValue ? "#A2D161" : "#D2D0BC",
      value: item.count,
      secondaryValue: item.price,
    }));

    return { maxValue: roundUpToNiceNumber(maxValue), data };
  } catch (error) {
    console.error("Error fetching top manufacturers:", error);
    throw error;
  }
};

export const topRetailers = async ({ auth, ingredientId }: { auth: string; ingredientId: string }) => {
  try {
    const response = await getApi(auth).get<TopRetailersResponse>(
      `/ingredients/${ingredientId}/top_retailers`
    );

    const maxValue = Math.max(
      ...response.data.top_retails_by_banner_name.map((item) => item.count),
    );

    const data: BarChartItem[] = response.data.top_retails_by_banner_name.map((item, index) => ({
      id: index + 1,
      label: item.name,
      color: item.count === maxValue ? "#A2D161" : "#D2D0BC",
      value: item.count,
      secondaryValue: item.price,
    }));

    return { maxValue: roundUpToNiceNumber(maxValue), data };
  } catch (error) {
    console.error("Error fetching top retailers:", error);
    throw error;
  }
};

function roundUpToNiceNumber(num: number): number {
  const power = Math.pow(10, Math.floor(Math.log10(num)));
  return Math.ceil(num / power) * power;
}

export const highestGrowingIngredientByCategory = async ({ auth, ingredientId }: { auth: string; ingredientId: string }) => {
  try {
    const response = await getApi(auth).get<CategoryData[]>(`/ingredients/${ingredientId}/highest_growing_ingredient_by_category`);

    const allQuarterKeys = Array.from(
      new Set(response.data.flatMap(item => Object.keys(item.quarters)))
    ).sort((a, b) => {
      const [yearA, qA] = a.split('_q').map(Number);
      const [yearB, qB] = b.split('_q').map(Number);
      return yearA - yearB || qA - qB;
    });

    const formatQuarterLabel = (quarter: string): string => {
      const [year, quarterNum] = quarter.split('_q');
      return `Q${quarterNum} ${year}`;
    };

    const data = response.data.map((item, idx) => ({
      id: idx + 1,
      label: item.category_name,
      color: ["#FFBE05", "#FF6D56", "#8BC539", "#D273D8", "#3CA9E2", "#CBAF92"][idx % 6],
      data: allQuarterKeys.map(q => {
        const raw = item.quarters[q];
        return raw ? raw.value : 0;
      }),
      percentData: allQuarterKeys.map(q => {
        const raw = item.quarters[q];
        if (!raw) return 0;
        const percent = raw.percent;
        return typeof percent === 'string' ? parseFloat(percent.replace('%', '')) : percent;
      }),
    }));

    const formattedLabels = allQuarterKeys.map(formatQuarterLabel);

    return { labels: formattedLabels, data };
  } catch (error) {
    console.error("Error fetching highest growing ingredient by category:", error);
    throw error;
  }
};

function generateColor(index: number): string {
  const hue = (index * 137.508) % 360;
  return `hsl(${hue}, 70%, 60%)`;
}

export const fetchProductCategoryChartData = async ({ auth, ingredientId }: { auth: string; ingredientId: string }) => {
  try {
    const response = await getApi(auth).get<MostCommonPoductCategory>(`/ingredients/${ingredientId}/most_common_product_category`);
    const baseColors = [
      "#FFBE05",
      "#FF6D56",
      "#8BC539",
      "#D273D8",
      "#3CA9E2",
      "#CBAF92",
      "#FF9A8B",
      "#66BB6A",
      "#AB47BC",
      "#42A5F5",
      "#FFCA28",
      "#EC407A",
      "#26A69A",
      "#EF5350",
      "#7E57C2",
    ];

    const data = response.data.chart_data
      .filter(item => item.item_count > 0)
      .map((item, index) => ({
        label: item.category,
        value: item.item_count,
        color: index < baseColors.length ? baseColors[index] : generateColor(index),
      }));

    return data;
  } catch (error) {
    console.error("Error fetching top cuisines chart data:", error);
    throw error;
  }
};

export const fetchRetailGrowth = async ({ auth, ingredientId }: { auth: string; ingredientId: string }) => {
  try {
    const response = await getApi(auth).get<RetailGrowthResponse>(`/ingredients/${ingredientId}/retail_growth`);

    const retailGrowth = response.data.retail_growth;

    const labels = retailGrowth.quarters.map(quarter => quarter.name);
    const data = retailGrowth.quarters.map(quarter => quarter.count);

    const lineChartData = [{
      id: 1,
      label: "Retail Growth",
      color: "#FF6D56",
      data: data,
    }];

    return {
      lineChartData,
      labels,
      growthPercent: retailGrowth.growth_percent,
      growthPrediction: retailGrowth.growth_prediction,
    };
  } catch (error) {
    console.error("Error fetching retail growth:", error);
    throw error;
  }
};

export const fetchMentionsByState = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/mentions_by_state`);
    return response.data;
  } catch (error) {
    console.error("Error fetching mentions by state:", error);
    throw error;
  }
};

export const fetchConsumerExperience = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/consumer_experience`);
    return response.data;
  } catch (error) {
    console.error("Error fetching consumer experience:", error);
  }
}
export const fetchConsumedByGeneration = async ({ auth, guid }: { auth: string; guid: string }) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/consumed_by_generation`);
    return response.data;
  } catch (error) {
    console.error("Error fetching consumed by generation:", error);
    throw error;
  }
};

export const fetchIngredientMenuItems = async ({ auth, guid, page, pageSize, secondary_filter }: { auth: string; guid: string, page: number, pageSize: number, secondary_filter: string }) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/items`, {
      params: {
        page,
        per_page: pageSize,
        secondary_filter
      }
    })
    return response.data

  } catch(error){
    console.log(error)
  }
}


interface ProductTableData {
  id: number;
  name: string;
  category: string;
  product: { name: string; image_urls: string[] };
  price: string;
  created_at: string;
  upc?: string;
  listing_url?: string;
}

interface ProductsTableResponse {
  id: number;
  products_table: {
    data: ProductTableData[];
    recordsTotal: number;
    recordsFiltered: number;
  };
}

interface ProductsTableRequest {
  auth: string;
  ingredientId: string;
  pagination?: PaginationState;
  search?: string;
  sorting?: SortingState;
}

export const fetchProductsTable = async({auth, ingredientId, pagination, search, sorting} : ProductsTableRequest) => {
  try {
    const response = await getApi(auth).get<ProductsTableResponse>(
      `/ingredients/${ingredientId}/products_table`,
      {
        params: {
          page: pagination?.pageIndex ? pagination.pageIndex + 1 : 1,
          per_page: pagination?.pageSize,
          search,
          sort: {
            column: sorting?.[0]?.id,
            direction: sorting?.[0]?.desc ? "desc" : "asc",
          },
        },
      }
    )
    return response.data.products_table
  } catch(error){
    console.error("Error fetching products table:", error)
    throw error
  }
}

export const fetchPopularThemesByGeneration = async({auth} : {auth: string}) => {
  try {
    const response = await getApi(auth).get('/ingredients/popular_themes_by_generation');
    return response.data;
  } catch (error) {
    console.error("Error fetching popular themes by generation:", error);
    throw error;
  }
}

export const fetchIngredientsSocialMediaMentions = async({auth} : {auth: string}) => {
  try {
    const response = await getApi(auth).get('/ingredients/social_media_insights/ingredient_social_media_mentions');
    return response.data;
  } catch (error) {
    console.error("Error fetching ingredients social media mentions:", error);
    throw error;
  }
}

export const fetchIngredientTrends = async({auth} : {auth: string}) => {
  try {
    const response = await getApi(auth).get('/ingredients/social_media_insights/ingredient_trends');
    return response.data;
  } catch (error) {
    console.error("Error fetching ingredient trends:", error);
    throw error;
  }
}

export const fetchIngredientsMostPopularThemesByAges = async({auth} : {auth: string}) => {
  try {
    const response = await getApi(auth).get('/ingredients/social_media_insights/most_popular_themes_by_ages');
    return response.data;
  } catch (error) {
    console.error("Error fetching ingredients most popular themes by ages:", error);
    throw error;
  }
}

export const fetchIngredientSocialMediaTrends = async({auth, search, filters, sort, page, perPage} : {auth: string, search: string, filters: string[], sort: string, page: number, perPage: number}) => {
  try {
    const response = await getApi(auth).get('/ingredients/social_media_insights/ingredient_social_media_trends', {
      params: {
        search,
        filters,
        sort,
        page,
        per_page: perPage
      }
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching ingredient social media trends:", error);
    throw error;
  }
}

export const fetchSocialMediaPopularity = async({auth, guid} : {auth: string, guid: string}) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/social_media_popularity`);
    return response.data;
  } catch (error) {
    console.error("Error fetching social media popularity:", error);
    throw error;
  }
}

export const fetchSocialMediaConversations = async({auth, guid} : {auth: string, guid: string}) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/social_media_conversations`);
    return response.data;
  } catch (error) {
    console.error("Error fetching social media conversations:", error);
    throw error;
  }
}

export const fetchSocialMediaWords = async({auth, guid} : {auth: string, guid: string}) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/social_media_words`);
    return response.data;
  } catch (error) {
    console.error("Error fetching social media words:", error);
    throw error;
  }
}
export const fetchSocialMediaPosts = async({auth, guid} : {auth: string, guid: string}) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/social_media_posts`);
    return response.data;
  } catch (error) {
    console.error("Error fetching social media posts:", error);
    throw error;
  }
}

export const fetchRestaurantTypes = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/restaurant_types`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchRegionsDistro = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/regions_distro`)
    return response.data
  } catch(error){
    console.log(error)
  }
}


export const fetchIngredientCategories = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/ingredient_categories`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchIngredientSubcategories = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/ingredient_subcategories`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchChainTypes = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/chain_types_distro`)
    return response.data
  } catch(error){
    console.log(error)
  }
}

export const fetchMenuItems = async({auth, guid} : IngredientRequest) => {
  try {
    const response = await getApi(auth).get(`/ingredients/${guid}/menu_items`)
    return response.data
  } catch(error){
    console.log(error)
  }
}