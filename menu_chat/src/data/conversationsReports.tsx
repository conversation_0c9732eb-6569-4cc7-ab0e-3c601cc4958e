import { IconReport } from "@/components/icons";

//TO IMPLEMENT
export const CONVERSATIONS_FILTER = [
  {
    key: "conversations",
    label: (
      <>
        <IconReport size={20} className="text-neutral-500" /> Conversations
      </>
    ),
    filters: [
      { label: "Yes", value: "conversations-true" },
      { label: "No", value: "conversations-false" },
    ],
  },
];

//TO IMPLEMENT
export const CONVERSATIONS_SORT = [
  {
    key: "conversations",
    value: "conversations",
    label: "Conversations",
  },
];

//TO IMPLEMENT
export const REPORTS_FILTER = [
  {
    key: "reports",
    label: (
      <>
        <IconReport size={20} className="text-neutral-500" /> Reports
      </>
    ),
    filters: [
      { label: "Yes", value: "reports-true" },
      { label: "No", value: "reports-false" },
    ],
  },
];

//TO IMPLEMENT
export const REPORTS_SORT = [
  {
    key: "reports",
    value: "reports",
    label: "Reports",
  },
];
