import {
  IconMenu,
  IconChefHat,
  IconHealthy,
  IconCircleUp,
  IconCircleDown,
  IconCircleFilled,
  IconTaste2,
} from "@/components/icons";

export const MENUITEMS_MENU = [
  {
    slug: "/menu-items/industry-insights",
    title: "Industry Insights",
  },
  {
    slug: "/menu-items/consumer-insights",
    title: "Consumer Insights",
  },
  {
    slug: "/menu-items/all-menu-items",
    title: "All Menu Items",
  },
];

export const MENUITEMS_FILTER = [
  {
    label: (
      <>
        <IconMenu size={20} className="text-neutral-500" /> Meal Type
      </>
    ),
    filters: [
      { label: "Option 1", value: "meal-option-1" },
      { label: "Option 2", value: "meal-option-2" },
      { label: "Option 3", value: "meal-option-3" },
    ],
  },
  {
    label: (
      <>
        <IconChefHat size={20} className="text-neutral-500" /> Cuisine Type
      </>
    ),
    filters: [
      { label: "Option 1", value: "cusine-option-1" },
      { label: "Option 2", value: "cusine-option-2" },
      { label: "Option 3", value: "cusine-option-3" },
    ],
  },
  {
    label: (
      <>
        <IconHealthy size={20} className="text-neutral-500" /> Healthy
      </>
    ),
    filters: [
      { label: "Option 1", value: "healthy-option-1" },
      { label: "Option 2", value: "healthy-option-2" },
      { label: "Option 3", value: "healthy-option-3" },
    ],
  },
  {
    label: (
      <>
        <IconCircleFilled size={20} className="text-neutral-500" /> Texture
      </>
    ),
    filters: [
      { label: "Option 1", value: "texture-option-1" },
      { label: "Option 2", value: "texture-option-2" },
      { label: "Option 3", value: "texture-option-3" },
    ],
  },
  {
    label: (
      <>
        <IconTaste2 size={20} className="text-neutral-500" /> Flavor
      </>
    ),
    filters: [
      { label: "Option 1", value: "flavor-option-1" },
      { label: "Option 2", value: "flavor-option-2" },
      { label: "Option 3", value: "flavor-option-3" },
    ],
  },
];

export const MENUITEMS_SORT = [
  { label: "Penetration", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "penetration-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "penetration-lowest",
  },
  { label: "", value: "divider" },
  { label: "Social Mentions", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "social-mentions-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "social-mentions-lowest",
  },
  { label: "", value: "divider" },
  { label: "Foodservice Growth", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "foodservice-growth-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "foodservice-growth-lowest",
  },
  { label: "", value: "divider" },
  { label: "Retail Growth", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "retail-growth-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "retail-growth-lowest",
  },
];

export const getMenuItemstDetailsMenu = (menuItemsSlug: string) => [
  {
    slug: `/menu-items/details/${menuItemsSlug}`,
    title: "General",
  },
  {
    slug: `/menu-items/details/${menuItemsSlug}/consumer-insights`,
    title: "Consumer Insights",
  },
  {
    slug: `/menu-items/details/${menuItemsSlug}/on-menus`,
    title: "On Menus",
  },
];
