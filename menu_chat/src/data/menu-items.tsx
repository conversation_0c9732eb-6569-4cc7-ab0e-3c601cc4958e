import {
  IconMenu,
  IconChefHat,
  // IconHealthy,
  IconCircleUp,
  IconCircleDown,
  IconCircleFilled,
  IconTaste2,
  IconWorkflow,
  IconAroma,
  IconAppearance,
  IconCompany,
  IconLocation,
  IconPerson,
} from "@/components/icons";

export const categoryList = [
  "sides",
  "beverages",
  "desserts",
  "appetizers",
  "entrees",
  "other",
];
export const subcategoryList = [
  "beans",
  "water",
  "other desserts",
  "soups",
  "rice",
  "burgers",
  "sandwiches",
  "potato-based",
  "bread appetizers",
  "coffee",
  "pork entrees",
  "veggie appetizers",
  "juice",
  "high protein appetizers",
  "beef-based entrees",
  "milk",
  "lemonade",
  "other alcoholic",
  "other entrees",
  "breakfast entrees",
  "other protein-based entrees",
  "breakfast/pastry desserts",
  "cobbler/crisp",
  "brownies/bars",
  "combo desserts",
  "hot tea",
  "pie",
  "dips",
  "mexican entrees",
  "chicken-based entrees",
  "salads",
  "specialty coffee",
  "cake",
  "sparkling wine",
  "shared appetizers",
  "pizza",
  "cheesecake",
  "soft drinks",
  "straight alcohol",
  "fish-based entrees",
  "cookies",
  "other appetizers",
  "hot chocolate",
  "specialty iced coffee",
  "veggie",
  "other sides",
  "cocktails",
  "seafood entrees",
  "veggie entrees",
  "fruit",
  "sports/energy drinks",
  "frozen desserts",
  "noodle dishes",
  "iced tea",
  "hard seltzers",
  "other non-alcoholic",
  "iced coffee",
  "beer",
  "smoothie/boba",
  "mocktails",
  "nachos",
  "other",
  "protein",
  "sauce/topping",
  "wine",
  "sparkling water",
  "wings",
]; //TODO depend from category?

export const cuisineTypes = [
  "american",
  "asian",
  "asian fusion",
  "austrian",
  "british",
  "cajun/creole",
  "caribbean",
  "chinese",
  "cuban",
  "danish",
  "european",
  "french",
  "german",
  "greek",
  "hawaiian",
  "indian",
  "international",
  "irish",
  "italian",
  "italian-american",
  "japanese",
  "jewish",
  "korean",
  "latin american",
  "mediterranean",
  "mexican",
  "tex-mex",
  "middle eastern",
  "russian",
  "spanish",
  "thai",
  "universal",
  "unknown",
  "vietnamese",
];

const LtoMealTypes = [
  "3 For Lunch Combos",
  "Add Ons",
  "American",
  "Appetizers",
  "Bagels",
  "Bakery",
  "Beers",
  "Beverages",
  "BOGO",
  "Bowls",
  "Breakfast",
  "Burritos",
  "Burgers",
  "Buckets",
  "Catering",
  "Cheesecake",
  "Chicken",
  "Chicken Nuggets",
  "Combos",
  "Confectionery",
  "Cookies",
  "Crunchwrap Supreme Reimagined",
  "Desserts",
  "Deals",
  "Dips",
  "Donuts",
  "Dinner",
  "Entrees",
  "Footlongs",
  "Fries",
  "Game Day Special",
  "Holiday",
  "Ice Cream",
  "Kids Menu",
  "Limited Time",
  "Lunch",
  "Meats",
  "Merchandise",
  "National Pizza Week",
  "New",
  "Pastries",
  "Pastas",
  "Pies",
  "Pizza",
  "Quesadillas",
  "Salads",
  "Sandwiches",
  "Sauces",
  "Seasonal",
  "Seafood",
  "Shakes",
  "Sides",
  "Snacks",
  "Soups",
  "Specials",
  "Sweets",
  "Tacos",
  "Tostadas",
  "Value Pick",
  "Wing Bundles",
  "Wings",
  "Wraps",
];

export const tasteList = [
  "bitter",
  "buttery",
  "citrusy",
  "earthy",
  "fiery",
  "fruity",
  "mellow",
  "mild",
  "nutty",
  "refreshing",
  "rich",
  "robust",
  "salty",
  "savory",
  "sharp",
  "smoky",
  "sour",
  "spicy",
  "sweet",
  "tangy",
  "tart",
  "umami",
  "zesty",
];
export const aromaList = [
  "aged",
  "aromatic",
  "bready",
  "briny",
  "buttery",
  "caramelized",
  "citrusy",
  "earthy",
  "fermented",
  "floral",
  "fresh",
  "fruity",
  "garlicky",
  "herbal",
  "minty",
  "nutty",
  "pungent",
  "roasted",
  "savory",
  "smoky",
  "spicy",
  "sweet",
  "tangy",
  "woody",
];
export const textureList = [
  "chewy",
  "creamy",
  "crispy",
  "crumbly",
  "crunchy",
  "dense",
  "firm",
  "flaky",
  "fluffy",
  "gooey",
  "grainy",
  "juicy",
  "light",
  "moist",
  "silky",
  "slurpy",
  "smooth",
  "soft",
  "sticky",
  "succulent",
  "tender",
];
export const appearanceList = [
  "bubbly",
  "caramelized",
  "charred",
  "chunky",
  "cracked",
  "crystalline",
  "flaky",
  "glazed",
  "glossy",
  "grainy",
  "layered",
  "marbled",
  "matte",
  "molded",
  "powdered",
  "pristine",
  "rough",
  "rustic",
  "smooth",
  "stringy",
];

export const brandList = ["Starbucks", "McDonald's", "Burger King", "Wendy's"];

export const recipeList = [
  "Chocolate Chip Cookie",
  "Chocolate Cookie",
  "Tomato Soup",
  "Lasagna",
];
export const authorList = [
  "All authors",
  "John Doe",
  "Jane Doe",
  "John Smith",
  "Jane Smith",
];

export const MENUITEMS_MENU = [
  {
    slug: "/menu-items/industry-insights",
    title: "Industry Insights",
  },
  // {
  //   slug: "/menu-items/consumer-insights",
  //   title: "Consumer Insights",
  // },
  // {
  //   slug: "/menu-items/social-media-insights",
  //   title: "Social Media Insights",
  // },
  {
    slug: "/menu-items/all-menu-items",
    title: "All Menu Items",
  },
];

function listToFilterOptions(key: string, list: string[]) {
  const sortedList = list.sort((a, b) => a.localeCompare(b));
  return sortedList.map((item) => ({
    label: item,
    value: `${key}-${item}`,
  }));
}

export const MENUITEMS_FILTER = [
  {
    key: "category",
    label: (
      <>
        <IconWorkflow size={20} className="text-neutral-500" /> Category
      </>
    ),
    filters: listToFilterOptions("category", categoryList),
  },
  {
    //TODO depend from category?
    key: "subcategory",
    label: (
      <>
        <IconWorkflow size={20} className="text-neutral-500" /> SubCategory
      </>
    ),
    filters: listToFilterOptions("subcategory", subcategoryList),
  },
  {
    key: "cuisine_type",
    label: (
      <>
        <IconChefHat size={20} className="text-neutral-500" /> Cuisine Type
      </>
    ),
    filters: listToFilterOptions("cuisine_type", cuisineTypes),
  },
  {
    key: "aroma",
    label: (
      <>
        <IconAroma size={20} className="text-neutral-500" /> Aroma
      </>
    ),
    filters: listToFilterOptions("aroma", aromaList),
  },
  {
    key: "texture",
    label: (
      <>
        <IconCircleFilled size={20} className="text-neutral-500" /> Texture
      </>
    ),
    filters: listToFilterOptions("texture", textureList),
  },
  {
    key: "taste",
    label: (
      <>
        <IconTaste2 size={20} className="text-neutral-500" /> Taste
      </>
    ),
    filters: listToFilterOptions("taste", tasteList),
  },
  {
    key: "appearance",
    label: (
      <>
        <IconAppearance size={20} className="text-neutral-500" /> Appearance
      </>
    ),
    filters: listToFilterOptions("appearance", appearanceList),
  },
];

export const MENUITEMS_ITEMS_FILTER = [
  {
    key: "category",
    label: (
      <>
        <IconWorkflow size={20} className="text-neutral-500" /> Category
      </>
    ),
    filters: listToFilterOptions("category", categoryList),
  },
];

export const LTO_FILTER = [
  {
    key: "meal_type",
    label: (
      <>
        <IconMenu size={20} className="text-neutral-500" /> Meal Type
      </>
    ),
    filters: listToFilterOptions("meal_type", LtoMealTypes),
  },
  {
    key: "cuisine",
    label: (
      <>
        <IconChefHat size={20} className="text-neutral-500" /> Cuisine
      </>
    ),
    filters: listToFilterOptions("cuisine", cuisineTypes),
  },
];

export const MENUITEMS_SORT = [
  { label: "Penetration", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "penetration-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "penetration-lowest",
  },
  { label: "", value: "divider" },
  { label: "Social Mentions", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "social-mentions-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "social-mentions-lowest",
  },
  { label: "", value: "divider" },
  { label: "Foodservice Growth", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "foodservice-growth-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "foodservice-growth-lowest",
  },
  { label: "", value: "divider" },
  { label: "Retail Growth", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "retail-growth-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "retail-growth-lowest",
  },
];

export const SOCIAL_MEDIA_FILTER = [
  {
    key: "brand",
    label: (
      <>
        <IconCompany size={20} className="text-neutral-500" /> Brand
      </>
    ),
    filters: listToFilterOptions("brand", brandList),
  },
  {
    key: "recipe",
    label: (
      <>
        <IconChefHat size={20} className="text-neutral-500" /> Recipe
      </>
    ),
    filters: listToFilterOptions("recipe", recipeList),
  },
  {
    key: "author",
    label: (
      <>
        <IconPerson size={20} className="text-neutral-500" /> Author
      </>
    ),
    filters: listToFilterOptions("author", authorList),
  },
];

export const SOCIAL_MEDIA_SORT = [
  {
    key: "engagement&views",
    value: "heading",
    label: "Engagement & Views",
  },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "engagement&views-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "engagement&views-lowest",
  },
  { label: "", value: "divider" },
  {
    key: "publishing_date",
    value: "heading",
    label: "Publishing Date",
  },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Latest
      </>
    ),
    value: "publishing_date-latest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Earliest
      </>
    ),
    value: "publishing_date-earliest",
  },
];

export const getMenuItemstDetailsMenu = (menuItemsSlug: string) => [
  {
    slug: `/menu-items/details/${menuItemsSlug}`,
    title: "General",
  },
  {
    slug: `/menu-items/details/${menuItemsSlug}/distribution-overview`,
    title: "Distribution Overview",
  },
  {
    slug: `/menu-items/details/${menuItemsSlug}/consumer-insights`,
    title: "Consumer Insights",
  },
  {
    slug: `/menu-items/details/${menuItemsSlug}/social-media-insights`,
    title: "Social Media Insights",
  },
  {
    slug: `/menu-items/details/${menuItemsSlug}/on-menus`,
    title: "On Menus",
  },
];

export const getRestaurantMenu = (restaurantSlug: string) => [
  {
    slug: `/menu-items/restaurant/${restaurantSlug}`,
    title: "General",
  },
  {
    slug: `/menu-items/restaurant/${restaurantSlug}/menu`,
    title: "Menu",
  },
];

export const MENU_ITEMS_DATA = {
  ratatouille: {
    name: "Ratatouille",
    medianPrice: "$11,35",
    ingredients: [
      "Eggplant",
      "Zucchini",
      "Bell Peppers",
      "Tomatoes",
      "Onions",
      "Garlic",
      "Olive Oil",
      "Herbs",
      "Basil",
      "Thyme",
    ],
  },
};

export const getMenuItemData = (menuItem: string) => {
  return MENU_ITEMS_DATA[menuItem as keyof typeof MENU_ITEMS_DATA] || null;
};

export const RESTAURANT_TYPES_FILTER = [
  {
    key: "locations_number",
    label: (
      <>
        <IconLocation size={20} className="text-neutral-500" /> Locations Number
      </>
    ),
    filters: [
      { label: "1-10", value: "1-10" },
      { label: "11-50", value: "11-50" },
      { label: "51-100", value: "51-100" },
      { label: "101-200", value: "101-200" },
      { label: "201+", value: "201+" },
    ],
  },
  {
    key: "menu_items_number",
    label: (
      <>
        <IconMenu size={20} className="text-neutral-500" /> Menu Items Number
      </>
    ),
    filters: [
      { label: "1-50", value: "1-50" },
      { label: "51-100", value: "51-100" },
      { label: "101-200", value: "101-200" },
      { label: "201+", value: "201+" },
    ],
  },
];

export const RESTAURANT_TYPES_SORT = [
  {
    key: "locations_number",
    value: "heading",
    label: "Locations Number",
  },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "locations_number-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "locations_number-lowest",
  },
  {
    key: "menu_items_number",
    value: "heading",
    label: "Menu Items Number",
  },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "menu_items_number-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "menu_items_number-lowest",
  },
  {
    key: "social_mentions",
    value: "heading",
    label: "Social Mentions",
  },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "social_mentions-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "social_mentions-lowest",
  },
];
