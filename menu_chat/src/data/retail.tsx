import {
  IconMenu,
  IconChefHat,
  IconHealthy,
  IconCircleUp,
  IconCircleDown,
  IconCircleFilled,
  IconTaste2,
  IconCompany,
  IconPerson,
} from "@/components/icons";

export const brandList = ["Starbucks", "McDonald's", "Burger King", "Wendy's"];

export const RETAIL_ONSHELF_SORT = [
  { label: "Name", value: "heading" },
  { label: (<> <IconCircleUp size={20} className="text-neutral-500" /> A - Z </>), value: "name:asc", },
  { label: (<> <IconCircleDown size={20} className="text-neutral-500" /> Z - A </>), value: "name:desc", },
  { label: "", value: "divider" }, { label: "Brand", value: "heading" },
  { label: (<> <IconCircleUp size={20} className="text-neutral-500" /> A - Z </>), value: "brand:asc", },
  { label: (<> <IconCircleDown size={20} className="text-neutral-500" /> Z - A </>), value: "brand:desc", },
  { label: "", value: "divider" }, { label: "Retailer", value: "heading" },
  { label: (<> <IconCircleUp size={20} className="text-neutral-500" /> A - Z </>), value: "retailer_name:asc", },
  { label: (<> <IconCircleDown size={20} className="text-neutral-500" /> Z - A </>), value: "retailer_name:desc", },
  { label: "", value: "divider" }, { label: "Category", value: "heading" },
  { label: (<> <IconCircleUp size={20} className="text-neutral-500" /> A - Z </>), value: "l1:asc", },
  { label: (<> <IconCircleDown size={20} className="text-neutral-500" /> Z - A </>), value: "l1:desc", },
  { label: "", value: "divider" }, { label: "Price", value: "heading" },
  { label: (<> <IconCircleUp size={20} className="text-neutral-500" /> Highest </>), value: "price:asc", },
  { label: (<> <IconCircleDown size={20} className="text-neutral-500" /> Lowest </>), value: "price:desc", },
];

export const recipeList = [
  "Chocolate Chip Cookie",
  "Chocolate Cookie",
  "Tomato Soup",
  "Lasagna",
];
export const authorList = [
  "All authors",
  "John Doe",
  "Jane Doe",
  "John Smith",
  "Jane Smith",
];

export const RETAIL_MENU = [
  {
    slug: "/retail/industry-insights",
    title: "Industry Insights",
  },
  // {
  //   slug: "/retail/consumer-insights",
  //   title: "Consumer Insights",
  // },
  // {
  //   slug: "/retail/social-media-insights",
  //   title: "Social Media Insights",
  // },
  {
    slug: "/retail/all-products",
    title: "All Products",
  },
];

export const getRetailDetailsMenu = (retailSlug: string) => [
  {
    slug: `/retail/details/${retailSlug}`,
    title: "General",
  },
  {
    slug: `/retail/details/${retailSlug}/consumer-insights`,
    title: "Consumer Insights",
  },
  {
    slug: `/retail/details/${retailSlug}/social-media-insights`,
    title: "Social Media Insights",
  },
  {
    slug: `/retail/details/${retailSlug}/on-shelf`,
    title: "On the Shelf",
  },
];

function listToFilterOptions(key: string, list: string[]) {
  const sortedList = list.sort((a, b) => a.localeCompare(b));
  return sortedList.map((item) => ({
    label: item,
    value: `${key}-${item}`,
  }));
}

export const RETAIL_FILTER = [
  {
    label: (
      <>
        <IconMenu size={20} className="text-neutral-500" /> Meal Type
      </>
    ),
    filters: [
      { label: "Option 1", value: "meal-option-1" },
      { label: "Option 2", value: "meal-option-2" },
      { label: "Option 3", value: "meal-option-3" },
    ],
  },
  {
    label: (
      <>
        <IconChefHat size={20} className="text-neutral-500" /> Cuisine Type
      </>
    ),
    filters: [
      { label: "Option 1", value: "cusine-option-1" },
      { label: "Option 2", value: "cusine-option-2" },
      { label: "Option 3", value: "cusine-option-3" },
    ],
  },
  {
    label: (
      <>
        <IconHealthy size={20} className="text-neutral-500" /> Healthy
      </>
    ),
    filters: [
      { label: "Option 1", value: "healthy-option-1" },
      { label: "Option 2", value: "healthy-option-2" },
      { label: "Option 3", value: "healthy-option-3" },
    ],
  },
  {
    label: (
      <>
        <IconCircleFilled size={20} className="text-neutral-500" /> Texture
      </>
    ),
    filters: [
      { label: "Option 1", value: "texture-option-1" },
      { label: "Option 2", value: "texture-option-2" },
      { label: "Option 3", value: "texture-option-3" },
    ],
  },
  {
    label: (
      <>
        <IconTaste2 size={20} className="text-neutral-500" /> Flavor
      </>
    ),
    filters: [
      { label: "Option 1", value: "flavor-option-1" },
      { label: "Option 2", value: "flavor-option-2" },
      { label: "Option 3", value: "flavor-option-3" },
    ],
  },
];

export const RETAIL_SORT = [
  { label: "Name", value: "gri" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> A - Z
      </>
    ),
    value: "gri:asc",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Z - A
      </>
    ),
    value: "gri:desc",
  },
  { label: "", value: "divider" },
  { label: "Main Category", value: "l0" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> A - Z
      </>
    ),
    value: "l0:asc",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Z - A
      </>
    ),
    value: "l0:desc",
  },
  { label: "", value: "divider" },
  { label: "Category", value: "l1" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> A - Z
      </>
    ),
    value: "l1:asc",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Z - A
      </>
    ),
    value: "l1:desc",
  },
  { label: "", value: "divider" },
  
  { label: "", value: "divider" },
  { label: "Product Count", value: "products_count" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "products_count:asc",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "products_count:desc",
  },
];

export const SOCIAL_MEDIA_FILTER = [
  {
    key: "brand",
    label: (
      <>
        <IconCompany size={20} className="text-neutral-500" /> Brand
      </>
    ),
    filters: listToFilterOptions("brand", brandList),
  },
  {
    key: "recipe",
    label: (
      <>
        <IconChefHat size={20} className="text-neutral-500" /> Recipe
      </>
    ),
    filters: listToFilterOptions("recipe", recipeList),
  },
  {
    key: "author",
    label: (
      <>
        <IconPerson size={20} className="text-neutral-500" /> Author
      </>
    ),
    filters: listToFilterOptions("author", authorList),
  },
];

export const SOCIAL_MEDIA_SORT = [
  {
    key: "engagement&views",
    value: "heading",
    label: "Engagement & Views",
  },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "engagement&views-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "engagement&views-lowest",
  },
  { label: "", value: "divider" },
  {
    key: "publishing_date",
    value: "heading",
    label: "Publishing Date",
  },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Latest
      </>
    ),
    value: "publishing_date-latest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Earliest
      </>
    ),
    value: "publishing_date-earliest",
  },
];
