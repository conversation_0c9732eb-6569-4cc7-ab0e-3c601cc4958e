import { IconCircleUp, IconCircleDown, IconWorkflow } from "@/components/icons";

// export const categoryList = [
//   "sides",
//   "beverages",
//   "desserts",
//   "appetizers",
//   "entrees",
//   "other",
// ];

export const typeList = ["Ingredient", "MenuItem", "RetailProduct"];

function listToFilterOptions(key: string, list: string[]) {
  return list.map((item) => ({
    label: item,
    value: `${key}-${item}`,
  }));
}

export const SEARCH_FILTER = [
  // todo: Need Category List for All Models
  // {
  //   label: (
  //     <>
  //       <IconWorkflow size={20} className="text-neutral-500" /> Category
  //     </>
  //   ),
  //   filters: listToFilterOptions("category", categoryList),
  // },
  {
    label: (
      <>
        <IconWorkflow size={20} className="text-neutral-500" /> Type
      </>
    ),
    filters: listToFilterOptions("model_type", typeList),
  },
];

export const SEARCH_SORT = [
  { label: "Sort by", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "search-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "search-lowest",
  },
];
