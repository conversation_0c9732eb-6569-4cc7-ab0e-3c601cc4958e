import {
  IconCircleUp,
  IconCircleDown,
  IconWorkflow,
  IconFilter,
  IconChefHat,
  IconLocation
} from "@/components/icons";
import { cuisineTypes } from "@/data/menu-items";
import titleCase from "voca/title_case";
export const typeList = ["Ingredient", "MenuItem", "RetailProduct"];
export const frequencyOptions = ['1-10 locations', '10-100 locations', '100+ locations']
export const regions = [
  "California",
  "Great Lakes",
  "Mid South",
  "North East",
  "Plains",
  "South Central",
  "South East",
  "West",
  "Atlanta (Region)",
  "Austin/San Antonio Region",
  "Bay Area",
  "Chicago Region",
  "Dallas - Houston - Prosciutto",
  "Kennesaw - GA",
  "la greater area sample",
  "Las Vegas Region",
  "Los Angeles City",
  "Los Angeles (OC, DT, Santa Monica)",
  "Mexican Food - Sample",
  "Mexican Food - Texas - Sample",
  "Miami (key biscayne,coconut grove, beach)",
  "Midwest Region",
  "Midwest Region - Zonin <PERSON>",
  "New York greater area",
  "NY Region (Manhattan, Brooklyn)",
  "Pacific NW (Seattle, Portland)",
  "Phoenix Metro Area",
  "Saval",
  "South Carolina Region",
  "Video Tutorial Sample Region"
]

function listToFilterOptions(key: string, list: string[]) {
  return list.map((item) => ({
    label: titleCase(item),
    value: `${key}-${item}`,
  }));
}

export const SEARCH_FILTER = [
  // todo: Need Category List for All Models
  // {
  //   label: (
  //     <>
  //       <IconWorkflow size={20} className="text-neutral-500" /> Category
  //     </>
  //   ),
  //   filters: listToFilterOptions("category", categoryList),
  // },
  {
    label: (
      <>
        <IconWorkflow size={20} className="text-neutral-500" /> Type
      </>
    ),
    filters: listToFilterOptions("model_type", typeList),
  },
];

export const SEARCH_SORT = [
  { label: "Sort by", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "search-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "search-lowest",
  },
];

export const SEARCH_MENU = [
  {
    slug: "/search/all-text-results",
    title: "All Results",
  },
  {
    slug: "/search/similar-ingredients",
    title: "Similar Ingredients",
  },
  {
    slug: "/search/similar-menu-items",
    title: "Similar Menu Items",
  },
  {
    slug: "/search/similar-products",
    title: "Similar Products",
  },
];

export const SEARCH_FILTER_STATIC = [
  {
    key: "cuisine",
    label: (
      <>
        <IconChefHat size={20} className="text-neutral-500" /> Cuisine
      </>
    ),
    filters: listToFilterOptions("cuisine", cuisineTypes),
  },
  {
    key: "frequency",
    label: (
      <>
        <IconFilter size={20} className="text-neutral-500" /> Frequency
      </>
    ),
    filters: listToFilterOptions("frequency", frequencyOptions),
  },
  {
    key: "locations",
    label: (
      <>
        <IconLocation size={20} className="text-neutral-500" /> Locations
      </>
    ),
    filters: listToFilterOptions("locations", regions),
  },
];

export const SEARCH_SORT_STATIC = [
  { label: "Price", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "price-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "price-lowest",
  },
  { label: "Innovation Period", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Earliest
      </>
    ),
    value: "innovation_period-earliest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Latest
      </>
    ),
    value: "innovation_period-latest",
  },
];
