import {
  IconCircleUp,
  IconCircleDown,
  IconStar,
  IconIncrease,
  IconAroma,
  IconCircleFilled,
  IconWorkflow,
  IconTaste2,
  IconAppearance,
  IconFullscreen,
  IconPrint,
  IconPNG,
  IconImage,
  IconDocument,
  IconXLS,
} from "@/components/icons";

export interface IngredientQuestion {
  question: string;
}

export const ingredientList = [
  "Tomato",
  "Bacon",
  "Lemon",
  "Egg",
  "Avocado",
  "Fries",
  "Chocolate",
  "Butter",
  "Sandwich",
  "Spinach",
  "Paneer",
  "Feta",
  "Honey",
  "Cucumber",
  "Orange",
  "Salmon",
  "Cilantro",
  "Basil",
  "Mushroom",
  "Sugar",
  "Vanilla",
  "Lime",
  "Apple",
  "Ginger",
  "Coffee",
  "Strawberry",
  "Pineapple",
  "Salsa",
  "Olive",
  "Sesame",
  "Cherry",
  "Tuna",
  "Arugula",
  "Mango",
  "Coconut",
  "Cinnamon",
  "Breakfast",
  "Lemonade",
  "Banana",
  "Beer",
  "Cream Cheese",
  "Olive Oil",
  "Seasonal",
  "Wine",
  "Caramel",
  "Sour Cream",
  "Broccoli",
  "Espresso",
  "Celery",
  "Carrot",
  "Mint",
  "Whipped Cream",
  "Sourdough",
  "Kale",
  "Sweet Potato",
  "Goat Cheese",
  "Almond",
  "Truffle",
  "Ricotta",
  "Parsley",
  "Raspberry",
  "Buttermilk",
  "Maple",
  "Cauliflower",
  "Yogurt",
  "Cheesecake",
  "Tortilla Chips",
  "Pickle",
  "Blueberry",
  "Jam",
  "Cranberry",
  "<PERSON>cha",
  "Chocolate Chip",
  "Peach",
  "Sea Salt",
  "Green Tea",
  "Berry",
  "Salami",
  "Grapefruit",
  "Black Pepper",
  "Rye",
  "Powdered Sugar",
  "Granola",
  "Pumpkin",
  "Cookies",
  "Rosemary",
  "Peanut Butter",
  "Red Wine",
  "Hummus",
  "Chai",
  "Cold Brew",
  "Pudding",
  "Matcha",
  "Miso",
  "Quinoa",
  "Pear",
  "Hot Sauce",
  "Brown Sugar",
  "Walnut",
  "Fennel",
  "Soy Sauce",
  "Grape",
  "Tiramisu",
  "Pistachio",
  "Beet",
  "Rose",
  "Brussels Sprouts",
  "Oatmeal",
  "Passion Fruit",
  "Watermelon",
  "Mascarpone",
  "Pomegranate",
  "Nutella",
  "Dark Chocolate",
  "Hard Boiled Egg",
  "Pecan",
  "Extra Virgin Olive Oil",
  "White Chocolate",
  "Oat",
  "Yuzu",
  "Plum",
  "Sage",
  "Oreo",
  "Pretzel",
  "Thyme",
  "Hibiscus",
  "Sauerkraut",
  "Turmeric",
  "Fudge",
  "Vanilla Bean",
  "Tahini",
  "Guava",
  "Lavender",
  "Graham Cracker",
  "Blackberry",
  "Skillet",
  "Earl Grey",
  "Dates",
  "Raisin",
  "Lychee",
  "Peppermint",
  "Brown Butter",
  "Kimchi",
  "Salted Caramel",
  "Tamarind",
  "Saffron",
  "Crackers",
  "Fig",
  "Blood Orange",
  "Greek Yogurt",
  "Chia",
  "Candy",
  "Papaya",
  "Lentil",
  "Balsamic Vinegar",
  "Apricot",
  "Acai",
  "Cayenne",
  "Popcorn",
  "Jicama",
  "Whole Grain",
  "Horchata",
  "Mole",
  "Milk Chocolate",
  "Spritz",
  "Tajin",
  "Cacao",
  "Coconut Water",
  "Cardamom",
  "Kiwi",
  "Chocolate Ganache",
  "Key Lime",
  "Cottage Cheese",
  "Kombucha",
  "Boneless Wings",
  "Macadamia",
  "Churro",
  "Wraps",
  "Marshmallow",
  "Dulce De Leche",
  "Toffee",
  "Red Velvet",
  "Honeydew",
  "Black Sesame",
  "Apple Pie",
  "Gochujang",
  "Watercress",
  "Charcuterie",
  "Hemp",
  "Tangerine",
  "Vegan Cheese",
  "Plantain",
  "Cornmeal",
  "Sweet Tea",
  "Fish Sauce",
  "Molasses",
  "Mixed Berry",
  "Black Cherry",
  "Cantaloupe",
  "Thai Iced Tea",
  "Flax",
  "Shortbread",
  "Buckwheat",
  "Butterscotch",
  "Ube",
  "Double Chocolate",
  "Creme Fraiche",
  "Cereal",
  "Charcoal",
  "Rice Cake",
  "Pepita",
  "Cactus",
  "Pina Colada",
  "Praline",
  "Worcestershire",
  "Goji",
  "Lassi",
  "Applesauce",
  "Currant",
  "Malt Vinegar",
  "Prickly Pear",
  "Biscotti",
  "Cookie Dough",
  "Swiss Chard",
  "Lemon Grass",
  "Irish Cream",
  "Soft Pretzel",
  "Sugar Cookie",
  "Vegan Chocolate",
  "Gingerbread",
  "Jackfruit",
  "Mandarin Orange",
  "Chia Pudding",
  "Apple Cider Vinegar",
  "Tempeh",
  "Asian Pear",
  "Persimmon",
  "Queso Dip",
  "Rhubarb",
  "Snickerdoodle",
  "Ume",
  "Black Rice",
  "Macaron",
  "Mexican Chocolate",
  "Kumquat",
  "Five Spice",
  "Coconut Oil",
  "Huckleberry",
  "Quince",
  "Turtle",
  "Cookie Butter",
  "Fruit Smoothie",
  "Green Juice",
  "M&M",
  "Cotton Candy",
  "Aloe Vera",
  "Dried Cranberry",
  "Eggnog",
  "Rice Vinegar",
  "Taquito",
  "Birthday Cake",
  "Pork Rinds",
  "Fried Mozzarella Sticks",
  "Mint Chocolate",
  "Boysenberry",
  "Cultured Butter",
  "Ghee",
  "Golden Milk",
  "Jalapeno Pepper",
  "Rocky Road",
  "Ancient Grain",
  "Green Tea/Matcha",
  "Pomelo",
  "Salted Chocolate",
  "Durian",
  "Elderberry",
  "Kefir",
  "Nappa Cabbage",
  "Cheese Stick",
  "Coconut Butter",
  "Tart Cherry",
  "Golden Beet",
  "Rice Crackers",
  "Soursop",
  "Dosa",
  "Beef Liver",
  "Mangonada",
  "Mulberries",
  "Nut Butter",
  "Protein Shake",
  "Tepache",
  "Coconut Chips",
  "Bourbon/Whiskey",
  "Corn Nuts",
  "Micro Green",
  "Alfajor",
  "Garlic Mustard",
  "Injera",
  "Roasted Chickpeas",
  "Trail Mix",
  "Baby Carrot",
  "Cane Juice",
  "Cold Press",
  "Smore",
  "White Cranberry",
  "Ginger Snap",
  "Kale Chips",
  "Sprouted Grain",
  "Wheat Grass",
  "Bread Fruit",
  "Egg Bites",
  "Marshmallow Treat",
  "Protein Chocolate",
  "Fruit Snacks",
  "Pitaya/Dragonfruit",
  "Baobab",
  "Chilli Pepper",
  "Chocolate Bark",
  "Fruit Bar",
  "Fruit Chips",
  "Kettle Corn",
  "Maqui Berry",
  "Pizza Bites",
  "Plantain Chip",
  "Rum Raisin",
  "Seaweed Chips",
  "Seed Butter",
  "Whiskey/Bourbon",
  "Cannabis",
  "Coffeeberry",
  "Cookie And Cream",
  "Kolaczki",
  "Mint/Peppermint",
  "Protein Chip",
  "Dragon Frui/ Pitaya",
  "Pandan",
  "Pink Cranberry",
];

export const categoryList = ["acids and salts", "cider & progressive adult beverages", "cocktail flavors", "cocktails", "colors", "cookies", "dairy", "dessert flavors", "emulsifiers", "fats and oils", "fermented", "frozen treat flavors", "fruits", "grains", "gums", "health & wellness terms", "healthy: better for you ingredients", "healthy: functional foods", "healthy : superfoods", "herbs & spices", "ice cream flavors", "juice & juice ingredients", "macro trends", "non alcoholic bev flavors", "non alcoholic bev varieties", "non-dairy alternatives", "nuts & seeds", "plant based dairy", "plant based protein alternatives", "preparation methods (app. entree, side)", "proteins", "pulses( beans, lentils & peas)", "salad dressings & oils", "salts", "savory sauces & flavors", "smoothies", "snacks", "starches and fillers", "sugars & sweeteners", "sweeteners", "umami", "vegetables", "water varieties and flavors"]
export const menuAdoptionList = ["emergence", "growth", "mainstream", "mature"]
export const tasteList = ["acidic", "aesthetic", "alcoholic", "aromatic", "austere", "authentic", "bergamot", "beyond taste", "bitter", "botanical", "briny", "bubbly", "buttery", "citrusy", "clean", "compressed", "creamy", "crisp", "crispy", "drive thry only", "dry", "earthy", "fad", "floral", "fresh", "fruity", "functional", "grassy", "herbal", "item as flavor", "last", "lemony", "malty", "mellow", "mild", "mildly bitter", "minty", "n/a", "neutral", "no artificial", "no sugar added", "no taste", "none", "not applicable", "nutrient-dense", "nutty", "old", "peer", "peppery", "personalized", "piney", "pungent", "refreshing", "rich", "robust", "salty", "savory", "sharp", "simple", "slightly smoky", "slightly sour", "smoky", "sober", "sour", "spicy", "starchy", "strong", "sweet", "tangy", "tart", "toasted", "umami", "unknown", "uplifting", "yeasty", "zesty"]
export const aromaList = ["aromatic", "balsamic", "beany", "beefy", "bergamot", "botanical", "bready", "briny", "bubbly", "buttery", "caramelized", "carbonated", "chocolate", "chocolatey", "chocolaty", "cinnamony", "citrusy", "coffee-like", "complex", "creamy", "crisp", "crystalline", "delicate", "earthy", "fermented", "fishy", "flash", "floral", "fluffy", "fragrant", "fresh", "fruity", "functional", "garlicky", "ginger", "glistening", "graham cracker", "grassy", "green", "herbaceous", "herbal", "licorice", "light", "malty", "marshmallow", "marshmallowy", "meaty", "mellow", "mild", "minty", "musky", "n/a", "neutral", "no aroma", "no artificial", "none", "not applicable", "nutty", "oaky", "oceanic", "peppery", "porky", "pungent", "refreshing", "rich", "roasted", "robust", "savory", "sensery driven", "slightly fruity", "slightly sweet", "smoky", "sour", "spicy", "subtle", "sugary", "sweet", "tangy", "tart", "toasted", "tropical", "umami", "unknown", "vegetal", "woodsy", "woody", "zesty"]
export const textureList = ["aromatic", "brittle", "bubbly", "cakey", "carbonated", "charred", "chewy", "coarse", "cotton-like", "creamy", "crisp", "crispy", "crumbly", "crunchy", "crusty", "crystalline", "delicate", "dense", "dried", "effervescent", "fibrous", "fine", "firm", "fizzy", "flaky", "fluffy", "fudgy", "functional", "glazed", "glistening", "glossy", "gooey", "grainy", "granular", "gritty", "gummy", "icy", "juicy", "light", "luscious", "meaty", "mellow", "melting", "moist", "n/a", "no artificial", "none", "not applicable", "oily", "plump", "powdery", "refreshing", "rich", "saucy", "sharp", "silky", "sizzling", "slightly crunchy", "slimy", "slurpy", "smooth", "soft", "spongy", "starchy", "sticky", "succulent", "syrupy", "tangy", "tart", "tender", "textured", "thick", "thin", "trend", "umami", "unknown", "velvety", "vibrant", "viscous", "watery", "zesty"]
export const appearanceList = ["aromatic", "brittle", "bubbly", "cakey", "carbonated", "charred", "chewy", "coarse", "cotton-like", "creamy", "crisp", "crispy", "crumbly", "crunchy", "crusty", "crystalline", "delicate", "dense", "dried", "effervescent", "fibrous", "fine", "firm", "fizzy", "flaky", "fluffy", "fudgy", "functional", "glazed", "glistening", "glossy", "gooey", "grainy", "granular", "gritty", "gummy", "icy", "juicy", "light", "luscious", "meaty", "mellow", "melting", "moist", "n/a", "no artificial", "none", "not applicable", "oily", "plump", "powdery", "refreshing", "rich", "saucy", "sharp", "silky", "sizzling", "slightly crunchy", "slimy", "slurpy", "smooth", "soft", "spongy", "starchy", "sticky", "succulent", "syrupy", "tangy", "tart", "tender", "textured", "thick", "thin", "trend", "umami", "unknown", "velvety", "vibrant", "viscous", "watery", "zesty"]

export const ingredientQuestions: IngredientQuestion[] = [
  { question: "Give me a social media analysis on {ingredient}" },
  { question: "Give me a retail analysis on {ingredient}" },
  { question: "Give me a food service analysis on {ingredient}" },
];

export const defaultQuestions: IngredientQuestion[] = [
  { question: "What are the fastest growing fruit flavors in foodservice?" },
  { question: "What are social media trend around snacking?" },
  {
    question:
      "Compare functional sodas like Olipop vs Poppi on averge price and retailers they are present in.",
  },
];

export const analyzeQuestions: IngredientQuestion[] = [
  { question: "Analyze the fastest growing fruit flavors in foodservice" },
  { question: "Analyze social media trends around snacking" },
  {
    question:
      "Analyze which limited time offers are utilizing spicy flavors on menus",
  },
];

export const compareQuestions: IngredientQuestion[] = [
  {
    question:
      "Compare functional sodas like Olipop vs Poppi on average price and retailers they are present in",
  },
  {
    question:
      "Compare the popularity of oatmilk in beverages vs desserts on menus",
  },
  {
    question: "Compare top gluten free and vegan desserts",
  },
];

export const pricingQuestions: IngredientQuestion[] = [
  {
    question:
      "Which U.S. city has the highest average price for pepperoni pizza?",
  },
  {
    question:
      "What is the average price of a mocktail in Miami, Los Angeles and Las Vegas?",
  },
  { question: "What is the most expensive dessert menu item on menus?" },
];

export const salesQuestions: IngredientQuestion[] = [
  {
    question:
      "What are restaurants with yuzu on the menu in Miami with price over $12?",
  },
  {
    question:
      "What are restaurants with oatmilk on the menu in San Francisco with price over $4?",
  },
];

export const innovateQuestions: IngredientQuestion[] = [
  {
    question:
      "Imagine I'm in R&D coming up with new product ideas using pineapple. Currently I make a lot of frozen snacks. What areas, brands and categories should I be thinking about?",
  },
  {
    question:
      "Identify whitespace opportunities for functional ingredients in beverages",
  },
  {
    question:
      "Identify whitespace innovation opportunities for Asian flavors in snacking",
  },
];

export function getQuestions(searchTerm: string): IngredientQuestion[] {
  if (!searchTerm.trim()) {
    return defaultQuestions;
  }

  const normalizedSearch = searchTerm.toLowerCase().trim();

  // First try to find a match for the entire search term
  const fullMatch = ingredientList.find(
    (ingredient) => ingredient.toLowerCase() === normalizedSearch,
  );

  if (fullMatch) {
    const questions = ingredientQuestions.map((q) => ({
      ...q,
      question: q.question.replace("{ingredient}", fullMatch),
    }));
    return questions;
  }

  // If no full match, try individual words
  const words = normalizedSearch.split(/\s+/);
  const matchingIngredient = words.find((word) => {
    const match = ingredientList.some(
      (ingredient) => ingredient.toLowerCase() === word.toLowerCase(),
    );
    return match;
  });

  if (matchingIngredient) {
    const questions = ingredientQuestions.map((q) => ({
      ...q,
      question: q.question.replace("{ingredient}", matchingIngredient),
    }));
    return questions;
  }
  return defaultQuestions;
}

export const INGREDIENTS_MENU = [
  {
    slug: "/ingredients/industry-insights",
    title: "Industry Insights",
  },
  {
    slug: "/ingredients/consumer-insights",
    title: "Consumer Insights",
  },
  {
    slug: "/ingredients/retail-insights",
    title: "Retail Insights",
  },
  {
    slug: "/ingredients/all-ingredients",
    title: "All Ingredients",
  },
];

function listToFilterOptions(key: string, list: string[]) {
  return list.map((item) => ({
    label: item,
    value: `${key}-${item}`,
  }));
}

export const INGREDIENTS_FILTER = [
  {
    label: (
      <>
        <IconStar size={20} className="text-neutral-500" /> Favorite
      </>
    ),
    filters: [
      { label: "Yes", value: "favorite-true" },
      { label: "No", value: "favorite-false" },
    ],
  },
  {
    label: (
      <>
        <IconWorkflow size={20} className="text-neutral-500" /> Category
      </>
    ),
    filters: listToFilterOptions('category', categoryList),
  },
  {
    label: (
      <>
        <IconIncrease size={20} className="text-neutral-500" /> Menu Adoption
      </>
    ),
    filters: listToFilterOptions('menu_adoption', menuAdoptionList),
  },
  {
    label: (
      <>
        <IconAroma size={20} className="text-neutral-500" /> Aroma
      </>
    ),
    filters: listToFilterOptions('aroma', aromaList),
  },
  {
    label: (
      <>
        <IconCircleFilled size={20} className="text-neutral-500" /> Texture
      </>
    ),
    filters: listToFilterOptions('texture', textureList),
  },
  {
    label: (
      <>
        <IconTaste2 size={20} className="text-neutral-500" /> Taste
      </>
    ),
    filters: listToFilterOptions('taste', tasteList),
  },
  {
    label: (
      <>
        <IconAppearance size={20} className="text-neutral-500" /> Appearance
      </>
    ),
    filters: listToFilterOptions('appearance', appearanceList),
  },
];

export const INGREDIENTS_SORT = [
  { label: "Name", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "name-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "name-lowest",
  },
  { label: "", value: "divider" },
  { label: "Category", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "menu-category-name-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "menu-category-name-lowest",
  },
  { label: "", value: "divider" },
  { label: "Penetration", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "penetration-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "penetration-lowest",
  },
  { label: "", value: "divider" },
  { label: "Social Mentions", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "social-mentions-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "social-mentions-lowest",
  },
  { label: "", value: "divider" },
  { label: "Foodservice Growth", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "foodservice-growth-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "foodservice-growth-lowest",
  },
  { label: "", value: "divider" },
  { label: "Retail Growth", value: "heading" },
  {
    label: (
      <>
        <IconCircleUp size={20} className="text-neutral-500" /> Highest
      </>
    ),
    value: "retail-change-highest",
  },
  {
    label: (
      <>
        <IconCircleDown size={20} className="text-neutral-500" /> Lowest
      </>
    ),
    value: "retail-change-lowest",
  },
];

export const INGREDIENTS_SORT_TRANSFORM: Record<string, { id: string; desc: boolean }[]> = {
  "penetration-highest" : [{id: "pen_rate", desc: true}],
  "penetration-lowest" : [{id: "pen_rate", desc: false}],
  "social-mentions-highest" : [{id: "social_mentions", desc: true}],
  "social-mentions-lowest" : [{id: "social_mentions", desc: false}],
  "foodservice-growth-highest" : [{id: "change_1y", desc: true}],
  "foodservice-growth-lowest" : [{id: "change_1y", desc: false}],
  "retail-growth-highest" : [{id: "retail_change", desc: true}],
  "retail-growth-lowest": [{id: "retail_change", desc: false}],
}

export const MENU_ADOPTION_CURVE_DROPDOWN = [
  {
    label: (
      <>
        <IconFullscreen size={20} className="text-neutral-500" /> View in full
        screen
      </>
    ),
    value: "fullscreen",
  },
  {
    label: (
      <>
        <IconPrint size={20} className="text-neutral-500" /> Print chart
      </>
    ),
    value: "print-chart",
  },
  {
    label: "",
    value: "divider",
  },
  {
    label: "Download Formats",
    value: "heading",
  },
  {
    label: (
      <>
        <IconPNG size={20} className="text-neutral-500" /> PNG
      </>
    ),
    value: "png",
  },
  {
    label: (
      <>
        <IconImage size={20} className="text-neutral-500" /> JPG
      </>
    ),
    value: "jpg",
  },
  // { TODO: do we need this?
  //   label: (
  //     <>
  //       <IconEdit size={20} className="text-neutral-500" /> SVG
  //     </>
  //   ),
  //   value: "svg",
  // },
  {
    label: (
      <>
        <IconDocument size={20} className="text-neutral-500" /> CSV
      </>
    ),
    value: "csv",
  },
  {
    label: (
      <>
        <IconXLS size={20} className="text-neutral-500" /> XLS
      </>
    ),
    value: "xls",
  },
];

export const getIngredientDetailsMenu = (ingredientSlug: string) => [
  {
    slug: `/ingredients/details/${ingredientSlug}`,
    title: "General",
  },
  {
    slug: `/ingredients/details/${ingredientSlug}/consumer-insights`,
    title: "Consumer Insights",
  },
  {
    slug: `/ingredients/details/${ingredientSlug}/retail`,
    title: "Retail",
  },
  {
    slug: `/ingredients/details/${ingredientSlug}/on-menus`,
    title: "On Menus",
  },
];
