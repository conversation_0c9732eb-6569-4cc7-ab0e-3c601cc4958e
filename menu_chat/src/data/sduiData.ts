// Example SDUI data structure
export const sduiData = {
  "components": [
    {
      "component_id": "1",
      "component_type": "text",
      "typography": {
        "value": "TITLE"
      },
      "content": {
        "value": "Food Statistics in New York.",
       
      }
    },
    {
      "component_id": "2",
      "component_type": "text",
      "content": {
        "value": "USA, known for its culinary diversity, offers a wide range of dining options, from traditional Portuguese cuisine to international flavors. Below are some key statistics and insights about restaurants in New York:",
        "typography": {
          "value": "BODY"
        }
      }
    },
    {
      "component_id": "3",
      "component_type": "link",
      "content": {
        "beforeContentIcon": "IconLocation",
        "label": "Find on Google Maps",
        "afterContentIcon": "IconCircleUpright",
        "url": "https://maps.google.com/?q=New+York+restaurants"
      }
    },
    {
      "component_id": "4",
      "component_type": "horizontal-container",
      "content": {
        "componentList": [
          {
            "component_id": "card-1",
            "component_type": "card",
            "content": {
              "imgURL": "/assets/images/<EMAIL>",
              "title": "Crispy Bok Choy",
              "subTitle": "$12",
              "description": "Fried bok choy served with potatoes."
            }
          },
          {
            "component_id": "card-2",
            "component_type": "card",
            "content": {
              "imgURL": "/assets/images/<EMAIL>",
              "title": "Apple Pie",
              "subTitle": "$18",
              "description": "Apple pie dessert with vanilla ice cream."
            }
          },
          {
            "component_id": "card-3",
            "component_type": "card",
            "content": {
              "imgURL": "/assets/images/<EMAIL>",
              "title": "Wagyu Steak",
              "subTitle": "$35",
              "description": "Prepared on BBQ and served with potato."
            }
          }
        ]
      }
    },
    {
      "component_id": "5",
      "component_type": "link",
      "content": {
        "beforeContentIcon": "IconMenuAdoption",
        "label": "See full restaurant menu",
        "afterContentIcon": "IconCircleUpright",
        "url": "https://example.com/restaurant-menu"
      }
    }
  ]
};

// Mock API function that simulates fetching SDUI data
export const fetchSduiData = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Return the mock data
  return { data: sduiData };
}; 