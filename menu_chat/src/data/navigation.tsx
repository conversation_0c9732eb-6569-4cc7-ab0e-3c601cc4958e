import {
  IconHome,
  IconIngredients,
  IconIngredientsSolid,
  IconMenu,
  IconMenuSolid,
  IconReport,
  IconReportSolid,
  IconRetail,
  IconRetailSolid,
  // IconSettings,
  IconLogout,
  IconHomeSolid,
} from "@/components/icons";

import { signOut } from "next-auth/react";

export const NAV_TOP = [
  {
    label: "Home",
    icon: <IconHome size={20} />,
    iconSolid: <IconHomeSolid size={20} />,
    link: "/",
  },
  {
    label: "Ingredients",
    icon: <IconIngredients size={20} />,
    iconSolid: <IconIngredientsSolid size={20} />,
    link: "/ingredients/industry-insights",
  },
  {
    label: "Menu Items",
    icon: <IconMenu size={20} />,
    iconSolid: <IconMenuSolid size={20} />,
    link: "/menu-items/industry-insights",
  },
  {
    label: "Retail",
    icon: <IconRetail size={20} />,
    iconSolid: <IconRetailSolid size={20} />,
    link: "/retail/industry-insights",
  },
  {
    label: "divider",
  },
  {
    label: "Conversations & Reports",
    icon: <IconReport size={20} />,
    iconSolid: <IconReportSolid size={20} />,
    link: "/conversations-reports",
  },
];

export const NAV_BOTTOM = [
  // {
  //   label: "Settings",
  //   icon: <IconSettings size={20} />,
  //   link: "/settings",
  // },
  {
    label: "Logout",
    icon: <IconLogout size={20} />,
    onClick: () => {
      signOut({ callbackUrl: "/login" });
    },
  },
];
