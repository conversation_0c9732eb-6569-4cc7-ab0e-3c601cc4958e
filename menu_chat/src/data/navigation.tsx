import {
  IconBinoculars,
  IconBinocularsSolid,
  IconIngredients,
  IconIngredientsSolid,
  IconMenu,
  IconMenuSolid,
  IconReport,
  IconReportSolid,
  IconRetail,
  IconRetailSolid,
  IconSettings,
  IconLogout,
} from "@/components/icons";

import { signOut } from "next-auth/react";

export const NAV_TOP = [
  {
    label: "Home",
    icon: <IconBinoculars size={20} />,
    iconSolid: <IconBinocularsSolid size={20} />,
    link: "/",
  },
  {
    label: "Ingredients",
    icon: <IconIngredients size={20} />,
    iconSolid: <IconIngredientsSolid size={20} />,
    link: "/ingredients/industry-insights",
  },
  {
    label: "Menu Items",
    icon: <IconMenu size={20} />,
    iconSolid: <IconMenuSolid size={20} />,
    link: "/menu-items/industry-insights",
  },
  {
    label: "Retail",
    icon: <IconRetail size={20} />,
    iconSolid: <IconRetailSolid size={20} />,
    link: "/retail/industry-insights",
  },
  {
    label: "divider",
  },
  {
    label: "Previous Reports",
    icon: <IconReport size={20} />,
    iconSolid: <IconReportSolid size={20} />,
    link: "previous-reports",
  },
];

export const NAV_BOTTOM = [
  {
    label: "Settings",
    icon: <IconSettings size={20} />,
    link: "/settings",
  },
  {
    label: "Logout",
    icon: <IconLogout size={20} />,
    onClick: () => {
      signOut({ callbackUrl: "/login" });
    },
  },
];
