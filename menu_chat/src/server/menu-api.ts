import axios from "axios";
const MENU_BASE_URL: string =
  process.env.MENU_BASE_URL ||
  "https://staging-menu-data.herokuapp.com/menu_api/v1";

export async function signInToMenu(email: string, password: string) {
  try {
    const response = await axios.post(`${MENU_BASE_URL}/users/sign_in`, {
      user: {
        email: email,
        password: password,
      },
    });
    return response;
  } catch (error) {
    console.log(error);
  }
}
